'use client';

import { 
  EnvelopeIcon,
  GlobeAltIcon,
  MapPinIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  StarIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import Image from 'next/image';

interface PortfolioData {
  freelancer: {
    displayName: string;
    title: string;
    bio: string;
    email: string;
    location: string;
    website: string;
    skills: string[];
  };
  projects: any[];
  customization: {
    primaryColor: string;
    secondaryColor: string;
    showContactInfo: boolean;
    showProjectDetails: boolean;
    showClientTestimonials: boolean;
    showSkills: boolean;
    showStats: boolean;
  };
  stats: {
    totalProjects: number;
    completedProjects: number;
    happyClients: number;
    yearsExperience: number;
  };
}

interface ProfessionalTemplateProps {
  data: PortfolioData;
}

export default function ProfessionalTemplate({ data }: ProfessionalTemplateProps) {
  const { freelancer, projects, customization, stats } = data;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Professional Header */}
      <div className="bg-white shadow-sm border-b-2" style={{ borderColor: customization.primaryColor }}>
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8">
            <div 
              className="w-32 h-32 rounded-lg flex items-center justify-center text-white font-bold text-2xl"
              style={{ backgroundColor: customization.primaryColor }}
            >
              {freelancer.displayName.split(' ').map(n => n[0]).join('')}
            </div>
            
            <div className="flex-1 text-center lg:text-left">
              <h1 className="text-4xl font-bold text-gray-900 mb-2">{freelancer.displayName}</h1>
              <p className="text-xl text-gray-600 mb-4">{freelancer.title}</p>
              <p className="text-gray-700 max-w-3xl mb-6">{freelancer.bio}</p>
              
              {customization.showContactInfo && (
                <div className="flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-gray-600">
                  <div className="flex items-center">
                    <EnvelopeIcon className="h-4 w-4 mr-2" />
                    <a href={`mailto:${freelancer.email}`} className="hover:text-gray-900">
                      {freelancer.email}
                    </a>
                  </div>
                  <div className="flex items-center">
                    <MapPinIcon className="h-4 w-4 mr-2" />
                    {freelancer.location}
                  </div>
                  <div className="flex items-center">
                    <GlobeAltIcon className="h-4 w-4 mr-2" />
                    <a href={freelancer.website} target="_blank" rel="noopener noreferrer" className="hover:text-gray-900">
                      Portfolio Website
                    </a>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Professional Stats */}
        {customization.showStats && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Professional Summary</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold mb-2" style={{ color: customization.primaryColor }}>
                  {stats.totalProjects}
                </div>
                <div className="text-sm text-gray-600 uppercase tracking-wide">Total Projects</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-2" style={{ color: customization.primaryColor }}>
                  {stats.completedProjects}
                </div>
                <div className="text-sm text-gray-600 uppercase tracking-wide">Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-2" style={{ color: customization.primaryColor }}>
                  {stats.happyClients}
                </div>
                <div className="text-sm text-gray-600 uppercase tracking-wide">Satisfied Clients</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-2" style={{ color: customization.primaryColor }}>
                  {stats.yearsExperience}
                </div>
                <div className="text-sm text-gray-600 uppercase tracking-wide">Years Experience</div>
              </div>
            </div>
          </div>
        )}

        {/* Core Competencies */}
        {customization.showSkills && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Core Competencies</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {freelancer.skills.map((skill, index) => (
                <div
                  key={index}
                  className="px-4 py-3 border rounded-lg text-center font-medium"
                  style={{ 
                    borderColor: customization.primaryColor,
                    color: customization.primaryColor,
                    backgroundColor: `${customization.primaryColor}08`
                  }}
                >
                  {skill}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Project Portfolio */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">Project Portfolio</h2>
          
          <div className="space-y-8">
            {projects.map((project, index) => (
              <div key={project.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-0">
                  {/* Project Image */}
                  {project.images && project.images.length > 0 && (
                    <div className="relative h-64 lg:h-auto">
                      <Image
                        src={project.images[0]}
                        alt={project.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}

                  {/* Project Details */}
                  <div className={`p-8 ${project.images?.length ? 'lg:col-span-2' : 'lg:col-span-3'}`}>
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-2">{project.name}</h3>
                        <div className="flex items-center text-gray-600 mb-2">
                          <BuildingOfficeIcon className="h-4 w-4 mr-2" />
                          <span className="font-medium">{project.client_name}</span>
                        </div>
                      </div>
                      <span 
                        className="px-3 py-1 text-sm font-semibold rounded-full text-white"
                        style={{ backgroundColor: customization.primaryColor }}
                      >
                        {project.status}
                      </span>
                    </div>

                    <p className="text-gray-700 mb-6">{project.description}</p>

                    {customization.showProjectDetails && (
                      <>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                          <div>
                            <h4 className="text-sm font-semibold text-gray-900 mb-2 uppercase tracking-wide">Project Timeline</h4>
                            <div className="flex items-center text-gray-600">
                              <CalendarIcon className="h-4 w-4 mr-2" />
                              {formatDate(project.start_date)} - {formatDate(project.end_date)}
                            </div>
                          </div>
                          <div>
                            <h4 className="text-sm font-semibold text-gray-900 mb-2 uppercase tracking-wide">Project Value</h4>
                            <div className="flex items-center text-gray-600">
                              <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                              {formatCurrency(project.budget)}
                            </div>
                          </div>
                        </div>

                        {/* Technologies */}
                        <div className="mb-6">
                          <h4 className="text-sm font-semibold text-gray-900 mb-3 uppercase tracking-wide">Technologies Used</h4>
                          <div className="flex flex-wrap gap-2">
                            {project.technologies?.map((tech: string, techIndex: number) => (
                              <span
                                key={techIndex}
                                className="px-3 py-1 text-xs font-medium rounded border"
                                style={{ 
                                  borderColor: customization.secondaryColor,
                                  color: customization.secondaryColor,
                                  backgroundColor: `${customization.secondaryColor}08`
                                }}
                              >
                                {tech}
                              </span>
                            ))}
                          </div>
                        </div>

                        {/* Key Deliverables */}
                        <div className="mb-6">
                          <h4 className="text-sm font-semibold text-gray-900 mb-3 uppercase tracking-wide">Key Deliverables</h4>
                          <ul className="text-sm text-gray-700 space-y-2">
                            {project.highlights?.map((highlight: string, highlightIndex: number) => (
                              <li key={highlightIndex} className="flex items-start">
                                <div 
                                  className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0"
                                  style={{ backgroundColor: customization.primaryColor }}
                                ></div>
                                {highlight}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Client Testimonial */}
                    {customization.showClientTestimonials && project.testimonial && (
                      <div className="border-l-4 pl-6" style={{ borderColor: customization.primaryColor }}>
                        <div className="flex items-start">
                          <div className="flex text-yellow-400 mr-3 mt-1">
                            {[...Array(5)].map((_, i) => (
                              <StarIcon key={i} className="h-4 w-4 fill-current" />
                            ))}
                          </div>
                          <div className="flex-1">
                            <p className="text-gray-700 italic mb-3">"{project.testimonial.text}"</p>
                            <div className="text-sm">
                              <p className="font-semibold text-gray-900">{project.testimonial.author}</p>
                              <p className="text-gray-600">{project.testimonial.position}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Professional Contact */}
        {customization.showContactInfo && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Let's Discuss Your Project</h2>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              I'm committed to delivering exceptional results and building long-term professional relationships. 
              Contact me to discuss how I can help achieve your business objectives.
            </p>
            <a
              href={`mailto:${freelancer.email}?subject=Professional Inquiry&body=Dear ${freelancer.displayName}, I would like to discuss a potential business opportunity.`}
              className="inline-flex items-center px-8 py-3 font-semibold rounded-lg text-white transition-colors"
              style={{ backgroundColor: customization.primaryColor }}
            >
              <EnvelopeIcon className="h-5 w-5 mr-2" />
              Schedule Consultation
            </a>
          </div>
        )}
      </div>

      {/* Professional Footer */}
      <footer className="bg-white border-t border-gray-200 py-6">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-500 text-sm">
            Professional portfolio powered by <span className="font-semibold" style={{ color: customization.primaryColor }}>KaiNote</span>
          </p>
        </div>
      </footer>
    </div>
  );
}
