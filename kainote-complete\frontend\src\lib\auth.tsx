'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';

interface User {
  id: string;
  email: string;
  name: string;
  subscription_tier: string;
  hourly_rate?: number;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (email: string, password: string, name: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Check for existing token on mount
    const token = localStorage.getItem('token');
    if (token) {
      // For demo purposes, set a demo user
      setUser({
        id: 'demo-user-id',
        email: '<EMAIL>',
        name: 'Demo User',
        subscription_tier: 'pro',
        hourly_rate: 75,
      });
    }
    setLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      
      // For demo purposes, accept any email/password
      if (email && password) {
        const token = 'demo-token';
        localStorage.setItem('token', token);
        
        setUser({
          id: 'demo-user-id',
          email: email,
          name: 'Demo User',
          subscription_tier: 'pro',
          hourly_rate: 75,
        });
        
        toast.success('Login successful!');
        router.push('/dashboard');
      } else {
        throw new Error('Email and password are required');
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Login failed. Please try again.');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (email: string, password: string, name: string) => {
    try {
      setLoading(true);
      
      // For demo purposes, accept any registration
      if (email && password && name) {
        const token = 'demo-token';
        localStorage.setItem('token', token);
        
        setUser({
          id: 'demo-user-id',
          email: email,
          name: name,
          subscription_tier: 'pro',
          hourly_rate: 75,
        });
        
        toast.success('Registration successful!');
        router.push('/dashboard');
      } else {
        throw new Error('All fields are required');
      }
    } catch (error) {
      console.error('Registration error:', error);
      toast.error('Registration failed. Please try again.');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    setUser(null);
    toast.success('Logged out successfully');
    router.push('/');
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, logout, register }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
