import express, { Request, Response, NextFunction } from 'express';
import { stripeConnect, stripeConnectConfig, kaiNoteSubscriptionPlans } from '../config/stripe-connect';

const router = express.Router();

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    userId: string;
    email: string;
    name: string;
    stripeAccountId?: string;
    kaiNoteSubscription?: string;
  };
}

// Demo auth middleware
const demoAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  req.user = {
    id: 'demo-user-id',
    userId: 'demo-user-id',
    email: '<EMAIL>',
    name: 'Demo Freelancer',
    stripeAccountId: 'acct_demo_123', // Demo connected account
    kaiNoteSubscription: 'professional'
  };
  next();
};

// Get freelancer's Stripe Connect status
router.get('/stripe-status', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;
    
    if (!user.stripeAccountId) {
      return res.json({
        success: true,
        data: {
          connected: false,
          accountId: null,
          onboardingRequired: true,
          paymentsEnabled: false,
          detailsSubmitted: false
        }
      });
    }

    // In demo mode, return mock data
    const demoAccountStatus = {
      connected: true,
      accountId: user.stripeAccountId,
      onboardingRequired: false,
      paymentsEnabled: true,
      detailsSubmitted: true,
      country: 'US',
      currency: 'usd',
      businessType: 'individual',
      email: user.email,
      displayName: user.name,
      dashboardUrl: '#demo-dashboard'
    };

    res.json({
      success: true,
      data: demoAccountStatus
    });

  } catch (error: any) {
    console.error('Error getting Stripe status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get Stripe status',
      details: error.message
    });
  }
});

// Create Stripe Connect account
router.post('/stripe-connect', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;
    const { country = 'US', businessType = 'individual' } = req.body;

    // In demo mode, simulate account creation
    const demoAccountId = 'acct_demo_' + Date.now();
    
    // In production, you would:
    // const account = await stripeConnect.createExpressAccount({
    //   email: user.email,
    //   country,
    //   businessType
    // });
    // 
    // const accountLink = await stripeConnect.createAccountLink(
    //   account.id,
    //   `${process.env.FRONTEND_URL}/integrations/stripe/refresh`,
    //   `${process.env.FRONTEND_URL}/integrations/stripe/success`
    // );

    const demoOnboardingUrl = `${process.env.FRONTEND_URL}/integrations/stripe/demo-onboarding?account=${demoAccountId}`;

    res.json({
      success: true,
      data: {
        accountId: demoAccountId,
        onboardingUrl: demoOnboardingUrl
      }
    });

  } catch (error: any) {
    console.error('Error creating Stripe Connect account:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create Stripe account',
      details: error.message
    });
  }
});

// Create client invoice with payment option
router.post('/create-invoice', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;
    const {
      clientId,
      projectId,
      amount,
      currency = 'usd',
      description,
      dueDate,
      items,
      enableOnlinePayment = false
    } = req.body;

    // Generate invoice
    const invoice = {
      id: 'inv_' + Date.now(),
      number: 'INV-' + new Date().getFullYear() + '-' + String(Date.now()).slice(-6),
      freelancerId: user.id,
      clientId,
      projectId,
      amount,
      currency,
      description,
      dueDate,
      items,
      status: 'draft',
      createdAt: new Date().toISOString(),
      enableOnlinePayment,
      paymentUrl: null,
      stripeAccountId: enableOnlinePayment ? user.stripeAccountId : null
    };

    // If online payment is enabled and freelancer has Stripe connected
    if (enableOnlinePayment && user.stripeAccountId) {
      // In production, create Stripe checkout session
      // const session = await stripeConnect.createCheckoutSession(
      //   amount * 100, // Convert to cents
      //   currency,
      //   user.stripeAccountId,
      //   `${process.env.FRONTEND_URL}/invoice/${invoice.id}/payment-success`,
      //   `${process.env.FRONTEND_URL}/invoice/${invoice.id}/payment-cancel`,
      //   0, // No application fee for demo
      //   { invoiceId: invoice.id, freelancerId: user.id }
      // );
      
      // Demo payment URL
      invoice.paymentUrl = `${process.env.FRONTEND_URL}/invoice/${invoice.id}/pay`;
    }

    res.json({
      success: true,
      data: invoice
    });

  } catch (error: any) {
    console.error('Error creating invoice:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create invoice',
      details: error.message
    });
  }
});

// Get freelancer's invoices
router.get('/invoices', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;
    const { status, clientId, limit = 50 } = req.query;

    // Demo invoices
    const demoInvoices = [
      {
        id: 'inv_001',
        number: 'INV-2024-001',
        clientId: 'client_001',
        clientName: 'Acme Corp',
        projectId: 'proj_001',
        projectName: 'Website Redesign',
        amount: 2500,
        currency: 'usd',
        status: 'paid',
        enableOnlinePayment: true,
        paymentUrl: `${process.env.FRONTEND_URL}/invoice/inv_001/pay`,
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        paidAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
        dueDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'inv_002',
        number: 'INV-2024-002',
        clientId: 'client_002',
        clientName: 'Tech Startup Inc',
        projectId: 'proj_002',
        projectName: 'Mobile App Development',
        amount: 5000,
        currency: 'usd',
        status: 'sent',
        enableOnlinePayment: true,
        paymentUrl: `${process.env.FRONTEND_URL}/invoice/inv_002/pay`,
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'inv_003',
        number: 'INV-2024-003',
        clientId: 'client_001',
        clientName: 'Acme Corp',
        projectId: 'proj_003',
        projectName: 'SEO Optimization',
        amount: 1200,
        currency: 'usd',
        status: 'draft',
        enableOnlinePayment: false,
        paymentUrl: null,
        createdAt: new Date().toISOString(),
        dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString()
      }
    ];

    // Filter by status if provided
    let filteredInvoices = demoInvoices;
    if (status) {
      filteredInvoices = demoInvoices.filter(inv => inv.status === status);
    }
    if (clientId) {
      filteredInvoices = filteredInvoices.filter(inv => inv.clientId === clientId);
    }

    res.json({
      success: true,
      data: filteredInvoices.slice(0, Number(limit))
    });

  } catch (error: any) {
    console.error('Error getting invoices:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get invoices',
      details: error.message
    });
  }
});

// Get KaiNote subscription plans
router.get('/kainote-plans', (req: Request, res: Response) => {
  res.json({
    success: true,
    data: kaiNoteSubscriptionPlans
  });
});

// Get freelancer's KaiNote subscription
router.get('/kainote-subscription', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;

    // Demo subscription data
    const demoSubscription = {
      id: 'sub_kainote_123',
      plan: kaiNoteSubscriptionPlans.professional,
      status: 'active',
      currentPeriodStart: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      currentPeriodEnd: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
      cancelAtPeriodEnd: false,
      usage: {
        meetings: 45,
        clients: 8,
        storage: '15.2 GB'
      }
    };

    res.json({
      success: true,
      data: demoSubscription
    });

  } catch (error: any) {
    console.error('Error getting KaiNote subscription:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get subscription',
      details: error.message
    });
  }
});

// Get earnings summary
router.get('/earnings', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;
    const { period = 'month' } = req.query;

    // Demo earnings data
    const demoEarnings = {
      totalEarnings: 12500,
      paidInvoices: 8750,
      pendingInvoices: 3750,
      overdueInvoices: 0,
      currency: 'usd',
      period: period,
      breakdown: {
        thisMonth: 3750,
        lastMonth: 4500,
        thisYear: 12500,
        lastYear: 8900
      },
      recentPayments: [
        {
          invoiceId: 'inv_001',
          amount: 2500,
          currency: 'usd',
          paidAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
          clientName: 'Acme Corp'
        }
      ]
    };

    res.json({
      success: true,
      data: demoEarnings
    });

  } catch (error: any) {
    console.error('Error getting earnings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get earnings',
      details: error.message
    });
  }
});

// Client payment routes (public, no auth required)

// Get invoice for client payment (public)
router.get('/invoice/:invoiceId/payment-info', async (req: Request, res: Response) => {
  try {
    const { invoiceId } = req.params;

    // Demo invoice data for client payment
    const demoInvoice = {
      id: invoiceId,
      number: 'INV-2024-002',
      freelancerName: 'Demo Freelancer',
      freelancerEmail: '<EMAIL>',
      clientName: 'Tech Startup Inc',
      projectName: 'Mobile App Development',
      amount: 5000,
      currency: 'usd',
      description: 'Mobile app development services for Q4 2024',
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'sent',
      enableOnlinePayment: true,
      items: [
        {
          description: 'UI/UX Design',
          quantity: 1,
          rate: 2000,
          amount: 2000
        },
        {
          description: 'Frontend Development',
          quantity: 1,
          rate: 2000,
          amount: 2000
        },
        {
          description: 'Backend Integration',
          quantity: 1,
          rate: 1000,
          amount: 1000
        }
      ]
    };

    res.json({
      success: true,
      data: demoInvoice
    });

  } catch (error: any) {
    console.error('Error getting invoice for payment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get invoice',
      details: error.message
    });
  }
});

// Create payment session for client
router.post('/invoice/:invoiceId/create-payment', async (req: Request, res: Response) => {
  try {
    const { invoiceId } = req.params;
    const { successUrl, cancelUrl } = req.body;

    // In production, you would:
    // 1. Get invoice from database
    // 2. Get freelancer's Stripe account ID
    // 3. Create Stripe checkout session
    // 4. Return session URL

    // Demo payment session
    const demoPaymentSession = {
      sessionId: 'cs_demo_' + Date.now(),
      paymentUrl: `${process.env.FRONTEND_URL}/invoice/${invoiceId}/demo-payment`,
      invoiceId: invoiceId
    };

    res.json({
      success: true,
      data: demoPaymentSession
    });

  } catch (error: any) {
    console.error('Error creating payment session:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create payment session',
      details: error.message
    });
  }
});

export default router;
