# KaiNote Setup Guide

Complete step-by-step setup instructions for developers to compile and run KaiNote.

## 📋 Prerequisites

### Required Software
- **Node.js 18+** - [Download here](https://nodejs.org/)
- **npm 8+** or **yarn 1.22+** - Package manager
- **Git** - Version control
- **PostgreSQL 14+** - Database (or Supabase account)

### Required API Keys
- **OpenAI API Key** - For transcription and AI features
- **Stripe Account** - For payment processing (optional)
- **Supabase Account** - For database hosting (optional)

## 🚀 Quick Start (5 minutes)

### 1. Clone the Repository
```bash
git clone https://github.com/charo360/Kai.git
cd Kai
```

### 2. Automated Setup
```bash
# Make setup script executable
chmod +x setup.sh

# Run automated setup
./setup.sh
```

### 3. Configure Environment Variables
```bash
# Frontend environment
cp frontend/.env.example frontend/.env.local

# Backend environment
cp backend/.env.example backend/.env
```

### 4. Update API Keys
Edit `backend/.env`:
```bash
OPENAI_API_KEY=your_openai_api_key_here
DATABASE_URL=postgresql://user:pass@localhost:5432/kainote
```

### 5. Start Development Servers
```bash
# Start both frontend and backend
npm run dev

# Or start individually
npm run dev:frontend  # http://localhost:3001
npm run dev:backend   # http://localhost:3003
```

## 📖 Detailed Setup Instructions

### Step 1: Environment Setup

#### Install Node.js
```bash
# Check Node.js version
node --version  # Should be 18+
npm --version   # Should be 8+
```

#### Install Dependencies
```bash
# Install root dependencies
npm install

# Install frontend dependencies
cd frontend && npm install

# Install backend dependencies
cd ../backend && npm install
```

### Step 2: Database Setup

#### Option A: Local PostgreSQL
```bash
# Install PostgreSQL
# macOS
brew install postgresql
brew services start postgresql

# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib
sudo systemctl start postgresql

# Create database
createdb kainote

# Run migrations
cd backend
npm run db:migrate
npm run db:seed
```

#### Option B: Supabase (Recommended)
1. Create account at [supabase.com](https://supabase.com)
2. Create new project
3. Copy database URL from Settings > Database
4. Update `DATABASE_URL` in `backend/.env`

### Step 3: API Configuration

#### OpenAI API Setup
1. Create account at [platform.openai.com](https://platform.openai.com)
2. Generate API key
3. Add to `backend/.env`:
```bash
OPENAI_API_KEY=sk-your-key-here
```

#### Stripe Setup (Optional)
1. Create account at [stripe.com](https://stripe.com)
2. Get API keys from Dashboard
3. Add to `backend/.env`:
```bash
STRIPE_SECRET_KEY=sk_test_your-key-here
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
```

### Step 4: Environment Configuration

#### Frontend Configuration (`frontend/.env.local`)
```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3003
NEXT_PUBLIC_WS_URL=ws://localhost:3003

# Supabase Configuration (if using)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# App Configuration
NEXT_PUBLIC_APP_NAME=KaiNote
NEXT_PUBLIC_APP_URL=http://localhost:3001

# Feature Flags
NEXT_PUBLIC_ENABLE_DEMO_MODE=true
NEXT_PUBLIC_ENABLE_STRIPE=false
```

#### Backend Configuration (`backend/.env`)
```bash
# Server Configuration
NODE_ENV=development
PORT=3003
FRONTEND_URL=http://localhost:3001

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/kainote

# Authentication
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Stripe Configuration (Optional)
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png

# Logging
LOG_LEVEL=info

# Demo Mode
ENABLE_DEMO_MODE=true
```

### Step 5: Build and Start

#### Development Mode
```bash
# Start both services
npm run dev

# Or start individually
npm run dev:frontend  # Frontend on http://localhost:3001
npm run dev:backend   # Backend on http://localhost:3003
```

#### Production Build
```bash
# Build both frontend and backend
npm run build

# Start production servers
npm run start
```

## 🔧 Development Commands

### Root Commands
```bash
npm run dev          # Start both frontend and backend
npm run build        # Build both applications
npm run start        # Start production servers
npm run test         # Run all tests
npm run lint         # Lint all code
npm run clean        # Clean node_modules and build files
```

### Frontend Commands
```bash
cd frontend
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Lint frontend code
npm run type-check   # TypeScript type checking
```

### Backend Commands
```bash
cd backend
npm run dev          # Start development server with nodemon
npm run build        # Compile TypeScript
npm run start        # Start production server
npm run test         # Run backend tests
npm run db:migrate   # Run database migrations
npm run db:seed      # Seed database with demo data
```

## 🐳 Docker Setup (Alternative)

### Using Docker Compose
```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Docker Compose Configuration
The project includes a complete `docker-compose.yml` with:
- Frontend (Next.js)
- Backend (Express.js)
- Database (PostgreSQL)
- Redis (for caching)

## 🌐 Deployment Options

### Vercel + Railway (Recommended)
1. **Frontend to Vercel**:
   - Connect GitHub repository
   - Set environment variables
   - Deploy automatically

2. **Backend to Railway**:
   - Connect GitHub repository
   - Add PostgreSQL database
   - Set environment variables

### AWS Deployment
- **Frontend**: AWS Amplify
- **Backend**: AWS ECS or Lambda
- **Database**: AWS RDS PostgreSQL

### Self-Hosted
- **Server**: Ubuntu/CentOS with Docker
- **Database**: PostgreSQL
- **Reverse Proxy**: Nginx
- **SSL**: Let's Encrypt

## 🔍 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Kill process on port 3001 or 3003
lsof -ti:3001 | xargs kill -9
lsof -ti:3003 | xargs kill -9
```

#### Database Connection Issues
```bash
# Check PostgreSQL status
pg_isready

# Check connection string format
postgresql://username:password@host:port/database
```

#### OpenAI API Issues
- Verify API key is correct
- Check API quota and billing
- Ensure proper environment variable format

#### Build Failures
```bash
# Clear node_modules and reinstall
npm run clean
npm install

# Clear Next.js cache
cd frontend && rm -rf .next
```

### Getting Help
- **Documentation**: Check `/docs` folder
- **Issues**: Create GitHub issue
- **Logs**: Check browser console and server logs

## 📚 Next Steps

After successful setup:
1. **Explore Features**: Visit http://localhost:3001
2. **Admin Dashboard**: Visit http://localhost:3001/admin
3. **API Documentation**: Visit http://localhost:3003/health
4. **Read Documentation**: Check `/docs` folder
5. **Customize**: Modify configuration as needed

## 🎯 Demo Access

### User Demo
- **URL**: http://localhost:3001
- **Login**: Any email/password (demo mode)
- **Features**: All user features available

### Admin Demo
- **URL**: http://localhost:3001/admin
- **Login**: <EMAIL> / any password
- **Features**: User management, analytics, system monitoring

## 📊 Health Checks

### Verify Installation
```bash
# Check frontend
curl http://localhost:3001

# Check backend API
curl http://localhost:3003/health

# Check WebSocket (live transcription)
# Should show WebSocket upgrade response
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" http://localhost:3003/ws/transcription
```

### Expected Response
```json
{
  "status": "OK",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.45,
  "environment": "development"
}
```
