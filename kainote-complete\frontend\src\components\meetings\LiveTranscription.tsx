'use client';

import { useState, useEffect, useRef } from 'react';
import {
  MicrophoneIcon,
  StopIcon,
  PlayIcon,
  PauseIcon,
  DocumentTextIcon,
  SpeakerWaveIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface TranscriptionSegment {
  id: string;
  text: string;
  timestamp: number;
  confidence: number;
  speaker?: string;
  isFinal: boolean;
}

interface LiveTranscriptionProps {
  meetingId?: string;
  onTranscriptionUpdate?: (segments: TranscriptionSegment[]) => void;
  autoSave?: boolean;
}

export function LiveTranscription({ 
  meetingId, 
  onTranscriptionUpdate, 
  autoSave = true 
}: LiveTranscriptionProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [transcriptionSegments, setTranscriptionSegments] = useState<TranscriptionSegment[]>([]);
  const [currentText, setCurrentText] = useState('');
  const [audioLevel, setAudioLevel] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const websocketRef = useRef<WebSocket | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const chunksRef = useRef<Blob[]>([]);

  // Initialize audio context and analyzer for audio level monitoring
  const initializeAudioAnalyzer = (stream: MediaStream) => {
    try {
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      source.connect(analyserRef.current);
      
      analyserRef.current.fftSize = 256;
      const bufferLength = analyserRef.current.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);

      const updateAudioLevel = () => {
        if (analyserRef.current && isRecording && !isPaused) {
          analyserRef.current.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((a, b) => a + b) / bufferLength;
          setAudioLevel(average / 255);
          requestAnimationFrame(updateAudioLevel);
        }
      };
      updateAudioLevel();
    } catch (error) {
      console.error('Error initializing audio analyzer:', error);
    }
  };

  // Initialize WebSocket connection for real-time transcription
  const initializeWebSocket = () => {
    try {
      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3003/ws/transcription';
      websocketRef.current = new WebSocket(wsUrl);

      websocketRef.current.onopen = () => {
        setIsConnected(true);
        console.log('WebSocket connected for live transcription');
      };

      websocketRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'transcription') {
            const segment: TranscriptionSegment = {
              id: data.id || Date.now().toString(),
              text: data.text,
              timestamp: data.timestamp || Date.now(),
              confidence: data.confidence || 0.8,
              speaker: data.speaker,
              isFinal: data.is_final || false,
            };

            if (segment.isFinal) {
              setTranscriptionSegments(prev => {
                const updated = [...prev, segment];
                onTranscriptionUpdate?.(updated);
                return updated;
              });
              setCurrentText('');
            } else {
              setCurrentText(segment.text);
            }
          } else if (data.type === 'error') {
            setError(data.message);
            toast.error(`Transcription error: ${data.message}`);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      websocketRef.current.onclose = () => {
        setIsConnected(false);
        console.log('WebSocket disconnected');
      };

      websocketRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setError('Connection error occurred');
        setIsConnected(false);
      };
    } catch (error) {
      console.error('Error initializing WebSocket:', error);
      setError('Failed to connect to transcription service');
    }
  };

  // Start recording and live transcription
  const startRecording = async () => {
    try {
      setError(null);
      
      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000,
        }
      });

      streamRef.current = stream;
      
      // Initialize audio analyzer
      initializeAudioAnalyzer(stream);
      
      // Initialize WebSocket
      initializeWebSocket();
      
      // Setup MediaRecorder for audio chunks
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
          
          // Send audio chunk to WebSocket for real-time transcription
          if (websocketRef.current?.readyState === WebSocket.OPEN) {
            websocketRef.current.send(event.data);
          }
        }
      };

      mediaRecorderRef.current.onstop = () => {
        // Handle recording stop
        if (autoSave && chunksRef.current.length > 0) {
          saveRecording();
        }
      };

      // Start recording with small chunks for real-time processing
      mediaRecorderRef.current.start(1000); // 1 second chunks
      setIsRecording(true);
      setIsPaused(false);
      
      toast.success('Live transcription started');
    } catch (error) {
      console.error('Error starting recording:', error);
      setError('Failed to access microphone');
      toast.error('Failed to start recording. Please check microphone permissions.');
    }
  };

  // Stop recording and transcription
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setIsPaused(false);
      setAudioLevel(0);
      
      // Stop audio stream
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      
      // Close WebSocket
      if (websocketRef.current) {
        websocketRef.current.close();
      }
      
      // Close audio context
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
      
      toast.success('Recording stopped');
    }
  };

  // Pause/resume recording
  const togglePause = () => {
    if (mediaRecorderRef.current && isRecording) {
      if (isPaused) {
        mediaRecorderRef.current.resume();
        setIsPaused(false);
        toast.success('Recording resumed');
      } else {
        mediaRecorderRef.current.pause();
        setIsPaused(true);
        toast.success('Recording paused');
      }
    }
  };

  // Save recording to backend
  const saveRecording = async () => {
    if (chunksRef.current.length === 0) return;

    try {
      const audioBlob = new Blob(chunksRef.current, { type: 'audio/webm' });
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.webm');
      formData.append('transcription', JSON.stringify(transcriptionSegments));
      
      if (meetingId) {
        formData.append('meetingId', meetingId);
      }

      const response = await fetch('/api/meetings/save-recording', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: formData,
      });

      if (response.ok) {
        toast.success('Recording saved successfully');
        chunksRef.current = [];
      } else {
        throw new Error('Failed to save recording');
      }
    } catch (error) {
      console.error('Error saving recording:', error);
      toast.error('Failed to save recording');
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isRecording) {
        stopRecording();
      }
    };
  }, []);

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg ${isRecording ? 'bg-red-100' : 'bg-gray-100'}`}>
            <MicrophoneIcon className={`h-6 w-6 ${isRecording ? 'text-red-600' : 'text-gray-600'}`} />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Live Transcription</h3>
            <p className="text-sm text-gray-600">
              {isRecording ? (isPaused ? 'Paused' : 'Recording...') : 'Ready to record'}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Connection status */}
          <div className={`flex items-center space-x-1 text-xs ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
          </div>

          {/* Audio level indicator */}
          {isRecording && (
            <div className="flex items-center space-x-1">
              <SpeakerWaveIcon className="h-4 w-4 text-gray-500" />
              <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-green-500 transition-all duration-100"
                  style={{ width: `${audioLevel * 100}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Error display */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2">
          <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
          <span className="text-red-700 text-sm">{error}</span>
        </div>
      )}

      {/* Control buttons */}
      <div className="flex items-center space-x-3 mb-6">
        {!isRecording ? (
          <button
            onClick={startRecording}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <MicrophoneIcon className="h-5 w-5" />
            <span>Start Recording</span>
          </button>
        ) : (
          <>
            <button
              onClick={togglePause}
              className="flex items-center space-x-2 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
            >
              {isPaused ? <PlayIcon className="h-5 w-5" /> : <PauseIcon className="h-5 w-5" />}
              <span>{isPaused ? 'Resume' : 'Pause'}</span>
            </button>
            
            <button
              onClick={stopRecording}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <StopIcon className="h-5 w-5" />
              <span>Stop</span>
            </button>
          </>
        )}

        {transcriptionSegments.length > 0 && (
          <button
            onClick={saveRecording}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <DocumentTextIcon className="h-5 w-5" />
            <span>Save</span>
          </button>
        )}
      </div>

      {/* Live transcription display */}
      <div className="border border-gray-200 rounded-lg p-4 min-h-[300px] max-h-[500px] overflow-y-auto">
        <div className="space-y-3">
          {/* Final transcription segments */}
          {transcriptionSegments.map((segment) => (
            <div key={segment.id} className="flex items-start space-x-3">
              <div className="text-xs text-gray-500 mt-1 w-16 flex-shrink-0">
                {formatTimestamp(segment.timestamp)}
              </div>
              <div className="flex-1">
                <p className="text-gray-900">{segment.text}</p>
                {segment.confidence && (
                  <div className="mt-1 flex items-center space-x-2">
                    <div className="w-16 h-1 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className={`h-full rounded-full ${
                          segment.confidence > 0.8 ? 'bg-green-500' : 
                          segment.confidence > 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${segment.confidence * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-xs text-gray-500">
                      {Math.round(segment.confidence * 100)}% confidence
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Current (interim) transcription */}
          {currentText && (
            <div className="flex items-start space-x-3 opacity-70">
              <div className="text-xs text-gray-500 mt-1 w-16 flex-shrink-0">
                {formatTimestamp(Date.now())}
              </div>
              <div className="flex-1">
                <p className="text-gray-600 italic">{currentText}</p>
                <span className="text-xs text-gray-400">Transcribing...</span>
              </div>
            </div>
          )}

          {/* Empty state */}
          {transcriptionSegments.length === 0 && !currentText && (
            <div className="text-center text-gray-500 py-12">
              <MicrophoneIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Start recording to see live transcription</p>
            </div>
          )}
        </div>
      </div>

      {/* Statistics */}
      {transcriptionSegments.length > 0 && (
        <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
          <span>{transcriptionSegments.length} segments transcribed</span>
          <span>
            Avg. confidence: {Math.round(
              transcriptionSegments.reduce((sum, seg) => sum + (seg.confidence || 0), 0) / 
              transcriptionSegments.length * 100
            )}%
          </span>
        </div>
      )}
    </div>
  );
}
