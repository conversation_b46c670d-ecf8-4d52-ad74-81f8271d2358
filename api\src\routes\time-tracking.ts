import express, { Request, Response, NextFunction } from 'express';

const router = express.Router();

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    userId: string;
    email: string;
    name: string;
  };
}

// Demo auth middleware
const demoAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  req.user = {
    id: 'demo-user-id',
    userId: 'demo-user-id',
    email: '<EMAIL>',
    name: '<PERSON>'
  };
  next();
};

// In-memory storage for demo (use database in production)
let timeEntries: any[] = [
  {
    id: 'time_1',
    userId: 'demo-user-id',
    projectId: 'demo-project-1',
    projectName: 'E-commerce Platform Redesign',
    description: 'Working on homepage design and user flow',
    startTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    duration: 90, // minutes
    hourlyRate: 75,
    totalAmount: 112.50,
    isRunning: false,
    isBillable: true,
    tags: ['design', 'frontend'],
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 'time_2',
    userId: 'demo-user-id',
    projectId: 'demo-project-2',
    projectName: 'SaaS Dashboard Development',
    description: 'Implementing data visualization components',
    startTime: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() - 2.5 * 60 * 60 * 1000).toISOString(),
    duration: 90,
    hourlyRate: 85,
    totalAmount: 127.50,
    isRunning: false,
    isBillable: true,
    tags: ['development', 'react'],
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 'time_3',
    userId: 'demo-user-id',
    projectId: 'demo-project-1',
    projectName: 'E-commerce Platform Redesign',
    description: 'Code review and bug fixes',
    startTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    endTime: null,
    duration: 0,
    hourlyRate: 75,
    totalAmount: 0,
    isRunning: true,
    isBillable: true,
    tags: ['development', 'debugging'],
    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString()
  }
];

// Get all time entries
router.get('/', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { project_id, start_date, end_date, is_running } = req.query;
    const user = req.user!;

    let filteredEntries = timeEntries.filter(entry => entry.userId === user.id);

    // Apply filters
    if (project_id) {
      filteredEntries = filteredEntries.filter(entry => entry.projectId === project_id);
    }
    
    if (start_date) {
      filteredEntries = filteredEntries.filter(entry => 
        new Date(entry.startTime) >= new Date(start_date as string)
      );
    }
    
    if (end_date) {
      filteredEntries = filteredEntries.filter(entry => 
        new Date(entry.startTime) <= new Date(end_date as string)
      );
    }
    
    if (is_running !== undefined) {
      filteredEntries = filteredEntries.filter(entry => 
        entry.isRunning === (is_running === 'true')
      );
    }

    res.json({
      success: true,
      data: filteredEntries
    });

  } catch (error: any) {
    console.error('Error getting time entries:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch time entries',
      details: error.message
    });
  }
});

// Start timer
router.post('/start', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { projectId, description, hourlyRate, tags } = req.body;
    const user = req.user!;

    // Stop any running timer first
    const runningEntry = timeEntries.find(entry => entry.userId === user.id && entry.isRunning);
    if (runningEntry) {
      runningEntry.isRunning = false;
      runningEntry.endTime = new Date().toISOString();
      runningEntry.duration = Math.floor((new Date(runningEntry.endTime).getTime() - new Date(runningEntry.startTime).getTime()) / (1000 * 60));
      runningEntry.totalAmount = (runningEntry.duration / 60) * runningEntry.hourlyRate;
    }

    const newEntry = {
      id: `time_${Date.now()}`,
      userId: user.id,
      projectId,
      projectName: 'Demo Project', // In real app, fetch from projects table
      description: description || '',
      startTime: new Date().toISOString(),
      endTime: null,
      duration: 0,
      hourlyRate: hourlyRate || 75,
      totalAmount: 0,
      isRunning: true,
      isBillable: true,
      tags: tags || [],
      createdAt: new Date().toISOString()
    };

    timeEntries.push(newEntry);

    res.json({
      success: true,
      data: newEntry
    });

  } catch (error: any) {
    console.error('Error starting timer:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to start timer',
      details: error.message
    });
  }
});

// Stop timer
router.post('/stop', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.body;
    const user = req.user!;

    const entry = timeEntries.find(e => e.id === id && e.userId === user.id);
    if (!entry) {
      return res.status(404).json({
        success: false,
        error: 'Time entry not found'
      });
    }

    if (!entry.isRunning) {
      return res.status(400).json({
        success: false,
        error: 'Timer is not running'
      });
    }

    entry.isRunning = false;
    entry.endTime = new Date().toISOString();
    entry.duration = Math.floor((new Date(entry.endTime).getTime() - new Date(entry.startTime).getTime()) / (1000 * 60));
    entry.totalAmount = (entry.duration / 60) * entry.hourlyRate;

    res.json({
      success: true,
      data: entry
    });

  } catch (error: any) {
    console.error('Error stopping timer:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to stop timer',
      details: error.message
    });
  }
});

// Create manual time entry
router.post('/', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { projectId, description, startTime, endTime, hourlyRate, tags } = req.body;
    const user = req.user!;

    const start = new Date(startTime);
    const end = new Date(endTime);
    const duration = Math.floor((end.getTime() - start.getTime()) / (1000 * 60));

    const newEntry = {
      id: `time_${Date.now()}`,
      userId: user.id,
      projectId,
      projectName: 'Demo Project',
      description: description || '',
      startTime: start.toISOString(),
      endTime: end.toISOString(),
      duration,
      hourlyRate: hourlyRate || 75,
      totalAmount: (duration / 60) * (hourlyRate || 75),
      isRunning: false,
      isBillable: true,
      tags: tags || [],
      createdAt: new Date().toISOString()
    };

    timeEntries.push(newEntry);

    res.json({
      success: true,
      data: newEntry
    });

  } catch (error: any) {
    console.error('Error creating time entry:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create time entry',
      details: error.message
    });
  }
});

export default router;
