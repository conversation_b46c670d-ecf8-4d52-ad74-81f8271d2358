{"name": "kainote-api", "version": "1.0.0", "description": "KaiNote API backend for meeting transcription and action item extraction", "main": "dist/index.js", "scripts": {"dev": "ts-node src/index.ts", "dev-full": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "clean": "rm -rf dist"}, "dependencies": {"@supabase/supabase-js": "^2.50.1", "@types/stripe": "^8.0.416", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "form-data": "^4.0.0", "helmet": "^7.2.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.522.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "openai": "^4.104.0", "recharts": "^2.15.4", "stripe": "^18.2.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/fluent-ffmpeg": "^2.1.24", "@types/jest": "^29.5.8", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node": "^20.19.1", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}