import express, { Request, Response, NextFunction } from 'express';
import stripe, { stripeConfig, subscriptionPlans } from '../config/stripe';

const router = express.Router();

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    userId: string;
    email: string;
    name: string;
    subscription_tier: string;
  };
}

// Demo auth middleware (replace with real auth in production)
const demoAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  req.user = {
    id: 'demo-user-id',
    userId: 'demo-user-id',
    email: '<EMAIL>',
    name: 'Demo User',
    subscription_tier: 'professional'
  };
  next();
};

// Get subscription plans
router.get('/plans', (req: Request, res: Response) => {
  res.json({
    success: true,
    data: subscriptionPlans
  });
});

// Create checkout session
router.post('/create-checkout-session', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { planId, successUrl, cancelUrl } = req.body;
    const user = req.user;

    // Validate plan
    const plan = Object.values(subscriptionPlans).find(p => p.name.toLowerCase() === planId);
    if (!plan) {
      return res.status(400).json({
        success: false,
        error: 'Invalid plan selected'
      });
    }

    // For free plan, no checkout needed
    if (plan.price === 0) {
      return res.json({
        success: true,
        data: {
          url: successUrl || stripeConfig.successUrl,
          planName: plan.name
        }
      });
    }

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: plan.priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: successUrl || stripeConfig.successUrl + '?session_id={CHECKOUT_SESSION_ID}',
      cancel_url: cancelUrl || stripeConfig.cancelUrl,
      customer_email: user.email,
      metadata: {
        userId: user.id,
        planId: planId
      },
      subscription_data: {
        metadata: {
          userId: user.id,
          planId: planId
        }
      }
    });

    res.json({
      success: true,
      data: {
        sessionId: session.id,
        url: session.url,
        planName: plan.name
      }
    });

  } catch (error: any) {
    console.error('Stripe checkout error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create checkout session',
      details: error.message
    });
  }
});

// Get customer portal session
router.post('/create-portal-session', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user;
    const { returnUrl } = req.body;

    // In a real app, you'd get the customer ID from your database
    // For demo, we'll create a demo portal URL
    const portalUrl = `${process.env.FRONTEND_URL}/billing/manage?demo=true`;

    res.json({
      success: true,
      data: {
        url: portalUrl
      }
    });

  } catch (error: any) {
    console.error('Stripe portal error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create portal session',
      details: error.message
    });
  }
});

// Get current subscription
router.get('/subscription', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user;

    // Demo subscription data
    const demoSubscription = {
      id: 'sub_demo_123',
      status: 'active',
      plan: subscriptionPlans.professional,
      currentPeriodStart: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      currentPeriodEnd: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
      cancelAtPeriodEnd: false,
      customer: {
        id: 'cus_demo_123',
        email: user.email,
        name: user.name
      },
      usage: {
        meetings: 45,
        meetingLimit: -1, // unlimited
        botSessions: 12,
        botSessionLimit: -1,
        storageUsed: '2.3 GB',
        storageLimit: '100 GB'
      }
    };

    res.json({
      success: true,
      data: demoSubscription
    });

  } catch (error: any) {
    console.error('Get subscription error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get subscription',
      details: error.message
    });
  }
});

// Get billing history
router.get('/invoices', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user;

    // Demo invoice data
    const demoInvoices = [
      {
        id: 'in_demo_001',
        number: 'KN-2024-001',
        status: 'paid',
        amount: 1900,
        currency: 'usd',
        created: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        paidAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        periodStart: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
        periodEnd: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        plan: 'Professional',
        downloadUrl: '#'
      },
      {
        id: 'in_demo_002',
        number: 'KN-2024-002',
        status: 'paid',
        amount: 1900,
        currency: 'usd',
        created: new Date().toISOString(),
        paidAt: new Date().toISOString(),
        periodStart: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        periodEnd: new Date().toISOString(),
        plan: 'Professional',
        downloadUrl: '#'
      }
    ];

    res.json({
      success: true,
      data: demoInvoices
    });

  } catch (error: any) {
    console.error('Get invoices error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get invoices',
      details: error.message
    });
  }
});

// Cancel subscription
router.post('/cancel-subscription', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user;
    const { cancelAtPeriodEnd = true } = req.body;

    // Demo cancellation
    res.json({
      success: true,
      data: {
        status: cancelAtPeriodEnd ? 'cancel_at_period_end' : 'canceled',
        cancelAtPeriodEnd,
        message: cancelAtPeriodEnd 
          ? 'Subscription will be canceled at the end of the current billing period'
          : 'Subscription canceled immediately'
      }
    });

  } catch (error: any) {
    console.error('Cancel subscription error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel subscription',
      details: error.message
    });
  }
});

export default router;
