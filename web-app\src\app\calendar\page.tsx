'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import toast from 'react-hot-toast';
import {
  CalendarIcon,
  ClockIcon,
  UserGroupIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  BoltIcon
} from '@heroicons/react/24/outline';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, addMonths, subMonths } from 'date-fns';

interface CalendarEvent {
  id: string;
  type: 'task_deadline' | 'work_block' | 'meeting' | 'time_session' | 'milestone' | 'auto_scheduled' | 'smart_work_block';
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  all_day: boolean;
  status: 'upcoming' | 'scheduled' | 'completed';
  priority: 'low' | 'medium' | 'high';
  project: {
    id: string;
    name: string;
    client_name: string;
  };
  task?: {
    id: string;
    title: string;
    estimated_hours: number;
    actual_hours?: number;
    progress: number;
  };
  time_allocation?: {
    planned_hours: number;
    hourly_rate: number;
    estimated_earnings: number;
  };
  time_tracking?: {
    actual_hours: number;
    hourly_rate: number;
    total_earnings: number;
    is_billable: boolean;
  };
  meeting?: {
    platform: string;
    meeting_url: string;
    attendees: string[];
  };
  milestone?: {
    completion_percentage: number;
    deliverables: string[];
    budget_used: number;
    total_budget: number;
  };
  automation?: {
    rule_id: string;
    rule_name: string;
    trigger: string;
    action: string;
    template: string;
    recipient: string;
  };
  smart_scheduling?: {
    algorithm: string;
    confidence_score: number;
    factors: string[];
    suggested_by: string;
    optimal_reasons: string[];
  };
  color: string;
}

interface CalendarSummary {
  total_events: number;
  upcoming_deadlines: number;
  scheduled_work_hours: number;
  estimated_earnings: number;
  meetings_count: number;
  completed_hours: number;
}

export default function CalendarPage() {
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [summary, setSummary] = useState<CalendarSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [view, setView] = useState<'month' | 'week' | 'day'>('month');

  useEffect(() => {
    // Set demo token if none exists
    if (!localStorage.getItem('token')) {
      localStorage.setItem('token', 'demo-token');
    }
    fetchCalendarData();
  }, [currentDate, view]);

  const fetchCalendarData = async () => {
    try {
      setLoading(true);
      
      const startDate = startOfMonth(currentDate);
      const endDate = endOfMonth(currentDate);
      
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/calendar/events?start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}&view=${view}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setEvents(data.data.events);
        setSummary(data.data.summary);
      } else {
        toast.error('Failed to load calendar data');
      }
    } catch (error) {
      console.error('Error fetching calendar data:', error);
      toast.error('Failed to load calendar data');
    } finally {
      setLoading(false);
    }
  };

  const getEventsForDate = (date: Date) => {
    return events.filter(event => 
      isSameDay(new Date(event.start_time), date)
    );
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'task_deadline':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      case 'work_block':
        return <ClockIcon className="h-4 w-4" />;
      case 'meeting':
        return <UserGroupIcon className="h-4 w-4" />;
      case 'time_session':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'milestone':
        return <ChartBarIcon className="h-4 w-4" />;
      case 'auto_scheduled':
        return <BoltIcon className="h-4 w-4" />;
      case 'smart_work_block':
        return <BoltIcon className="h-4 w-4" />;
      default:
        return <CalendarIcon className="h-4 w-4" />;
    }
  };

  const getEventTypeLabel = (type: string) => {
    switch (type) {
      case 'task_deadline':
        return 'Deadline';
      case 'work_block':
        return 'Work Block';
      case 'meeting':
        return 'Meeting';
      case 'time_session':
        return 'Time Session';
      case 'milestone':
        return 'Milestone';
      case 'auto_scheduled':
        return 'Automated';
      case 'smart_work_block':
        return 'Smart Schedule';
      default:
        return 'Event';
    }
  };

  const formatTime = (dateString: string) => {
    return format(new Date(dateString), 'h:mm a');
  };

  const formatDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffMs = end.getTime() - start.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setCurrentDate(subMonths(currentDate, 1));
    } else {
      setCurrentDate(addMonths(currentDate, 1));
    }
  };

  const monthDays = eachDayOfInterval({
    start: startOfMonth(currentDate),
    end: endOfMonth(currentDate)
  });

  if (loading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Smart Calendar</h1>
              <p className="text-gray-600">Track tasks, meetings, deadlines, and time allocations</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setView('day')}
                  className={`px-3 py-1 rounded-md text-sm ${view === 'day' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                >
                  Day
                </button>
                <button
                  onClick={() => setView('week')}
                  className={`px-3 py-1 rounded-md text-sm ${view === 'week' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                >
                  Week
                </button>
                <button
                  onClick={() => setView('month')}
                  className={`px-3 py-1 rounded-md text-sm ${view === 'month' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                >
                  Month
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mb-8">
            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-lg p-2">
                  <CalendarIcon className="h-5 w-5 text-blue-600" />
                </div>
                <div className="ml-3">
                  <p className="text-xs font-medium text-gray-600">Total Events</p>
                  <p className="text-lg font-bold text-gray-900">{summary.total_events}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="bg-red-100 rounded-lg p-2">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
                </div>
                <div className="ml-3">
                  <p className="text-xs font-medium text-gray-600">Deadlines</p>
                  <p className="text-lg font-bold text-gray-900">{summary.upcoming_deadlines}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-2">
                  <ClockIcon className="h-5 w-5 text-green-600" />
                </div>
                <div className="ml-3">
                  <p className="text-xs font-medium text-gray-600">Scheduled Hours</p>
                  <p className="text-lg font-bold text-gray-900">{summary.scheduled_work_hours}h</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="bg-purple-100 rounded-lg p-2">
                  <CurrencyDollarIcon className="h-5 w-5 text-purple-600" />
                </div>
                <div className="ml-3">
                  <p className="text-xs font-medium text-gray-600">Est. Earnings</p>
                  <p className="text-lg font-bold text-gray-900">${summary.estimated_earnings}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="bg-orange-100 rounded-lg p-2">
                  <UserGroupIcon className="h-5 w-5 text-orange-600" />
                </div>
                <div className="ml-3">
                  <p className="text-xs font-medium text-gray-600">Meetings</p>
                  <p className="text-lg font-bold text-gray-900">{summary.meetings_count}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="bg-gray-100 rounded-lg p-2">
                  <CheckCircleIcon className="h-5 w-5 text-gray-600" />
                </div>
                <div className="ml-3">
                  <p className="text-xs font-medium text-gray-600">Completed</p>
                  <p className="text-lg font-bold text-gray-900">{summary.completed_hours}h</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Calendar Navigation */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => navigateMonth('prev')}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <ArrowLeftIcon className="h-5 w-5 text-gray-600" />
                </button>
                <h2 className="text-xl font-semibold text-gray-900">
                  {format(currentDate, 'MMMM yyyy')}
                </h2>
                <button
                  onClick={() => navigateMonth('next')}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <ArrowRightIcon className="h-5 w-5 text-gray-600" />
                </button>
              </div>
              <button
                onClick={() => setCurrentDate(new Date())}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
              >
                Today
              </button>
            </div>
          </div>

          {/* Calendar Grid */}
          <div className="p-6">
            {/* Days of Week Header */}
            <div className="grid grid-cols-7 gap-1 mb-4">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Days */}
            <div className="grid grid-cols-7 gap-1">
              {monthDays.map(day => {
                const dayEvents = getEventsForDate(day);
                const isToday = isSameDay(day, new Date());
                const isSelected = selectedDate && isSameDay(day, selectedDate);

                return (
                  <div
                    key={day.toISOString()}
                    onClick={() => setSelectedDate(day)}
                    className={`min-h-[120px] p-2 border border-gray-200 cursor-pointer hover:bg-gray-50 ${
                      isToday ? 'bg-blue-50 border-blue-300' : ''
                    } ${isSelected ? 'bg-primary-50 border-primary-300' : ''}`}
                  >
                    <div className={`text-sm font-medium mb-1 ${
                      isToday ? 'text-blue-600' : 'text-gray-900'
                    }`}>
                      {format(day, 'd')}
                    </div>

                    <div className="space-y-1">
                      {dayEvents.slice(0, 3).map(event => (
                        <div
                          key={event.id}
                          className="text-xs p-1 rounded truncate"
                          style={{ backgroundColor: event.color + '20', color: event.color }}
                          title={`${event.title} - ${formatTime(event.start_time)}`}
                        >
                          <div className="flex items-center space-x-1">
                            {getEventTypeIcon(event.type)}
                            <span className="truncate">{event.title}</span>
                          </div>
                        </div>
                      ))}
                      {dayEvents.length > 3 && (
                        <div className="text-xs text-gray-500 font-medium">
                          +{dayEvents.length - 3} more
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Selected Date Events */}
        {selectedDate && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Events for {format(selectedDate, 'EEEE, MMMM d, yyyy')}
              </h3>
            </div>
            <div className="p-6">
              {getEventsForDate(selectedDate).length === 0 ? (
                <p className="text-gray-500 text-center py-8">No events scheduled for this date</p>
              ) : (
                <div className="space-y-4">
                  {getEventsForDate(selectedDate).map(event => (
                    <div key={event.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <div
                              className="p-1 rounded"
                              style={{ backgroundColor: event.color + '20', color: event.color }}
                            >
                              {getEventTypeIcon(event.type)}
                            </div>
                            <span className="text-xs font-medium text-gray-500 uppercase">
                              {getEventTypeLabel(event.type)}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              event.priority === 'high' ? 'bg-red-100 text-red-800' :
                              event.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {event.priority}
                            </span>
                          </div>

                          <h4 className="text-lg font-semibold text-gray-900 mb-1">
                            {event.title}
                          </h4>
                          <p className="text-gray-600 mb-2">{event.description}</p>

                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>
                              {event.all_day ? 'All Day' : `${formatTime(event.start_time)} - ${formatTime(event.end_time)}`}
                            </span>
                            {!event.all_day && (
                              <span>Duration: {formatDuration(event.start_time, event.end_time)}</span>
                            )}
                          </div>

                          <div className="mt-2 text-sm">
                            <span className="font-medium text-gray-700">Project: </span>
                            <span className="text-gray-600">{event.project.name} - {event.project.client_name}</span>
                          </div>

                          {/* Task Information */}
                          {event.task && (
                            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <span className="font-medium text-gray-700">Task: {event.task.title}</span>
                                <span className="text-sm text-gray-500">{event.task.progress}% complete</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                                <div
                                  className="bg-blue-600 h-2 rounded-full"
                                  style={{ width: `${event.task.progress}%` }}
                                ></div>
                              </div>
                              <div className="flex items-center justify-between text-sm text-gray-600">
                                <span>Estimated: {event.task.estimated_hours}h</span>
                                {event.task.actual_hours && (
                                  <span>Actual: {event.task.actual_hours}h</span>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Time Allocation */}
                          {event.time_allocation && (
                            <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                              <div className="flex items-center justify-between text-sm">
                                <span className="font-medium text-gray-700">Planned Work:</span>
                                <span className="text-blue-600 font-medium">
                                  ${event.time_allocation.estimated_earnings}
                                </span>
                              </div>
                              <div className="text-sm text-gray-600 mt-1">
                                {event.time_allocation.planned_hours}h @ ${event.time_allocation.hourly_rate}/hr
                              </div>
                            </div>
                          )}

                          {/* Time Tracking Results */}
                          {event.time_tracking && (
                            <div className="mt-3 p-3 bg-green-50 rounded-lg">
                              <div className="flex items-center justify-between text-sm">
                                <span className="font-medium text-gray-700">Completed Work:</span>
                                <span className="text-green-600 font-medium">
                                  ${event.time_tracking.total_earnings}
                                </span>
                              </div>
                              <div className="text-sm text-gray-600 mt-1">
                                {event.time_tracking.actual_hours}h @ ${event.time_tracking.hourly_rate}/hr
                                {event.time_tracking.is_billable && (
                                  <span className="ml-2 text-green-600">• Billable</span>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Meeting Information */}
                          {event.meeting && (
                            <div className="mt-3 p-3 bg-purple-50 rounded-lg">
                              <div className="text-sm">
                                <div className="font-medium text-gray-700 mb-1">Meeting Details:</div>
                                <div className="text-gray-600">Platform: {event.meeting.platform}</div>
                                <div className="text-gray-600">Attendees: {event.meeting.attendees.length}</div>
                                {event.meeting.meeting_url && (
                                  <a
                                    href={event.meeting.meeting_url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 hover:text-blue-800 underline"
                                  >
                                    Join Meeting
                                  </a>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Milestone Information */}
                          {event.milestone && (
                            <div className="mt-3 p-3 bg-yellow-50 rounded-lg">
                              <div className="text-sm">
                                <div className="font-medium text-gray-700 mb-2">Milestone Progress:</div>
                                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                                  <div
                                    className="bg-yellow-600 h-2 rounded-full"
                                    style={{ width: `${event.milestone.completion_percentage}%` }}
                                  ></div>
                                </div>
                                <div className="flex items-center justify-between text-gray-600">
                                  <span>{event.milestone.completion_percentage}% complete</span>
                                  <span>${event.milestone.budget_used} / ${event.milestone.total_budget}</span>
                                </div>
                                <div className="mt-2">
                                  <span className="font-medium">Deliverables: </span>
                                  <span>{event.milestone.deliverables.join(', ')}</span>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Automation Information */}
                          {event.automation && (
                            <div className="mt-3 p-3 bg-orange-50 rounded-lg">
                              <div className="text-sm">
                                <div className="flex items-center mb-2">
                                  <BoltIcon className="h-4 w-4 text-orange-600 mr-2" />
                                  <span className="font-medium text-gray-700">Automated Action</span>
                                </div>
                                <div className="space-y-1 text-gray-600">
                                  <div><span className="font-medium">Rule:</span> {event.automation.rule_name}</div>
                                  <div><span className="font-medium">Trigger:</span> {event.automation.trigger}</div>
                                  <div><span className="font-medium">Action:</span> {event.automation.action}</div>
                                  <div><span className="font-medium">Recipient:</span> {event.automation.recipient}</div>
                                  <div className="mt-2 p-2 bg-white rounded border">
                                    <span className="font-medium">Template:</span> {event.automation.template}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Smart Scheduling Information */}
                          {event.smart_scheduling && (
                            <div className="mt-3 p-3 bg-cyan-50 rounded-lg">
                              <div className="text-sm">
                                <div className="flex items-center justify-between mb-2">
                                  <div className="flex items-center">
                                    <BoltIcon className="h-4 w-4 text-cyan-600 mr-2" />
                                    <span className="font-medium text-gray-700">Smart Scheduling</span>
                                  </div>
                                  <span className="text-cyan-600 font-medium">
                                    {Math.round(event.smart_scheduling.confidence_score * 100)}% confidence
                                  </span>
                                </div>
                                <div className="space-y-2 text-gray-600">
                                  <div>
                                    <span className="font-medium">Algorithm:</span> {event.smart_scheduling.algorithm}
                                  </div>
                                  <div>
                                    <span className="font-medium">Suggested by:</span> {event.smart_scheduling.suggested_by}
                                  </div>
                                  <div>
                                    <span className="font-medium">Optimization factors:</span>
                                    <div className="mt-1 flex flex-wrap gap-1">
                                      {event.smart_scheduling.factors.map((factor, index) => (
                                        <span key={index} className="px-2 py-1 bg-cyan-100 text-cyan-800 rounded-full text-xs">
                                          {factor}
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                  <div>
                                    <span className="font-medium">Why this time is optimal:</span>
                                    <ul className="mt-1 list-disc list-inside text-xs">
                                      {event.smart_scheduling.optimal_reasons.map((reason, index) => (
                                        <li key={index}>{reason}</li>
                                      ))}
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
