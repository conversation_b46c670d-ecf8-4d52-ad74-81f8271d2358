'use client';

import { useState, useEffect } from 'react';
import { api } from '@/lib/api';

interface Project {
  id: string;
  name: string;
  client_name: string;
}

export default function ProjectsTest() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setError(null);
      setLoading(true);
      
      const response = await api.get('/projects');
      console.log('Projects API response:', response);
      
      const projectsData = response?.data?.data;
      console.log('Projects data:', projectsData);
      
      if (Array.isArray(projectsData)) {
        setProjects(projectsData);
        console.log('Projects set successfully:', projectsData);
      } else {
        console.warn('Projects data is not an array:', projectsData);
        setProjects([]);
        setError('Invalid projects data format');
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
      setError('Failed to load projects');
      setProjects([]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-4 border border-gray-200 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Projects Test</h3>
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-4 border border-gray-200 rounded-lg">
      <h3 className="text-lg font-semibold mb-2">Projects Test</h3>
      
      {error && (
        <div className="bg-red-50 border border-red-200 rounded p-3 mb-4">
          <p className="text-red-700 text-sm">{error}</p>
          <button
            onClick={fetchProjects}
            className="mt-2 text-sm text-red-600 hover:text-red-500 underline"
          >
            Retry
          </button>
        </div>
      )}

      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Projects array length: {projects?.length || 0}
        </p>
        <p className="text-sm text-gray-600">
          Projects is array: {Array.isArray(projects) ? 'Yes' : 'No'}
        </p>
        
        {projects && projects.length > 0 ? (
          <div>
            <h4 className="font-medium mb-2">Projects:</h4>
            <ul className="space-y-1">
              {projects.map((project, index) => (
                <li key={project.id || index} className="text-sm text-gray-700">
                  {project.name} - {project.client_name}
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <p className="text-sm text-gray-500">No projects available</p>
        )}

        <div className="mt-4">
          <h4 className="font-medium mb-2">Select Test:</h4>
          <select className="w-full border border-gray-300 rounded px-3 py-2">
            <option value="">Select a project</option>
            {projects && projects.length > 0 ? (
              projects.map((project, index) => (
                <option key={project.id || index} value={project.id}>
                  {project.name} - {project.client_name}
                </option>
              ))
            ) : (
              <option value="" disabled>No projects available</option>
            )}
          </select>
        </div>
      </div>
    </div>
  );
}
