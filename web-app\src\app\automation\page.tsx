'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { api } from '@/lib/api';
// import { AutomationRuleForm } from '@/components/automation/AutomationRuleForm';
// import { WorkflowTemplateBuilder } from '@/components/automation/WorkflowTemplateBuilder';
// import { EmailTemplateManager } from '@/components/automation/EmailTemplateManager';

// Placeholder components for features not yet implemented
const AutomationRuleForm = ({ editRule, onClose, onSuccess }: any) => (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Automation Rule Form</h3>
      <p className="text-gray-600 mb-4">This feature is coming soon!</p>
      <button onClick={onClose} className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700">
        Close
      </button>
    </div>
  </div>
);

const WorkflowTemplateBuilder = ({ editTemplate, onClose, onSuccess }: any) => (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Workflow Template Builder</h3>
      <p className="text-gray-600 mb-4">This feature is coming soon!</p>
      <button onClick={onClose} className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700">
        Close
      </button>
    </div>
  </div>
);

const EmailTemplateManager = () => (
  <div className="text-center py-8">
    <EnvelopeIcon className="mx-auto h-12 w-12 text-gray-400" />
    <h3 className="mt-2 text-sm font-medium text-gray-900">Email Template Manager</h3>
    <p className="mt-1 text-sm text-gray-500">This feature is coming soon!</p>
  </div>
);
import {
  PlusIcon,
  CogIcon,
  ClockIcon,
  BoltIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  PauseIcon,
  ArrowPathIcon,
  CalendarIcon,
  EnvelopeIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import toast from 'react-hot-toast';

export default function AutomationPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('overview');
  const [showRuleForm, setShowRuleForm] = useState(false);
  const [showWorkflowForm, setShowWorkflowForm] = useState(false);
  const [editingRule, setEditingRule] = useState(null);
  const [editingWorkflow, setEditingWorkflow] = useState(null);

  // Placeholder data for features not yet implemented
  const workflows: any[] = [];
  const tasks: any[] = [];
  const followups: any[] = [];

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch smart scheduling automation rules
  const { data: smartRules, isLoading: smartRulesLoading } = useQuery(
    'smart-automation-rules',
    () => api.get('/smart-scheduling/automation-rules'),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch scheduled communications
  const { data: scheduledComms, isLoading: commsLoading } = useQuery(
    'scheduled-communications',
    () => api.get('/smart-scheduling/communications/scheduled'),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch automation executions
  const { data: executions, isLoading: executionsLoading } = useQuery(
    'automation-executions',
    () => api.get('/smart-scheduling/automation-rules/executions'),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Execute automation rules manually
  const executeRulesMutation = useMutation(
    (data: any) => api.post('/smart-scheduling/automation-rules/execute', data),
    {
      onSuccess: () => {
        toast.success('Automation rules executed successfully');
        queryClient.invalidateQueries('automation-executions');
        queryClient.invalidateQueries('scheduled-communications');
      },
      onError: () => {
        toast.error('Failed to execute automation rules');
      }
    }
  );

  // Send communication manually
  const sendCommunicationMutation = useMutation(
    (data: any) => api.post('/smart-scheduling/communications/send', data),
    {
      onSuccess: () => {
        toast.success('Communication sent successfully');
        queryClient.invalidateQueries('scheduled-communications');
      },
      onError: () => {
        toast.error('Failed to send communication');
      }
    }
  );

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Calculate smart automation stats
  const smartAutomationStats = {
    totalRules: smartRules?.length || 0,
    activeRules: smartRules?.filter((rule: any) => rule.is_active).length || 0,
    scheduledComms: scheduledComms?.filter((comm: any) => comm.status === 'scheduled').length || 0,
    successRate: smartRules?.length > 0
      ? Math.round((smartRules.reduce((acc: number, rule: any) => acc + rule.success_count, 0) /
          smartRules.reduce((acc: number, rule: any) => acc + rule.execution_count, 0)) * 100) || 0
      : 0
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'smart-scheduling', name: 'Smart Scheduling', icon: CalendarIcon },
    { id: 'rules', name: 'Automation Rules', icon: CogIcon },
    { id: 'communications', name: 'Communications', icon: EnvelopeIcon },
    { id: 'workflows', name: 'Workflows', icon: ArrowPathIcon },
    { id: 'templates', name: 'Email Templates', icon: ExclamationTriangleIcon },
    { id: 'executions', name: 'Execution History', icon: ClockIcon },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Smart Automation & Scheduling</h1>
              <p className="text-gray-600">Automate your project workflows and client communications</p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => executeRulesMutation.mutate({ trigger_type: 'manual_check' })}
                className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center space-x-2"
                disabled={executeRulesMutation.isLoading}
              >
                <PlayIcon className="h-4 w-4" />
                <span>Run Check</span>
              </button>
              <button
                onClick={() => setShowRuleForm(true)}
                className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center space-x-2"
              >
                <PlusIcon className="h-4 w-4" />
                <span>Create Rule</span>
              </button>
              <button
                onClick={() => setShowWorkflowForm(true)}
                className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center space-x-2"
              >
                <PlusIcon className="h-4 w-4" />
                <span>New Workflow</span>
              </button>
            </div>
          </div>
        </div>

        {/* Smart Automation Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-100 rounded-lg p-3">
                <BoltIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Rules</p>
                <p className="text-2xl font-bold text-gray-900">
                  {smartAutomationStats.activeRules} / {smartAutomationStats.totalRules}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-100 rounded-lg p-3">
                <CheckCircleIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-gray-900">{smartAutomationStats.successRate}%</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-yellow-100 rounded-lg p-3">
                <CalendarIcon className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Scheduled Communications</p>
                <p className="text-2xl font-bold text-gray-900">{smartAutomationStats.scheduledComms}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-purple-100 rounded-lg p-3">
                <ClockIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Recent Executions</p>
                <p className="text-2xl font-bold text-gray-900">{executions?.slice(0, 10).length || 0}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Recent Automation Activity */}
                <div className="bg-white shadow rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">Recent Automation Activity</h3>
                  </div>
                  <div className="divide-y divide-gray-200">
                    {executions?.slice(0, 5).map((execution: any) => (
                      <div key={execution.id} className="px-6 py-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900">
                              {execution.rule?.rule_name}
                            </p>
                            <p className="text-sm text-gray-500">
                              {execution.rule?.rule_type} • {format(new Date(execution.executed_at), 'MMM d, yyyy h:mm a')}
                            </p>
                          </div>
                          <div className="flex-shrink-0">
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                              execution.execution_status === 'completed'
                                ? 'bg-green-100 text-green-800'
                                : execution.execution_status === 'failed'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {execution.execution_status}
                            </span>
                          </div>
                        </div>
                      </div>
                    )) || (
                      <div className="px-6 py-8 text-center">
                        <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
                        <p className="mt-1 text-sm text-gray-500">
                          Automation rules will appear here once they start executing.
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Upcoming Communications */}
                <div className="bg-white shadow rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">Upcoming Client Communications</h3>
                  </div>
                  <div className="divide-y divide-gray-200">
                    {scheduledComms?.filter((comm: any) => comm.status === 'scheduled')
                      .slice(0, 5).map((comm: any) => (
                      <div key={comm.id} className="px-6 py-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900">
                              {comm.subject}
                            </p>
                            <p className="text-sm text-gray-500">
                              To: {comm.project?.client_name} • {format(new Date(comm.scheduled_for), 'MMM d, yyyy h:mm a')}
                            </p>
                          </div>
                          <div className="flex-shrink-0">
                            <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                              {comm.status}
                            </span>
                          </div>
                        </div>
                      </div>
                    )) || (
                      <div className="px-6 py-8 text-center">
                        <EnvelopeIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No scheduled communications</h3>
                        <p className="mt-1 text-sm text-gray-500">
                          Automated client communications will appear here.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'smart-scheduling' && (
              <div className="space-y-6">
                {/* Smart Automation Rules */}
                <div className="bg-white shadow rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium text-gray-900">Smart Automation Rules</h3>
                      <button className="btn btn-primary btn-sm">
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Create Rule
                      </button>
                    </div>
                  </div>
                  <div className="divide-y divide-gray-200">
                    {smartRules?.map((rule: any) => (
                      <div key={rule.id} className="px-6 py-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center">
                              <h4 className="text-sm font-medium text-gray-900">
                                {rule.rule_name}
                              </h4>
                              <span className={`ml-2 inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                                rule.is_active
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {rule.is_active ? 'Active' : 'Inactive'}
                              </span>
                            </div>
                            <p className="text-sm text-gray-500">
                              Type: {rule.rule_type} • Executed: {rule.execution_count} times
                            </p>
                            {rule.last_executed && (
                              <p className="text-xs text-gray-400">
                                Last executed: {format(new Date(rule.last_executed), 'MMM d, yyyy h:mm a')}
                              </p>
                            )}
                          </div>
                          <div className="flex-shrink-0 flex items-center space-x-2">
                            <div className="text-right">
                              <p className="text-sm font-medium text-green-600">
                                {rule.success_count} success
                              </p>
                              {rule.failure_count > 0 && (
                                <p className="text-sm font-medium text-red-600">
                                  {rule.failure_count} failed
                                </p>
                              )}
                            </div>
                            <button className="btn btn-sm btn-secondary">
                              Edit
                            </button>
                          </div>
                        </div>
                      </div>
                    )) || (
                      <div className="px-6 py-8 text-center">
                        <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No smart automation rules</h3>
                        <p className="mt-1 text-sm text-gray-500">
                          Create your first smart automation rule to get started.
                        </p>
                        <div className="mt-6">
                          <button className="btn btn-primary">
                            Create Smart Rule
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'communications' && (
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-900">Client Communications</h3>
                    <button
                      onClick={() => sendCommunicationMutation.mutate({ type: 'manual_check' })}
                      className="btn btn-secondary btn-sm"
                      disabled={sendCommunicationMutation.isLoading}
                    >
                      <EnvelopeIcon className="h-4 w-4 mr-2" />
                      Send Test
                    </button>
                  </div>
                </div>
                <div className="divide-y divide-gray-200">
                  {scheduledComms?.map((comm: any) => (
                    <div key={comm.id} className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900">
                            {comm.subject}
                          </h4>
                          <p className="text-sm text-gray-500">
                            To: {comm.recipient_email} ({comm.project?.client_name})
                          </p>
                          <p className="text-sm text-gray-500">
                            Project: {comm.project?.name}
                          </p>
                          <p className="text-xs text-gray-400">
                            Scheduled: {format(new Date(comm.scheduled_for), 'MMM d, yyyy h:mm a')}
                          </p>
                        </div>
                        <div className="flex-shrink-0">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            comm.status === 'sent'
                              ? 'bg-green-100 text-green-800'
                              : comm.status === 'failed'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {comm.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  )) || (
                    <div className="px-6 py-8 text-center">
                      <EnvelopeIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No communications scheduled</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Automated communications will appear here.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'executions' && (
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Execution History</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {executions?.map((execution: any) => (
                    <div key={execution.id} className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900">
                            {execution.rule?.rule_name}
                          </h4>
                          <p className="text-sm text-gray-500">
                            Type: {execution.rule?.rule_type}
                          </p>
                          <p className="text-xs text-gray-400">
                            Executed: {format(new Date(execution.executed_at), 'MMM d, yyyy h:mm a')}
                          </p>
                          {execution.error_message && (
                            <p className="text-xs text-red-600 mt-1">
                              Error: {execution.error_message}
                            </p>
                          )}
                        </div>
                        <div className="flex-shrink-0">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            execution.execution_status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : execution.execution_status === 'failed'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {execution.execution_status}
                          </span>
                        </div>
                      </div>
                    </div>
                  )) || (
                    <div className="px-6 py-8 text-center">
                      <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No execution history</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Automation execution history will appear here.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'rules' && (
              <div>
                {smartRules?.length > 0 ? (
                  <div className="space-y-4">
                    {rules.map((rule: any) => (
                      <div key={rule.id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h4 className="text-lg font-semibold text-gray-900">{rule.name}</h4>
                            {rule.description && (
                              <p className="text-sm text-gray-600">{rule.description}</p>
                            )}
                          </div>
                          <div className="flex items-center space-x-3">
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                              rule.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                            }`}>
                              {rule.is_active ? 'Active' : 'Inactive'}
                            </span>
                            <button className="text-gray-400 hover:text-gray-600">
                              {rule.is_active ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
                            </button>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="font-medium text-gray-700">Trigger</p>
                            <p className="text-gray-600 capitalize">{rule.trigger_type.replace('_', ' ')}</p>
                          </div>
                          <div>
                            <p className="font-medium text-gray-700">Action</p>
                            <p className="text-gray-600 capitalize">{rule.action_type.replace('_', ' ')}</p>
                          </div>
                          <div>
                            <p className="font-medium text-gray-700">Executions</p>
                            <p className="text-gray-600">{rule.execution_count || 0}</p>
                          </div>
                          <div>
                            <p className="font-medium text-gray-700">Last Executed</p>
                            <p className="text-gray-600">
                              {rule.last_executed ? format(new Date(rule.last_executed), 'MMM d, yyyy') : 'Never'}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No automation rules yet</h3>
                    <p className="mt-1 text-sm text-gray-500">Create your first automation rule to get started.</p>
                    <button className="mt-4 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700">
                      Create Rule
                    </button>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'workflows' && (
              <div>
                {workflows.length > 0 ? (
                  <div className="space-y-4">
                    {workflows.map((workflow: any) => (
                      <div key={workflow.id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h4 className="text-lg font-semibold text-gray-900">{workflow.name}</h4>
                            <p className="text-sm text-gray-600">
                              {workflow.template?.name} • {workflow.project?.name || 'No project'}
                            </p>
                          </div>
                          <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(workflow.status)}`}>
                            {workflow.status}
                          </span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex justify-between text-sm text-gray-600 mb-1">
                              <span>Progress</span>
                              <span>{workflow.current_step} of {workflow.total_steps} steps</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-primary-600 h-2 rounded-full"
                                style={{ width: `${(workflow.current_step / workflow.total_steps) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                          <div className="ml-6 text-right text-sm text-gray-500">
                            <p>Started: {format(new Date(workflow.started_at), 'MMM d, yyyy')}</p>
                            {workflow.due_date && (
                              <p>Due: {format(new Date(workflow.due_date), 'MMM d, yyyy')}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <ArrowPathIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No active workflows</h3>
                    <p className="mt-1 text-sm text-gray-500">Start a workflow from a template.</p>
                    <button className="mt-4 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700">
                      Start Workflow
                    </button>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'recurring' && (
              <div>
                {tasks.length > 0 ? (
                  <div className="space-y-4">
                    {tasks.map((task: any) => (
                      <div key={task.id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h4 className="text-lg font-semibold text-gray-900">{task.task_title}</h4>
                            {task.task_description && (
                              <p className="text-sm text-gray-600 mt-1">{task.task_description}</p>
                            )}
                            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                              <span className="capitalize">{task.recurrence_pattern}</span>
                              {task.project && <span>• {task.project.name}</span>}
                              {task.estimated_hours && <span>• {task.estimated_hours}h estimated</span>}
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-900">
                              Next: {format(new Date(task.next_due_date), 'MMM d, yyyy')}
                            </p>
                            <p className="text-xs text-gray-500">
                              Priority: <span className="capitalize">{task.priority}</span>
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No recurring tasks</h3>
                    <p className="mt-1 text-sm text-gray-500">Set up recurring tasks to automate your workflow.</p>
                    <button className="mt-4 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700">
                      Create Recurring Task
                    </button>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'templates' && (
              <EmailTemplateManager />
            )}

            {activeTab === 'followups' && (
              <div>
                {followups.length > 0 ? (
                  <div className="space-y-4">
                    {followups.map((followup: any) => (
                      <div key={followup.id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-lg font-semibold text-gray-900 capitalize">
                              {followup.followup_type.replace('_', ' ')}
                            </h4>
                            <p className="text-sm text-gray-600">
                              Target: {followup.target_type} • {followup.target_id}
                            </p>
                            {followup.custom_message && (
                              <p className="text-sm text-gray-700 mt-2">{followup.custom_message}</p>
                            )}
                          </div>
                          <div className="text-right">
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                              followup.status === 'scheduled' ? 'bg-yellow-100 text-yellow-800' :
                              followup.status === 'sent' ? 'bg-green-100 text-green-800' :
                              followup.status === 'failed' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {followup.status}
                            </span>
                            {followup.scheduled_date && (
                              <p className="text-sm text-gray-500 mt-1">
                                {format(new Date(followup.scheduled_date), 'MMM d, yyyy')}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No automated follow-ups</h3>
                    <p className="mt-1 text-sm text-gray-500">Set up automated follow-ups for better client communication.</p>
                    <button className="mt-4 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700">
                      Create Follow-up
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Automation Rule Form Modal */}
        {showRuleForm && (
          <AutomationRuleForm
            editRule={editingRule}
            onClose={() => {
              setShowRuleForm(false);
              setEditingRule(null);
            }}
            onSuccess={() => {
              // Refresh data
              window.location.reload();
            }}
          />
        )}

        {/* Workflow Template Builder Modal */}
        {showWorkflowForm && (
          <WorkflowTemplateBuilder
            editTemplate={editingWorkflow}
            onClose={() => {
              setShowWorkflowForm(false);
              setEditingWorkflow(null);
            }}
            onSuccess={() => {
              // Refresh data
              window.location.reload();
            }}
          />
        )}
      </div>
    </DashboardLayout>
  );
}
