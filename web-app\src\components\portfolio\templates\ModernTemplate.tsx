'use client';

import { 
  EnvelopeIcon,
  GlobeAltIcon,
  MapPinIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import Image from 'next/image';

interface PortfolioData {
  freelancer: {
    displayName: string;
    title: string;
    bio: string;
    email: string;
    location: string;
    website: string;
    skills: string[];
  };
  projects: any[];
  customization: {
    primaryColor: string;
    secondaryColor: string;
    showContactInfo: boolean;
    showProjectDetails: boolean;
    showClientTestimonials: boolean;
    showSkills: boolean;
    showStats: boolean;
  };
  stats: {
    totalProjects: number;
    completedProjects: number;
    happyClients: number;
    yearsExperience: number;
  };
}

interface ModernTemplateProps {
  data: PortfolioData;
}

export default function ModernTemplate({ data }: ModernTemplateProps) {
  const { freelancer, projects, customization, stats } = data;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with Gradient */}
      <div 
        className="relative overflow-hidden"
        style={{
          background: `linear-gradient(135deg, ${customization.primaryColor}, ${customization.secondaryColor})`
        }}
      >
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center text-white">
            <div className="w-32 h-32 bg-white bg-opacity-20 rounded-full mx-auto mb-6 flex items-center justify-center backdrop-blur-sm">
              <span className="text-3xl font-bold">
                {freelancer.displayName.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
            <h1 className="text-5xl font-bold mb-4">{freelancer.displayName}</h1>
            <p className="text-2xl mb-6 opacity-90">{freelancer.title}</p>
            <p className="text-lg max-w-3xl mx-auto mb-8 opacity-80">{freelancer.bio}</p>
            
            {customization.showContactInfo && (
              <div className="flex flex-wrap justify-center gap-8 text-white opacity-90">
                <div className="flex items-center">
                  <EnvelopeIcon className="h-5 w-5 mr-2" />
                  <a href={`mailto:${freelancer.email}`} className="hover:text-opacity-80">
                    {freelancer.email}
                  </a>
                </div>
                <div className="flex items-center">
                  <MapPinIcon className="h-5 w-5 mr-2" />
                  {freelancer.location}
                </div>
                <div className="flex items-center">
                  <GlobeAltIcon className="h-5 w-5 mr-2" />
                  <a href={freelancer.website} target="_blank" rel="noopener noreferrer" className="hover:text-opacity-80">
                    Portfolio Website
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Stats Cards */}
        {customization.showStats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12 -mt-20 relative z-10">
            <div className="bg-white rounded-xl shadow-lg p-6 text-center">
              <div className="text-3xl font-bold mb-2" style={{ color: customization.primaryColor }}>
                {stats.totalProjects}
              </div>
              <div className="text-sm text-gray-600">Total Projects</div>
            </div>
            <div className="bg-white rounded-xl shadow-lg p-6 text-center">
              <div className="text-3xl font-bold mb-2" style={{ color: customization.primaryColor }}>
                {stats.completedProjects}
              </div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="bg-white rounded-xl shadow-lg p-6 text-center">
              <div className="text-3xl font-bold mb-2" style={{ color: customization.primaryColor }}>
                {stats.happyClients}
              </div>
              <div className="text-sm text-gray-600">Happy Clients</div>
            </div>
            <div className="bg-white rounded-xl shadow-lg p-6 text-center">
              <div className="text-3xl font-bold mb-2" style={{ color: customization.primaryColor }}>
                {stats.yearsExperience}
              </div>
              <div className="text-sm text-gray-600">Years Experience</div>
            </div>
          </div>
        )}

        {/* Skills */}
        {customization.showSkills && (
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Skills & Technologies</h2>
            <div className="flex flex-wrap justify-center gap-4">
              {freelancer.skills.map((skill, index) => (
                <span
                  key={index}
                  className="px-6 py-3 rounded-full text-white font-medium shadow-lg"
                  style={{ backgroundColor: customization.primaryColor }}
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Featured Projects */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Featured Projects</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {projects.map((project, index) => (
              <div key={project.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                {/* Project Image */}
                {project.images && project.images.length > 0 && (
                  <div className="relative h-48">
                    <Image
                      src={project.images[0]}
                      alt={project.name}
                      fill
                      className="object-cover"
                    />
                    <div 
                      className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-60"
                    ></div>
                    <div className="absolute bottom-4 left-4 text-white">
                      <h3 className="text-xl font-bold">{project.name}</h3>
                      <p className="text-sm opacity-90">{project.client_name}</p>
                    </div>
                  </div>
                )}

                <div className="p-6">
                  {!project.images?.length && (
                    <div className="mb-4">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{project.name}</h3>
                      <p className="text-gray-600">Client: {project.client_name}</p>
                    </div>
                  )}

                  <p className="text-gray-700 mb-4">{project.description}</p>

                  {customization.showProjectDetails && (
                    <>
                      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                        <div className="flex items-center text-gray-600">
                          <CalendarIcon className="h-4 w-4 mr-2" />
                          {formatDate(project.start_date)} - {formatDate(project.end_date)}
                        </div>
                        <div className="flex items-center text-gray-600">
                          <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                          {formatCurrency(project.budget)}
                        </div>
                      </div>

                      {/* Technologies */}
                      <div className="mb-4">
                        <div className="flex flex-wrap gap-2">
                          {project.technologies?.map((tech: string, techIndex: number) => (
                            <span
                              key={techIndex}
                              className="px-3 py-1 text-xs rounded-full text-white"
                              style={{ backgroundColor: customization.secondaryColor }}
                            >
                              {tech}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Highlights */}
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Key Achievements:</h4>
                        <ul className="text-sm text-gray-700 space-y-1">
                          {project.highlights?.map((highlight: string, highlightIndex: number) => (
                            <li key={highlightIndex} className="flex items-start">
                              <span style={{ color: customization.primaryColor }} className="mr-2">•</span>
                              {highlight}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </>
                  )}

                  {/* Client Testimonial */}
                  {customization.showClientTestimonials && project.testimonial && (
                    <div 
                      className="rounded-lg p-4 mt-4"
                      style={{ backgroundColor: `${customization.primaryColor}10` }}
                    >
                      <div className="flex items-start">
                        <div className="flex text-yellow-400 mr-3 mt-1">
                          {[...Array(5)].map((_, i) => (
                            <StarIcon key={i} className="h-4 w-4 fill-current" />
                          ))}
                        </div>
                        <div className="flex-1">
                          <p className="text-gray-700 italic mb-2">"{project.testimonial.text}"</p>
                          <div className="text-sm">
                            <p className="font-medium text-gray-900">{project.testimonial.author}</p>
                            <p className="text-gray-600">{project.testimonial.position}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Contact CTA */}
        {customization.showContactInfo && (
          <div 
            className="rounded-xl p-8 text-center text-white"
            style={{
              background: `linear-gradient(135deg, ${customization.primaryColor}, ${customization.secondaryColor})`
            }}
          >
            <h2 className="text-3xl font-bold mb-4">Ready to Work Together?</h2>
            <p className="text-lg mb-6 max-w-2xl mx-auto opacity-90">
              I'm always excited to take on new challenges and help bring your ideas to life. 
              Let's discuss how we can work together on your next project.
            </p>
            <a
              href={`mailto:${freelancer.email}?subject=Project Inquiry&body=Hi ${freelancer.displayName}, I saw your portfolio and would like to discuss a potential project.`}
              className="inline-flex items-center px-8 py-4 bg-white text-gray-900 rounded-lg font-semibold hover:bg-gray-100 transition-colors shadow-lg"
            >
              <EnvelopeIcon className="h-5 w-5 mr-2" />
              Get In Touch
            </a>
          </div>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-600 text-sm">
            Portfolio powered by <span className="font-semibold" style={{ color: customization.primaryColor }}>KaiNote</span>
          </p>
        </div>
      </footer>
    </div>
  );
}
