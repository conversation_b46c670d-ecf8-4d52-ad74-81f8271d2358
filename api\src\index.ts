import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
// import smartSchedulingRoutes from './routes/smartScheduling';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3003; // API port

// Middleware
app.use(helmet());
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:3002',
    process.env.FRONTEND_URL
  ].filter(Boolean),
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Simple auth middleware for demo
const demoAuth = (req: any, res: any, next: any) => {
  req.user = {
    id: 'demo-user-id',
    userId: 'demo-user-id',
    email: '<EMAIL>',
    name: 'Demo User',
    subscription_tier: 'pro'
  };
  next();
};

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Welcome to KaiNote API!',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      auth: '/api/auth/*',
      projects: '/api/projects',
      meetings: '/api/meetings'
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'KaiNote API is running!',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Demo authentication endpoints
app.post('/api/auth/signin', (req, res) => {
  const { email, password } = req.body;

  // Demo authentication - accept any email/password
  const demoUser = {
    id: 'demo-user-id',
    email: email || '<EMAIL>',
    name: 'Demo User',
    subscription_tier: 'pro'
  };

  const demoToken = 'demo-token-' + Date.now();

  res.json({
    success: true,
    data: {
      user: demoUser,
      token: demoToken
    }
  });
});

app.post('/api/auth/signup', (req, res) => {
  const { email, password, name } = req.body;

  // Demo signup - accept any details
  const demoUser = {
    id: 'demo-user-id',
    email: email || '<EMAIL>',
    name: name || 'Demo User',
    subscription_tier: 'pro'
  };

  const demoToken = 'demo-token-' + Date.now();

  res.json({
    success: true,
    data: {
      user: demoUser,
      token: demoToken
    }
  });
});

app.post('/api/auth/verify', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: 'No token provided'
    });
  }

  // Demo verification - accept any token that starts with 'demo-token'
  const token = authHeader.substring(7);
  if (token.startsWith('demo-token')) {
    const demoUser = {
      id: 'demo-user-id',
      email: '<EMAIL>',
      name: 'Demo User',
      subscription_tier: 'pro'
    };

    res.json({
      success: true,
      data: {
        user: demoUser
      }
    });
  } else {
    res.status(401).json({
      success: false,
      error: 'Invalid token'
    });
  }
});

// Demo API endpoints
app.get('/api/projects', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        name: 'Demo Project 1',
        description: 'A sample project for demonstration',
        client_name: 'Demo Client',
        status: 'active',
        created_at: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Demo Project 2',
        description: 'Another sample project',
        client_name: 'Another Client',
        status: 'active',
        created_at: new Date().toISOString()
      }
    ]
  });
});

app.post('/api/projects', demoAuth, (req, res) => {
  const { name, client_name, description, status } = req.body;

  if (!name) {
    return res.status(400).json({
      success: false,
      error: 'Project name is required'
    });
  }

  // Create demo project
  const newProject = {
    id: 'demo-project-' + Date.now(),
    name,
    client_name: client_name || 'Demo Client',
    description: description || '',
    status: status || 'active',
    created_at: new Date().toISOString(),
    user_id: 'demo-user-id'
  };

  res.status(201).json({
    success: true,
    data: newProject,
    message: 'Project created successfully'
  });
});

app.get('/api/projects/:id', demoAuth, (req, res) => {
  const projectId = req.params.id;

  // Return demo project data
  const demoProject = {
    id: projectId,
    name: 'Demo Project Details',
    client_name: 'Demo Client',
    description: 'This is a demo project for testing purposes',
    status: 'active',
    created_at: new Date().toISOString(),
    user_id: 'demo-user-id',
    meetings: [],
    tasks: [],
    documents: [],
    invoices: []
  };

  res.json({
    success: true,
    data: demoProject
  });
});

app.get('/api/projects/:id/meetings', demoAuth, (req, res) => {
  const projectId = req.params.id;

  // Return demo meetings for this project
  const demoMeetings = [
    {
      id: 'demo-meeting-1',
      project_id: projectId,
      title: 'Project Kickoff Meeting',
      recorded_at: new Date().toISOString(),
      duration_minutes: 45,
      transcription_status: 'completed',
      status: 'completed',
      platform: 'zoom',
      summary: 'We discussed the project scope, timeline, and deliverables. The client confirmed their requirements and we aligned on the next steps.',
      transcript: 'Speaker 1: Welcome everyone to our project kickoff meeting...\nSpeaker 2: Thank you for having us. We\'re excited to get started...',
      client_summary: 'Hi there!\n\nI wanted to share a quick summary of our kickoff meeting today.\n\n**Key Points Discussed:**\n- Project scope and requirements\n- Timeline and milestones\n- Communication preferences\n- Next steps\n\n**Action Items:**\n- Finalize design mockups by Friday\n- Set up weekly check-in meetings\n- Share project documentation\n\nLooking forward to working together!\n\nBest regards,\nDemo Freelancer'
    },
    {
      id: 'demo-meeting-2',
      project_id: projectId,
      title: 'Weekly Check-in',
      recorded_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      duration_minutes: 30,
      transcription_status: 'completed',
      status: 'completed',
      platform: 'teams',
      summary: 'Weekly progress review. Discussed completed tasks, upcoming milestones, and addressed client feedback.',
      transcript: 'Speaker 1: Let\'s review the progress from this week...\nSpeaker 2: We\'ve completed the initial designs and are ready for feedback...',
      client_summary: 'Hi!\n\nHere\'s a summary of our weekly check-in:\n\n**Progress This Week:**\n- Completed initial design phase\n- Implemented user authentication\n- Set up development environment\n\n**Next Week:**\n- Client feedback review\n- Begin development phase\n- Schedule user testing\n\nEverything is on track for our timeline!\n\nBest,\nDemo Freelancer'
    }
  ];

  res.json({
    success: true,
    data: demoMeetings
  });
});

// Individual meeting details
app.get('/api/meetings/:id', demoAuth, (req, res) => {
  const meetingId = req.params.id;

  const demoMeeting = {
    id: meetingId,
    title: meetingId === 'demo-meeting-1' ? 'Project Kickoff Meeting' : 'Weekly Check-in',
    platform: meetingId === 'demo-meeting-1' ? 'zoom' : 'teams',
    duration_minutes: meetingId === 'demo-meeting-1' ? 45 : 30,
    recorded_at: meetingId === 'demo-meeting-1'
      ? new Date().toISOString()
      : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    transcription_status: 'completed',
    audio_url: meetingId === 'demo-meeting-1'
      ? 'https://www2.cs.uic.edu/~i101/SoundFiles/BabyElephantWalk60.wav'
      : 'https://www2.cs.uic.edu/~i101/SoundFiles/CantinaBand60.wav',
    audio_duration: meetingId === 'demo-meeting-1' ? 2700 : 1800, // duration in seconds
    summary: meetingId === 'demo-meeting-1'
      ? 'We discussed the project scope, timeline, and deliverables. The client confirmed their requirements and we aligned on the next steps. Key decisions were made regarding the technology stack and design approach.'
      : 'Weekly progress review. Discussed completed tasks, upcoming milestones, and addressed client feedback. The project is progressing well and on schedule.',
    transcript: meetingId === 'demo-meeting-1'
      ? `[00:00] Demo Freelancer: Welcome everyone to our project kickoff meeting. I'm excited to get started on this project with you.

[00:15] Client: Thank you for having us. We're really looking forward to working together.

[00:30] Demo Freelancer: Let's start by reviewing the project scope. Based on our initial discussions, we're building a web application with user authentication, dashboard, and reporting features.

[01:00] Client: That's correct. We also want to make sure it's mobile-responsive and has good performance.

[01:30] Demo Freelancer: Absolutely. I'll be using React and Node.js for this project, which will ensure both performance and scalability.

[02:00] Client: Sounds great. What about the timeline?

[02:15] Demo Freelancer: I estimate 8-10 weeks for the complete project. We'll have weekly check-ins to review progress and gather feedback.

[02:45] Client: Perfect. When can we see the first designs?

[03:00] Demo Freelancer: I'll have initial mockups ready by Friday. This will give us a good foundation to build upon.

[03:30] Client: Excellent. We're excited to see what you come up with.

[04:00] Demo Freelancer: Great! I'll send over the project documentation and set up our communication channels.`
      : `[00:00] Demo Freelancer: Good morning! Let's review the progress from this week.

[00:15] Client: Morning! We're eager to see what you've accomplished.

[00:30] Demo Freelancer: I've completed the initial design phase and implemented the user authentication system. Everything is working smoothly.

[01:00] Client: That's fantastic. Can we see a demo?

[01:15] Demo Freelancer: Absolutely. Let me share my screen... As you can see, users can now register, login, and access their dashboard.

[02:00] Client: This looks great! The design is exactly what we envisioned.

[02:30] Demo Freelancer: I'm glad you like it. Next week, I'll focus on the reporting features and mobile responsiveness.

[03:00] Client: Perfect. Any blockers or concerns?

[03:15] Demo Freelancer: None at the moment. Everything is progressing according to schedule.

[03:45] Client: Excellent. We'll review the current build and provide feedback by Wednesday.

[04:00] Demo Freelancer: Sounds good. I'll send you the staging link after this call.`,
    client_summary: meetingId === 'demo-meeting-1'
      ? `Hi there!

I wanted to share a quick summary of our kickoff meeting today.

**Key Points Discussed:**
• Project scope: Web application with user authentication, dashboard, and reporting
• Technology stack: React and Node.js for optimal performance
• Timeline: 8-10 weeks with weekly check-ins
• Mobile-responsive design with focus on performance

**Action Items:**
• Initial design mockups by Friday
• Set up weekly check-in meetings (Fridays at 10 AM)
• Share project documentation and communication channels
• Client to review and provide feedback on designs

**Next Steps:**
I'll be working on the initial designs this week and will have mockups ready for your review by Friday. This will give us a solid foundation to build upon.

Looking forward to working together on this exciting project!

Best regards,
Demo Freelancer`
      : `Hi!

Here's a summary of our weekly check-in:

**Progress This Week:**
• ✅ Completed initial design phase
• ✅ Implemented user authentication system
• ✅ Set up development environment and staging server
• ✅ Created user registration and login functionality

**What's Working Well:**
The authentication system is robust and secure. Users can successfully register, login, and access their personalized dashboard.

**Next Week's Focus:**
• Implement reporting features
• Add mobile responsiveness
• Conduct initial user testing
• Prepare for client feedback integration

**Action Items:**
• Client to review staging environment by Wednesday
• Provide feedback on current functionality
• Schedule user testing session for next week

Everything is progressing smoothly and we're on track with our timeline!

Best,
Demo Freelancer`,
    project: {
      id: 'demo-project-1',
      name: 'Demo Website Project',
      client_name: 'Demo Client',
      client_email: '<EMAIL>'
    }
  };

  res.json({
    success: true,
    data: demoMeeting
  });
});

// Meeting action items
app.get('/api/meetings/:id/action-items', demoAuth, (req, res) => {
  const meetingId = req.params.id;

  const demoActionItems = meetingId === 'demo-meeting-1' ? [
    {
      id: 'action-1',
      meeting_id: meetingId,
      description: 'Create initial design mockups for the web application',
      assigned_to: 'Demo Freelancer',
      due_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      priority: 'high',
      status: 'completed',
      created_at: new Date().toISOString()
    },
    {
      id: 'action-2',
      meeting_id: meetingId,
      description: 'Set up weekly check-in meetings',
      assigned_to: 'Demo Freelancer',
      due_date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
      priority: 'medium',
      status: 'completed',
      created_at: new Date().toISOString()
    },
    {
      id: 'action-3',
      meeting_id: meetingId,
      description: 'Share project documentation with client',
      assigned_to: 'Demo Freelancer',
      due_date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
      priority: 'medium',
      status: 'completed',
      created_at: new Date().toISOString()
    }
  ] : [
    {
      id: 'action-4',
      meeting_id: meetingId,
      description: 'Review staging environment and provide feedback',
      assigned_to: 'Demo Client',
      due_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      priority: 'high',
      status: 'pending',
      created_at: new Date().toISOString()
    },
    {
      id: 'action-5',
      meeting_id: meetingId,
      description: 'Implement reporting features',
      assigned_to: 'Demo Freelancer',
      due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      priority: 'high',
      status: 'in_progress',
      created_at: new Date().toISOString()
    },
    {
      id: 'action-6',
      meeting_id: meetingId,
      description: 'Schedule user testing session',
      assigned_to: 'Demo Freelancer',
      due_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      priority: 'medium',
      status: 'pending',
      created_at: new Date().toISOString()
    }
  ];

  res.json({
    success: true,
    data: demoActionItems
  });
});

// Generate client summary
app.post('/api/meetings/:id/generate-client-summary', demoAuth, (req, res) => {
  const meetingId = req.params.id;

  // Simulate AI generation delay
  setTimeout(() => {
    const clientSummary = meetingId === 'demo-meeting-1'
      ? `Hi there!

I wanted to share a quick summary of our kickoff meeting today.

**Key Points Discussed:**
• Project scope: Web application with user authentication, dashboard, and reporting
• Technology stack: React and Node.js for optimal performance
• Timeline: 8-10 weeks with weekly check-ins
• Mobile-responsive design with focus on performance

**Action Items:**
• Initial design mockups by Friday
• Set up weekly check-in meetings (Fridays at 10 AM)
• Share project documentation and communication channels
• Client to review and provide feedback on designs

**Next Steps:**
I'll be working on the initial designs this week and will have mockups ready for your review by Friday. This will give us a solid foundation to build upon.

Looking forward to working together on this exciting project!

Best regards,
Demo Freelancer`
      : `Hi!

Here's a summary of our weekly check-in:

**Progress This Week:**
• ✅ Completed initial design phase
• ✅ Implemented user authentication system
• ✅ Set up development environment and staging server
• ✅ Created user registration and login functionality

**What's Working Well:**
The authentication system is robust and secure. Users can successfully register, login, and access their personalized dashboard.

**Next Week's Focus:**
• Implement reporting features
• Add mobile responsiveness
• Conduct initial user testing
• Prepare for client feedback integration

**Action Items:**
• Client to review staging environment by Wednesday
• Provide feedback on current functionality
• Schedule user testing session for next week

Everything is progressing smoothly and we're on track with our timeline!

Best,
Demo Freelancer`;

    res.json({
      success: true,
      data: {
        client_summary: clientSummary
      },
      message: 'Client summary generated successfully'
    });
  }, 2000); // 2 second delay to simulate AI processing
});

// Generate shareable link
app.post('/api/meetings/:id/generate-shareable-link', demoAuth, (req, res) => {
  const meetingId = req.params.id;
  const token = 'demo-share-token-' + Date.now();
  const shareableLink = `${req.protocol}://${req.get('host')}/shared/meeting/${meetingId}?token=${token}`;

  res.json({
    success: true,
    data: {
      link: shareableLink,
      token: token,
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
    },
    message: 'Shareable link generated successfully'
  });
});

// Send client email
app.post('/api/meetings/:id/send-client-email', demoAuth, (req, res) => {
  const meetingId = req.params.id;

  // Simulate email sending delay
  setTimeout(() => {
    res.json({
      success: true,
      message: 'Email sent successfully to client'
    });
  }, 1500);
});

// Shared meeting endpoint (no auth required)
app.get('/api/meetings/shared/:id', (req, res) => {
  const meetingId = req.params.id;
  const token = req.query.token;

  if (!token) {
    return res.status(400).json({
      success: false,
      error: 'Access token is required'
    });
  }

  const sharedMeetingData = {
    meeting: {
      id: meetingId,
      title: meetingId === 'demo-meeting-1' ? 'Project Kickoff Meeting' : 'Weekly Check-in',
      recorded_at: meetingId === 'demo-meeting-1'
        ? new Date().toISOString()
        : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      duration_minutes: meetingId === 'demo-meeting-1' ? 45 : 30,
      client_summary: meetingId === 'demo-meeting-1'
        ? `Hi John,

Thanks for the productive meeting today! Here's a quick summary of what we discussed and the next steps:

**Meeting Overview:**
- Project: Website Redesign Kickoff
- Duration: 45 minutes
- Attendees: John Smith, Sarah Johnson, and myself

**Key Decisions:**
- Focus areas: Visual identity, user flow, and mobile responsiveness
- Timeline: Wireframes by Friday, design mockups the following week
- Additional features pricing needed

**My Deliverables:**
- Wireframes for homepage (Due: Friday, Jan 18th)
- Send pricing proposal (Due: Tuesday, Jan 16th)
- Schedule follow-up meeting (Due: Wednesday, Jan 17th)

Looking forward to bringing your vision to life!

Best regards,
Sarah`
        : `Hi John,

Here's a summary of our weekly check-in:

**Progress This Week:**
• ✅ Completed wireframes and initial design concepts
• ✅ Finalized user flow architecture
• ✅ Set up development environment
• ✅ Created responsive navigation system

**What's Working Well:**
The new design direction is resonating well with your brand values. The mobile-first approach is showing great results in early testing.

**Next Week's Focus:**
• Complete homepage design mockups
• Begin development of key pages
• Conduct user experience testing
• Prepare staging environment for review

**Action Items:**
• Review design mockups by Wednesday
• Provide feedback on visual direction
• Schedule user testing session

Everything is on track and looking great!

Best,
Sarah`
    },
    project: {
      id: 'demo-project-1',
      name: 'Demo Website Project',
      client_name: 'Demo Client'
    },
    freelancer: {
      name: 'Demo Freelancer'
    }
  };

  res.json({
    success: true,
    data: sharedMeetingData
  });
});

// Request access to project (no auth required)
app.post('/api/meetings/shared/:id/request-access', (req, res) => {
  const meetingId = req.params.id;
  const token = req.query.token;
  const { name, email, message } = req.body;

  if (!token) {
    return res.status(400).json({
      success: false,
      error: 'Access token is required'
    });
  }

  if (!name || !email) {
    return res.status(400).json({
      success: false,
      error: 'Name and email are required'
    });
  }

  // Simulate processing delay
  setTimeout(() => {
    res.json({
      success: true,
      message: 'Access request sent successfully'
    });
  }, 1000);
});

app.get('/api/meetings', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        title: 'Demo Meeting 1',
        project_id: '1',
        recorded_at: new Date().toISOString(),
        duration_minutes: 30,
        status: 'completed'
      }
    ]
  });
});

app.get('/api/time-tracking/analytics', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      summary: {
        totalHours: 40,
        billableHours: 35,
        totalRevenue: 2625,
        averageHourlyRate: 75
      }
    }
  });
});

app.get('/api/expenses/analytics', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      summary: {
        totalAmount: 1250,
        taxDeductibleAmount: 1000,
        billableAmount: 500,
        averageExpense: 125
      }
    }
  });
});

app.get('/api/clients/analytics', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      totalClients: 5,
      statusBreakdown: {
        active: 3,
        prospect: 2
      },
      recentCommunications: []
    }
  });
});

app.get('/api/financial/dashboard', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      summary: {
        totalRevenue: 5000,
        totalExpenses: 1250,
        grossProfit: 3750,
        profitMargin: 75,
        outstandingAmount: 2000,
        taxDeductibleExpenses: 1000
      },
      breakdown: {
        invoiceRevenue: 3000,
        timeRevenue: 2000,
        revenueByClient: [],
        expensesByCategory: []
      },
      trends: {
        monthly: []
      },
      outstanding: {
        invoices: 2,
        amount: 2000
      }
    }
  });
});

app.get('/api/automation/dashboard', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      summary: {
        totalRules: 3,
        activeRules: 2,
        activeWorkflows: 1,
        upcomingTasks: 2,
        pendingFollowups: 1
      },
      activeWorkflows: [],
      upcomingTasks: [],
      pendingFollowups: [],
      recentExecutions: []
    }
  });
});

// Invoice endpoints
app.get('/api/invoices/:id', (req, res) => {
  const { id } = req.params;

  // Demo invoice data
  const demoInvoice = {
    id: id,
    invoice_number: `INV-${id.toUpperCase()}`,
    project: {
      id: 'demo-project-1',
      name: 'Website Redesign',
      client_name: 'Acme Corp'
    },
    amount: 2500.00,
    currency: 'USD',
    status: 'pending',
    due_date: '2024-02-15T00:00:00Z',
    created_at: '2024-01-15T10:00:00Z',
    notes: 'Payment terms: Net 30 days. Late fees may apply after due date.'
  };

  res.json({
    success: true,
    data: demoInvoice
  });
});

app.put('/api/invoices/:id', (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  // In a real app, you would update the invoice in the database
  console.log(`Updating invoice ${id}:`, updateData);

  res.json({
    success: true,
    message: 'Invoice updated successfully',
    data: {
      id: id,
      ...updateData,
      updated_at: new Date().toISOString()
    }
  });
});

app.delete('/api/invoices/:id', (req, res) => {
  const { id } = req.params;

  // In a real app, you would delete the invoice from the database
  console.log(`Deleting invoice ${id}`);

  res.json({
    success: true,
    message: 'Invoice deleted successfully'
  });
});

app.get('/api/projects/:id/invoices', (req, res) => {
  const { id } = req.params;

  // Demo invoices for the project
  const demoInvoices = [
    {
      id: 'invoice-1',
      invoice_number: 'INV-001',
      amount: 2500.00,
      currency: 'USD',
      status: 'pending',
      due_date: '2024-02-15T00:00:00Z',
      created_at: '2024-01-15T10:00:00Z'
    },
    {
      id: 'invoice-2',
      invoice_number: 'INV-002',
      amount: 1800.00,
      currency: 'USD',
      status: 'paid',
      due_date: '2024-01-30T00:00:00Z',
      created_at: '2024-01-01T10:00:00Z'
    }
  ];

  res.json({
    success: true,
    data: demoInvoices
  });
});

app.post('/api/projects/:id/invoices', (req, res) => {
  const { id } = req.params;
  const invoiceData = req.body;

  // In a real app, you would create the invoice in the database
  const newInvoice = {
    id: `invoice-${Date.now()}`,
    project_id: id,
    ...invoiceData,
    status: 'draft',
    created_at: new Date().toISOString()
  };

  console.log(`Creating invoice for project ${id}:`, newInvoice);

  res.json({
    success: true,
    message: 'Invoice created successfully',
    data: newInvoice
  });
});

// Smart Scheduling and Automation Routes (commented out due to missing dependencies)
// app.use('/api/smart-scheduling', smartSchedulingRoutes);

// Demo smart scheduling endpoints for immediate functionality
app.get('/api/smart-scheduling/automation-rules', demoAuth, (req, res) => {
  const demoRules = [
    {
      id: 'rule-1',
      rule_name: 'Task Completion Notification',
      rule_type: 'task_completion',
      is_active: true,
      execution_count: 15,
      success_count: 14,
      failure_count: 1,
      last_executed: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      trigger_conditions: { task_status: 'completed' },
      actions: [
        {
          type: 'send_client_email',
          subject: 'Task Completed: {{task_title}}',
          content: 'Hi {{client_name}}, I\'ve completed the task "{{task_title}}" for your project.'
        }
      ]
    },
    {
      id: 'rule-2',
      rule_name: 'Deadline Reminder',
      rule_type: 'deadline_approaching',
      is_active: true,
      execution_count: 8,
      success_count: 8,
      failure_count: 0,
      last_executed: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      trigger_conditions: { days_until_deadline: 3 },
      actions: [
        {
          type: 'send_client_email',
          subject: 'Upcoming Deadline: {{task_title}}',
          content: 'Hi {{client_name}}, just a reminder that {{task_title}} is due in 3 days.'
        }
      ]
    },
    {
      id: 'rule-3',
      rule_name: 'Milestone Achievement',
      rule_type: 'milestone_reached',
      is_active: true,
      execution_count: 5,
      success_count: 5,
      failure_count: 0,
      last_executed: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      trigger_conditions: { milestone_percentage: 50 },
      actions: [
        {
          type: 'send_client_email',
          subject: 'Milestone Reached: {{milestone_name}}',
          content: 'Great news! We\'ve reached the {{milestone_name}} milestone in your project.'
        },
        {
          type: 'create_task',
          title: 'Review milestone deliverables',
          description: 'Review and document milestone completion'
        }
      ]
    }
  ];

  res.json({
    success: true,
    data: demoRules
  });
});

app.get('/api/smart-scheduling/communications/scheduled', demoAuth, (req, res) => {
  const demoComms = [
    {
      id: 'comm-1',
      recipient_email: '<EMAIL>',
      subject: 'Weekly Project Update - Demo Project',
      scheduled_for: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
      status: 'scheduled',
      project: {
        name: 'Demo Website Project',
        client_name: 'Demo Client'
      },
      task: {
        title: 'Homepage Design'
      }
    },
    {
      id: 'comm-2',
      recipient_email: '<EMAIL>',
      subject: 'Task Completed: User Authentication System',
      scheduled_for: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      status: 'scheduled',
      project: {
        name: 'Demo Website Project',
        client_name: 'Demo Client'
      },
      task: {
        title: 'User Authentication System'
      }
    },
    {
      id: 'comm-3',
      recipient_email: '<EMAIL>',
      subject: 'Milestone Reached: Phase 1 Complete',
      scheduled_for: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      status: 'sent',
      project: {
        name: 'Demo Website Project',
        client_name: 'Demo Client'
      }
    }
  ];

  res.json({
    success: true,
    data: demoComms
  });
});

app.get('/api/smart-scheduling/automation-rules/executions', demoAuth, (req, res) => {
  const demoExecutions = [
    {
      id: 'exec-1',
      executed_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      execution_status: 'completed',
      rule: {
        rule_name: 'Task Completion Notification',
        rule_type: 'task_completion'
      }
    },
    {
      id: 'exec-2',
      executed_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      execution_status: 'completed',
      rule: {
        rule_name: 'Deadline Reminder',
        rule_type: 'deadline_approaching'
      }
    },
    {
      id: 'exec-3',
      executed_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      execution_status: 'failed',
      error_message: 'Client email not found',
      rule: {
        rule_name: 'Milestone Achievement',
        rule_type: 'milestone_reached'
      }
    },
    {
      id: 'exec-4',
      executed_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      execution_status: 'completed',
      rule: {
        rule_name: 'Task Completion Notification',
        rule_type: 'task_completion'
      }
    },
    {
      id: 'exec-5',
      executed_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      execution_status: 'completed',
      rule: {
        rule_name: 'Milestone Achievement',
        rule_type: 'milestone_reached'
      }
    }
  ];

  res.json({
    success: true,
    data: demoExecutions
  });
});

app.post('/api/smart-scheduling/automation-rules/execute', demoAuth, (req, res) => {
  // Simulate rule execution
  setTimeout(() => {
    res.json({
      success: true,
      data: [
        {
          id: 'exec-new',
          rule_id: 'rule-1',
          execution_status: 'completed',
          executed_at: new Date().toISOString()
        }
      ],
      message: 'Automation rules executed successfully'
    });
  }, 1000);
});

app.post('/api/smart-scheduling/communications/send', demoAuth, (req, res) => {
  // Simulate sending communication
  setTimeout(() => {
    res.json({
      success: true,
      message: 'Communication sent successfully'
    });
  }, 500);
});

// Catch all for undefined routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl
  });
});

// Error handling middleware
app.use((error: any, req: any, res: any, next: any) => {
  console.error('API Error:', error);
  
  res.status(error.statusCode || 500).json({
    success: false,
    message: error.message || 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// Start server
const portNumber = parseInt(PORT.toString(), 10);
app.listen(portNumber, '0.0.0.0', () => {
  console.log(`🚀 KaiNote API Server running on port ${portNumber}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 Health check: http://localhost:${portNumber}/health`);
  console.log(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:3001'}`);
});

export default app;
