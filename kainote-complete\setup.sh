#!/bin/bash

# KaiNote Setup Script
# This script sets up the complete KaiNote application

echo "🚀 Setting up KaiNote - Complete Freelancer Business Management Platform"
echo "=================================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Create directories if they don't exist
mkdir -p logs
mkdir -p uploads

# Setup Frontend
echo ""
echo "📦 Setting up Frontend..."
cd frontend

# Copy environment file if it doesn't exist
if [ ! -f .env.local ]; then
    cp .env.example .env.local
    echo "📝 Created .env.local from .env.example"
    echo "⚠️  Please update .env.local with your configuration"
fi

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install frontend dependencies"
    exit 1
fi

echo "✅ Frontend dependencies installed"

# Setup Backend
echo ""
echo "🔧 Setting up Backend..."
cd ../backend

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    cp .env.example .env
    echo "📝 Created .env from .env.example"
    echo "⚠️  Please update .env with your configuration"
fi

# Install backend dependencies
echo "📦 Installing backend dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install backend dependencies"
    exit 1
fi

echo "✅ Backend dependencies installed"

# Build TypeScript
echo "🔨 Building TypeScript..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Failed to build TypeScript"
    exit 1
fi

echo "✅ TypeScript built successfully"

# Go back to root directory
cd ..

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Update environment variables in frontend/.env.local and backend/.env"
echo "2. Set up your database (PostgreSQL or Supabase)"
echo "3. Run database migrations: cd backend && npm run db:migrate"
echo "4. Seed demo data: cd backend && npm run db:seed"
echo "5. Start the development servers:"
echo "   - Backend: cd backend && npm run dev"
echo "   - Frontend: cd frontend && npm run dev"
echo ""
echo "🌐 Access the application:"
echo "   - Frontend: http://localhost:3001"
echo "   - Backend API: http://localhost:3003"
echo "   - Health Check: http://localhost:3003/health"
echo ""
echo "📚 Documentation:"
echo "   - README.md for detailed setup instructions"
echo "   - docs/ folder for API and feature documentation"
echo ""
echo "🆘 Need help? Check the documentation or create an issue on GitHub"
