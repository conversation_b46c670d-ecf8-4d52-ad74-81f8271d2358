'use client';

import { useState, useEffect } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import {
  ServerIcon,
  CpuChipIcon,
  CircleStackIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  MicrophoneIcon,
  WifiIcon,
} from '@heroicons/react/24/outline';

interface SystemMetrics {
  server: {
    uptime: number;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
  };
  api: {
    responseTime: number;
    requestsPerMinute: number;
    errorRate: number;
    activeConnections: number;
  };
  transcription: {
    queueLength: number;
    processingTime: number;
    successRate: number;
    liveSessionsActive: number;
  };
  database: {
    connectionPool: number;
    queryTime: number;
    activeQueries: number;
    cacheHitRate: number;
  };
}

export default function AdminSystemPage() {
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    fetchSystemMetrics();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchSystemMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchSystemMetrics = async () => {
    try {
      const response = await fetch('/api/admin/system', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSystemMetrics(data.data);
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Error fetching system metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (value: number, thresholds: { warning: number; critical: number }, inverted = false) => {
    if (inverted) {
      if (value < thresholds.critical) return 'text-red-600 bg-red-100';
      if (value < thresholds.warning) return 'text-yellow-600 bg-yellow-100';
      return 'text-green-600 bg-green-100';
    } else {
      if (value > thresholds.critical) return 'text-red-600 bg-red-100';
      if (value > thresholds.warning) return 'text-yellow-600 bg-yellow-100';
      return 'text-green-600 bg-green-100';
    }
  };

  const getStatusIcon = (value: number, thresholds: { warning: number; critical: number }, inverted = false) => {
    const isGood = inverted ? value >= thresholds.warning : value <= thresholds.warning;
    return isGood ? (
      <CheckCircleIcon className="h-5 w-5 text-green-500" />
    ) : (
      <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
    );
  };

  const formatUptime = (uptime: number) => {
    const days = Math.floor(uptime / (24 * 60 * 60));
    const hours = Math.floor((uptime % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((uptime % (60 * 60)) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!systemMetrics) {
    return (
      <AdminLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Failed to load system metrics</h2>
            <button
              onClick={fetchSystemMetrics}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">System Monitor</h1>
              <p className="text-gray-600">Real-time system performance and health metrics</p>
            </div>
            
            <div className="text-right">
              <button
                onClick={fetchSystemMetrics}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 mb-2"
              >
                Refresh
              </button>
              <p className="text-sm text-gray-500">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </p>
            </div>
          </div>
        </div>

        {/* System Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-2">
                  <CheckCircleIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">System Status</p>
                  <p className="text-lg font-bold text-green-600">Operational</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-lg p-2">
                  <ClockIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">Uptime</p>
                  <p className="text-lg font-bold text-gray-900">{systemMetrics.server.uptime}%</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="bg-purple-100 rounded-lg p-2">
                  <WifiIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">Active Connections</p>
                  <p className="text-lg font-bold text-gray-900">{systemMetrics.api.activeConnections}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="bg-red-100 rounded-lg p-2">
                  <MicrophoneIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">Live Sessions</p>
                  <p className="text-lg font-bold text-gray-900">{systemMetrics.transcription.liveSessionsActive}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Server Metrics */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Server Performance</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <CpuChipIcon className="h-6 w-6 text-gray-600 mr-2" />
                  <span className="text-sm font-medium text-gray-600">CPU Usage</span>
                </div>
                {getStatusIcon(systemMetrics.server.cpuUsage, { warning: 70, critical: 90 })}
              </div>
              <div className="mb-2">
                <div className="flex justify-between text-sm">
                  <span>{systemMetrics.server.cpuUsage}%</span>
                  <span className="text-gray-500">100%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      systemMetrics.server.cpuUsage > 90 ? 'bg-red-500' :
                      systemMetrics.server.cpuUsage > 70 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${systemMetrics.server.cpuUsage}%` }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <CircleStackIcon className="h-6 w-6 text-gray-600 mr-2" />
                  <span className="text-sm font-medium text-gray-600">Memory Usage</span>
                </div>
                {getStatusIcon(systemMetrics.server.memoryUsage, { warning: 80, critical: 95 })}
              </div>
              <div className="mb-2">
                <div className="flex justify-between text-sm">
                  <span>{systemMetrics.server.memoryUsage}%</span>
                  <span className="text-gray-500">100%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      systemMetrics.server.memoryUsage > 95 ? 'bg-red-500' :
                      systemMetrics.server.memoryUsage > 80 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${systemMetrics.server.memoryUsage}%` }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <ServerIcon className="h-6 w-6 text-gray-600 mr-2" />
                  <span className="text-sm font-medium text-gray-600">Disk Usage</span>
                </div>
                {getStatusIcon(systemMetrics.server.diskUsage, { warning: 80, critical: 95 })}
              </div>
              <div className="mb-2">
                <div className="flex justify-between text-sm">
                  <span>{systemMetrics.server.diskUsage}%</span>
                  <span className="text-gray-500">100%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      systemMetrics.server.diskUsage > 95 ? 'bg-red-500' :
                      systemMetrics.server.diskUsage > 80 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${systemMetrics.server.diskUsage}%` }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <ClockIcon className="h-6 w-6 text-gray-600 mr-2" />
                  <span className="text-sm font-medium text-gray-600">Response Time</span>
                </div>
                {getStatusIcon(systemMetrics.api.responseTime, { warning: 200, critical: 500 })}
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">{systemMetrics.api.responseTime}ms</p>
                <p className="text-sm text-gray-500">Average response time</p>
              </div>
            </div>
          </div>
        </div>

        {/* API & Database Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">API Performance</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Requests per minute</span>
                <span className="text-sm font-medium text-gray-900">{systemMetrics.api.requestsPerMinute}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Error rate</span>
                <span className={`text-sm font-medium px-2 py-1 rounded-full ${getStatusColor(systemMetrics.api.errorRate, { warning: 1, critical: 5 })}`}>
                  {systemMetrics.api.errorRate}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Active connections</span>
                <span className="text-sm font-medium text-gray-900">{systemMetrics.api.activeConnections}</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Database Performance</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Connection pool usage</span>
                <span className="text-sm font-medium text-gray-900">{systemMetrics.database.connectionPool}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Average query time</span>
                <span className="text-sm font-medium text-gray-900">{systemMetrics.database.queryTime}ms</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Active queries</span>
                <span className="text-sm font-medium text-gray-900">{systemMetrics.database.activeQueries}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Cache hit rate</span>
                <span className={`text-sm font-medium px-2 py-1 rounded-full ${getStatusColor(systemMetrics.database.cacheHitRate, { warning: 90, critical: 80 }, true)}`}>
                  {systemMetrics.database.cacheHitRate}%
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Transcription Service Metrics */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Transcription Service</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="bg-purple-100 rounded-lg p-3 w-12 h-12 mx-auto mb-2">
                <MicrophoneIcon className="h-6 w-6 text-purple-600 mx-auto" />
              </div>
              <p className="text-sm text-gray-600 mb-1">Queue Length</p>
              <p className="text-xl font-bold text-gray-900">{systemMetrics.transcription.queueLength}</p>
            </div>

            <div className="text-center">
              <div className="bg-blue-100 rounded-lg p-3 w-12 h-12 mx-auto mb-2">
                <ClockIcon className="h-6 w-6 text-blue-600 mx-auto" />
              </div>
              <p className="text-sm text-gray-600 mb-1">Processing Time</p>
              <p className="text-xl font-bold text-gray-900">{systemMetrics.transcription.processingTime}s</p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 rounded-lg p-3 w-12 h-12 mx-auto mb-2">
                <CheckCircleIcon className="h-6 w-6 text-green-600 mx-auto" />
              </div>
              <p className="text-sm text-gray-600 mb-1">Success Rate</p>
              <p className="text-xl font-bold text-gray-900">{systemMetrics.transcription.successRate}%</p>
            </div>

            <div className="text-center">
              <div className="bg-red-100 rounded-lg p-3 w-12 h-12 mx-auto mb-2">
                <WifiIcon className="h-6 w-6 text-red-600 mx-auto" />
              </div>
              <p className="text-sm text-gray-600 mb-1">Live Sessions</p>
              <p className="text-xl font-bold text-gray-900">{systemMetrics.transcription.liveSessionsActive}</p>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
