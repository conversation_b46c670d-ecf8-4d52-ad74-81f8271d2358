import Stripe from 'stripe';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Stripe with secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_...', {
  apiVersion: '2025-05-28.basil',
});

// Stripe configuration
export const stripeConfig = {
  publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || 'pk_test_...',
  secretKey: process.env.STRIPE_SECRET_KEY || 'sk_test_...',
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || 'whsec_...',
  successUrl: process.env.FRONTEND_URL + '/billing/success',
  cancelUrl: process.env.FRONTEND_URL + '/billing/cancel',
};

// Subscription plans configuration
export const subscriptionPlans = {
  starter: {
    name: 'Starter',
    priceId: process.env.STRIPE_STARTER_PRICE_ID || 'price_starter',
    price: 0,
    currency: 'usd',
    interval: 'month',
    features: [
      '5 meetings per month',
      'Basic transcription',
      'Action item extraction',
      'Email reminders',
      'Client summaries'
    ]
  },
  professional: {
    name: 'Professional',
    priceId: process.env.STRIPE_PROFESSIONAL_PRICE_ID || 'price_professional',
    price: 1900, // $19.00 in cents
    currency: 'usd',
    interval: 'month',
    features: [
      'Unlimited meetings',
      'Meeting Bot automation',
      'Smart scheduling',
      'Financial dashboard',
      'Client management',
      'Time tracking',
      'AI document generation'
    ]
  },
  business: {
    name: 'Business',
    priceId: process.env.STRIPE_BUSINESS_PRICE_ID || 'price_business',
    price: 3900, // $39.00 in cents
    currency: 'usd',
    interval: 'month',
    features: [
      'Everything in Professional',
      'Advanced automation',
      'Custom integrations',
      'Priority support',
      'White-label client portals',
      'Advanced analytics',
      'API access'
    ]
  }
};

export default stripe;
