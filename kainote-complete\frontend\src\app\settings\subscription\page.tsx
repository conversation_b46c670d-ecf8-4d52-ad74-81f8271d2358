'use client';

import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { SubscriptionManager } from '@/components/subscription/SubscriptionManager';
import { 
  CreditCardIcon,
  DocumentTextIcon,
  CogIcon,
} from '@heroicons/react/24/outline';

export default function SubscriptionSettingsPage() {
  const handleUpgrade = () => {
    // Redirect to pricing page or open upgrade modal
    window.open('/pricing', '_blank');
  };

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="bg-primary-100 rounded-lg p-2">
              <CreditCardIcon className="h-6 w-6 text-primary-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Subscription & Billing</h1>
              <p className="text-gray-600">Manage your subscription, usage, and billing information</p>
            </div>
          </div>
        </div>

        {/* Subscription Manager */}
        <SubscriptionManager onUpgrade={handleUpgrade} />

        {/* Additional Settings */}
        <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Billing Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center space-x-3 mb-4">
              <DocumentTextIcon className="h-5 w-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Billing Information</h3>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Billing Email
                </label>
                <input
                  type="email"
                  value="<EMAIL>"
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Method
                </label>
                <div className="flex items-center space-x-3 p-3 border border-gray-300 rounded-lg">
                  <CreditCardIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-600">•••• •••• •••• 4242</span>
                  <span className="text-gray-500 text-sm">Expires 12/25</span>
                </div>
              </div>
              
              <button className="w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                Update Payment Method
              </button>
            </div>
          </div>

          {/* Subscription Settings */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center space-x-3 mb-4">
              <CogIcon className="h-5 w-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Subscription Settings</h3>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Auto-renewal</h4>
                  <p className="text-sm text-gray-600">Automatically renew your subscription</p>
                </div>
                <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary-600">
                  <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                </button>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Usage notifications</h4>
                  <p className="text-sm text-gray-600">Get notified when approaching limits</p>
                </div>
                <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary-600">
                  <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                </button>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Billing reminders</h4>
                  <p className="text-sm text-gray-600">Email reminders before billing</p>
                </div>
                <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary-600">
                  <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                </button>
              </div>
              
              <hr className="my-4" />
              
              <button className="w-full px-4 py-2 text-red-600 border border-red-300 rounded-lg hover:bg-red-50">
                Cancel Subscription
              </button>
            </div>
          </div>
        </div>

        {/* Recent Invoices */}
        <div className="mt-8 bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Invoices</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Professional Plan - December 2024</h4>
                  <p className="text-sm text-gray-600">Paid on Dec 1, 2024</p>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="font-semibold text-gray-900">$49.00</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Paid
                  </span>
                  <button className="text-primary-600 hover:text-primary-800 text-sm font-medium">
                    Download
                  </button>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Professional Plan - November 2024</h4>
                  <p className="text-sm text-gray-600">Paid on Nov 1, 2024</p>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="font-semibold text-gray-900">$49.00</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Paid
                  </span>
                  <button className="text-primary-600 hover:text-primary-800 text-sm font-medium">
                    Download
                  </button>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Professional Plan - October 2024</h4>
                  <p className="text-sm text-gray-600">Paid on Oct 1, 2024</p>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="font-semibold text-gray-900">$49.00</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Paid
                  </span>
                  <button className="text-primary-600 hover:text-primary-800 text-sm font-medium">
                    Download
                  </button>
                </div>
              </div>
            </div>
            
            <div className="mt-6 text-center">
              <button className="text-primary-600 hover:text-primary-800 font-medium">
                View All Invoices
              </button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
