'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import toast from 'react-hot-toast';
import { 
  PlusIcon, 
  ReceiptPercentIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  CalendarIcon,
  TagIcon,
  DocumentArrowUpIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

interface Expense {
  id: string;
  userId: string;
  projectId?: string;
  projectName?: string;
  description: string;
  amount: number;
  currency: string;
  category: string;
  date: string;
  isBillable: boolean;
  isReimbursable: boolean;
  status: string;
  receiptUrl?: string;
  tags: string[];
  createdAt: string;
}

interface ExpenseCategory {
  id: string;
  name: string;
  color: string;
  is_tax_deductible: boolean;
}

export default function ExpensesPage() {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewExpenseForm, setShowNewExpenseForm] = useState(false);
  const [newExpenseData, setNewExpenseData] = useState({
    projectId: '',
    description: '',
    amount: '',
    category: 'Software',
    date: new Date().toISOString().split('T')[0],
    isBillable: false,
    isReimbursable: false
  });

  useEffect(() => {
    fetchExpenses();
  }, []);

  const fetchExpenses = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/expenses`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setExpenses(data.data);
      }
    } catch (error) {
      console.error('Error fetching expenses:', error);
      toast.error('Failed to load expenses');
    } finally {
      setLoading(false);
    }
  };

  const createExpense = async () => {
    try {
      if (!newExpenseData.description || !newExpenseData.amount || !newExpenseData.category) {
        toast.error('Please fill in all required fields');
        return;
      }

      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/expenses`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          ...newExpenseData,
          amount: parseFloat(newExpenseData.amount)
        })
      });

      if (response.ok) {
        toast.success('Expense created!');
        setShowNewExpenseForm(false);
        setNewExpenseData({
          projectId: '',
          description: '',
          amount: '',
          category: 'Software',
          date: new Date().toISOString().split('T')[0],
          isBillable: false,
          isReimbursable: false
        });
        fetchExpenses();
      } else {
        throw new Error('Failed to create expense');
      }
    } catch (error) {
      console.error('Error creating expense:', error);
      toast.error('Failed to create expense');
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Expenses</h1>
            <p className="text-gray-600">Track and manage your business expenses</p>
          </div>
          <button
            onClick={() => setShowNewExpenseForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Expense
          </button>
        </div>

        {/* New Expense Form */}
        {showNewExpenseForm && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Expense</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                <input
                  type="text"
                  value={newExpenseData.description}
                  onChange={(e) => setNewExpenseData({ ...newExpenseData, description: e.target.value })}
                  placeholder="What did you spend money on?"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Amount *</label>
                <input
                  type="number"
                  step="0.01"
                  value={newExpenseData.amount}
                  onChange={(e) => setNewExpenseData({ ...newExpenseData, amount: e.target.value })}
                  placeholder="0.00"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                <select
                  value={newExpenseData.category}
                  onChange={(e) => setNewExpenseData({ ...newExpenseData, category: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Software">Software</option>
                  <option value="Hardware">Hardware</option>
                  <option value="Hosting">Hosting</option>
                  <option value="Assets">Assets</option>
                  <option value="Travel">Travel</option>
                  <option value="Office Supplies">Office Supplies</option>
                  <option value="Marketing">Marketing</option>
                  <option value="Education">Education</option>
                  <option value="Legal">Legal</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                <input
                  type="date"
                  value={newExpenseData.date}
                  onChange={(e) => setNewExpenseData({ ...newExpenseData, date: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="md:col-span-2">
                <div className="flex items-center space-x-6">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={newExpenseData.isBillable}
                      onChange={(e) => setNewExpenseData({ ...newExpenseData, isBillable: e.target.checked })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Billable to client</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={newExpenseData.isReimbursable}
                      onChange={(e) => setNewExpenseData({ ...newExpenseData, isReimbursable: e.target.checked })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Reimbursable</span>
                  </label>
                </div>
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={createExpense}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                Add Expense
              </button>
              <button
                onClick={() => setShowNewExpenseForm(false)}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* Expenses List */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Expenses</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {expenses.length === 0 ? (
              <div className="px-6 py-8 text-center">
                <ReceiptPercentIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No expenses yet</h3>
                <p className="text-gray-600">Start tracking your business expenses to see them here.</p>
              </div>
            ) : (
              expenses.map((expense) => (
                <div key={expense.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h4 className="text-sm font-medium text-gray-900">{expense.description}</h4>
                        <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          expense.status === 'approved' ? 'bg-green-100 text-green-800' :
                          expense.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {expense.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">{expense.category}</p>
                      {expense.projectName && (
                        <p className="text-sm text-gray-500">Project: {expense.projectName}</p>
                      )}
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <span>{new Date(expense.date).toLocaleDateString()}</span>
                        {expense.isBillable && (
                          <span className="ml-2 text-blue-600">Billable</span>
                        )}
                        {expense.isReimbursable && (
                          <span className="ml-2 text-green-600">Reimbursable</span>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        ${expense.amount.toFixed(2)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {expense.currency}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
    ['expenses', selectedPeriod, selectedCategory, selectedProject],
    () => apiHelpers.getExpenses({ 
      category_id: selectedCategory || undefined,
      project_id: selectedProject || undefined,
      limit: 50 
    }),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch expense categories
  const { data: categoriesResponse } = useQuery(
    'expense-categories',
    apiHelpers.getExpenseCategories,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch projects
  const { data: projectsResponse } = useQuery(
    'projects',
    apiHelpers.getProjects,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch analytics
  const { data: analyticsResponse } = useQuery(
    ['expense-analytics', selectedPeriod, selectedProject],
    () => apiHelpers.getExpenseAnalytics({ 
      period: selectedPeriod,
      project_id: selectedProject || undefined
    }),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  // Get category color
  const getCategoryColor = (categoryId?: string) => {
    const category = categories?.find(cat => cat.id === categoryId);
    return category?.color || '#6B7280';
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const expenses = Array.isArray(expensesResponse) ? expensesResponse : [];
  const categories = Array.isArray(categoriesResponse) ? categoriesResponse : [];
  const projects = Array.isArray(projectsResponse) ? projectsResponse : [];
  const analytics = analyticsResponse?.summary;

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Expense Tracking</h1>
              <p className="text-gray-600">Manage expenses and track deductibles</p>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value as any)}
                className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
              </select>
              <button
                onClick={() => setShowNewExpenseForm(true)}
                className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center space-x-2"
              >
                <PlusIcon className="h-5 w-5" />
                <span>Add Expense</span>
              </button>
            </div>
          </div>
        </div>

        {/* Analytics Cards */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-red-100 rounded-lg p-3">
                  <CurrencyDollarIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Expenses</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(analytics.totalAmount)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-3">
                  <ReceiptPercentIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Tax Deductible</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(analytics.taxDeductibleAmount)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-lg p-3">
                  <ChartBarIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Billable</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(analytics.billableAmount)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-purple-100 rounded-lg p-3">
                  <CalendarIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Avg Expense</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(analytics.averageExpense)}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="flex items-center space-x-4">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">All Categories</option>
                  {categories.map((category: ExpenseCategory) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Project</label>
                <select
                  value={selectedProject}
                  onChange={(e) => setSelectedProject(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">All Projects</option>
                  {projects.map((project: any) => (
                    <option key={project.id} value={project.id}>
                      {project.name} - {project.client_name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-end">
                <button
                  onClick={() => {
                    setSelectedCategory('');
                    setSelectedProject('');
                  }}
                  className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Expenses List */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Recent Expenses</h2>
          </div>
          
          {expensesLoading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            </div>
          ) : expenses.length === 0 ? (
            <div className="p-6 text-center">
              <ReceiptPercentIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No expenses yet</h3>
              <p className="mt-1 text-sm text-gray-500">Start tracking your business expenses.</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {expenses.map((expense: Expense) => (
                <div key={expense.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: getCategoryColor(expense.category_id) }}
                        ></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{expense.title}</p>
                          {expense.description && (
                            <p className="text-sm text-gray-600">{expense.description}</p>
                          )}
                          <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                            {expense.category && (
                              <span>{expense.category.name}</span>
                            )}
                            {expense.project && (
                              <span>{expense.project.name}</span>
                            )}
                            {expense.vendor && (
                              <span>• {expense.vendor}</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-right">
                        <p className="font-medium text-gray-900">
                          {formatCurrency(expense.amount, expense.currency)}
                        </p>
                        <p className="text-gray-500">
                          {format(new Date(expense.expense_date), 'MMM d, yyyy')}
                        </p>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {expense.is_tax_deductible && (
                          <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                            Tax Deductible
                          </span>
                        )}
                        {expense.is_billable && (
                          <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                            Billable
                          </span>
                        )}
                        {expense.receipt_url && (
                          <DocumentArrowUpIcon className="h-4 w-4 text-gray-400" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
