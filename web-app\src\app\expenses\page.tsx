'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import toast from 'react-hot-toast';
import { 
  PlusIcon, 
  ReceiptPercentIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  CalendarIcon,
  TagIcon,
  DocumentArrowUpIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

interface Expense {
  id: string;
  userId: string;
  projectId?: string;
  projectName?: string;
  description: string;
  amount: number;
  currency: string;
  category: string;
  date: string;
  isBillable: boolean;
  isReimbursable: boolean;
  status: string;
  receiptUrl?: string;
  tags: string[];
  createdAt: string;
}

interface ExpenseCategory {
  id: string;
  name: string;
  color: string;
  is_tax_deductible: boolean;
}

export default function ExpensesPage() {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewExpenseForm, setShowNewExpenseForm] = useState(false);
  const [newExpenseData, setNewExpenseData] = useState({
    projectId: '',
    description: '',
    amount: '',
    category: 'Software',
    date: new Date().toISOString().split('T')[0],
    isBillable: false,
    isReimbursable: false
  });

  useEffect(() => {
    fetchExpenses();
  }, []);

  const fetchExpenses = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/expenses`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setExpenses(data.data);
      }
    } catch (error) {
      console.error('Error fetching expenses:', error);
      toast.error('Failed to load expenses');
    } finally {
      setLoading(false);
    }
  };

  const createExpense = async () => {
    try {
      if (!newExpenseData.description || !newExpenseData.amount || !newExpenseData.category) {
        toast.error('Please fill in all required fields');
        return;
      }

      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/expenses`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          ...newExpenseData,
          amount: parseFloat(newExpenseData.amount)
        })
      });

      if (response.ok) {
        toast.success('Expense created!');
        setShowNewExpenseForm(false);
        setNewExpenseData({
          projectId: '',
          description: '',
          amount: '',
          category: 'Software',
          date: new Date().toISOString().split('T')[0],
          isBillable: false,
          isReimbursable: false
        });
        fetchExpenses();
      } else {
        throw new Error('Failed to create expense');
      }
    } catch (error) {
      console.error('Error creating expense:', error);
      toast.error('Failed to create expense');
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Expenses</h1>
            <p className="text-gray-600">Track and manage your business expenses</p>
          </div>
          <button
            onClick={() => setShowNewExpenseForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Expense
          </button>
        </div>

        {/* New Expense Form */}
        {showNewExpenseForm && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Expense</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                <input
                  type="text"
                  value={newExpenseData.description}
                  onChange={(e) => setNewExpenseData({ ...newExpenseData, description: e.target.value })}
                  placeholder="What did you spend money on?"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Amount *</label>
                <input
                  type="number"
                  step="0.01"
                  value={newExpenseData.amount}
                  onChange={(e) => setNewExpenseData({ ...newExpenseData, amount: e.target.value })}
                  placeholder="0.00"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                <select
                  value={newExpenseData.category}
                  onChange={(e) => setNewExpenseData({ ...newExpenseData, category: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Software">Software</option>
                  <option value="Hardware">Hardware</option>
                  <option value="Hosting">Hosting</option>
                  <option value="Assets">Assets</option>
                  <option value="Travel">Travel</option>
                  <option value="Office Supplies">Office Supplies</option>
                  <option value="Marketing">Marketing</option>
                  <option value="Education">Education</option>
                  <option value="Legal">Legal</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                <input
                  type="date"
                  value={newExpenseData.date}
                  onChange={(e) => setNewExpenseData({ ...newExpenseData, date: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="md:col-span-2">
                <div className="flex items-center space-x-6">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={newExpenseData.isBillable}
                      onChange={(e) => setNewExpenseData({ ...newExpenseData, isBillable: e.target.checked })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Billable to client</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={newExpenseData.isReimbursable}
                      onChange={(e) => setNewExpenseData({ ...newExpenseData, isReimbursable: e.target.checked })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Reimbursable</span>
                  </label>
                </div>
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={createExpense}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                Add Expense
              </button>
              <button
                onClick={() => setShowNewExpenseForm(false)}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* Expenses List */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Expenses</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {expenses.length === 0 ? (
              <div className="px-6 py-8 text-center">
                <ReceiptPercentIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No expenses yet</h3>
                <p className="text-gray-600">Start tracking your business expenses to see them here.</p>
              </div>
            ) : (
              expenses.map((expense) => (
                <div key={expense.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h4 className="text-sm font-medium text-gray-900">{expense.description}</h4>
                        <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          expense.status === 'approved' ? 'bg-green-100 text-green-800' :
                          expense.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {expense.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">{expense.category}</p>
                      {expense.projectName && (
                        <p className="text-sm text-gray-500">Project: {expense.projectName}</p>
                      )}
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <span>{new Date(expense.date).toLocaleDateString()}</span>
                        {expense.isBillable && (
                          <span className="ml-2 text-blue-600">Billable</span>
                        )}
                        {expense.isReimbursable && (
                          <span className="ml-2 text-green-600">Reimbursable</span>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        ${expense.amount.toFixed(2)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {expense.currency}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
