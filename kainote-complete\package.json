{"name": "kainote-complete", "version": "1.0.0", "description": "Complete Freelancer Business Management Platform", "private": true, "scripts": {"setup": "chmod +x setup.sh && ./setup.sh", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:frontend": "cd frontend && npm start", "start:backend": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "clean": "rm -rf frontend/node_modules backend/node_modules frontend/.next backend/dist", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "backend"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/kainote-complete.git"}, "keywords": ["freelancer", "business-management", "meeting-recording", "ai-transcription", "project-management", "time-tracking", "invoicing", "financial-analytics"], "author": "KaiNote Team", "license": "MIT"}