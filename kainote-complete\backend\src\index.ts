import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import rateLimit from 'express-rate-limit';

// Import routes
import authRoutes from './routes/auth';
import projectRoutes from './routes/projects';
import taskRoutes from './routes/tasks';
import meetingRoutes from './routes/meetings';
import timeTrackingRoutes from './routes/time-tracking';
import expenseRoutes from './routes/expenses';
import financialRoutes from './routes/financial';
import calendarRoutes from './routes/calendar';
import automationRoutes from './routes/automation';
import invoiceRoutes from './routes/invoices';
import clientRoutes from './routes/clients';
import portfolioRoutes from './routes/portfolio';
import aiRoutes from './routes/ai';

// Import middleware
import { errorHandler } from './middleware/errorHandler';
import { authMiddleware } from './middleware/auth';
import { logger } from './utils/logger';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3003;

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3001',
  credentials: true,
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(limiter);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/projects', authMiddleware, projectRoutes);
app.use('/api/tasks', authMiddleware, taskRoutes);
app.use('/api/meetings', authMiddleware, meetingRoutes);
app.use('/api/time-tracking', authMiddleware, timeTrackingRoutes);
app.use('/api/expenses', authMiddleware, expenseRoutes);
app.use('/api/financial', authMiddleware, financialRoutes);
app.use('/api/calendar', authMiddleware, calendarRoutes);
app.use('/api/smart-scheduling', authMiddleware, automationRoutes);
app.use('/api/invoices', authMiddleware, invoiceRoutes);
app.use('/api/clients', authMiddleware, clientRoutes);
app.use('/api/portfolio', authMiddleware, portfolioRoutes);
app.use('/api/ai', authMiddleware, aiRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl,
  });
});

// Error handling middleware
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`🚀 KaiNote API server running on port ${PORT}`);
  logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`🔗 Health check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
