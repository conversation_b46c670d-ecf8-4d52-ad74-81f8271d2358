'use client';

import { useState, useEffect } from 'react';
import { 
  EyeIcon,
  ShareIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  LinkIcon,
  GlobeAltIcon,
  UserIcon,
  BriefcaseIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface Project {
  id: string;
  name: string;
  client_name: string;
  status: string;
  end_date: string | null;
  budget: number;
  description: string;
  isSelected: boolean;
}

interface PortfolioSettings {
  id: string;
  isPublic: boolean;
  portfolioUrl: string;
  shareableLink: string;
  displayName: string;
  title: string;
  bio: string;
  selectedProjects: string[];
  customization: {
    template: string;
    primaryColor: string;
    secondaryColor: string;
    showContactInfo: boolean;
    showProjectDetails: boolean;
    showClientTestimonials: boolean;
    showSkills: boolean;
    showStats: boolean;
    layout: string;
  };
}

interface Template {
  id: string;
  name: string;
  description: string;
  preview: string;
  features: string[];
  primaryColor: string;
  secondaryColor: string;
}

export default function PortfolioManagePage() {
  const [portfolioSettings, setPortfolioSettings] = useState<PortfolioSettings | null>(null);
  const [availableProjects, setAvailableProjects] = useState<Project[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editingProfile, setEditingProfile] = useState(false);
  const [activeTab, setActiveTab] = useState('projects');
  const [profileForm, setProfileForm] = useState({
    displayName: '',
    title: '',
    bio: ''
  });

  useEffect(() => {
    fetchPortfolioData();
  }, []);

  const fetchPortfolioData = async () => {
    try {
      const token = localStorage.getItem('token') || 'demo-token';
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3003';

      console.log('Fetching portfolio data with token:', token);
      console.log('API URL:', apiUrl);

      // Fetch portfolio settings
      const settingsResponse = await fetch(`${apiUrl}/api/portfolio/settings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Settings response status:', settingsResponse.status);

      if (settingsResponse.ok) {
        const settingsData = await settingsResponse.json();
        console.log('Settings data:', settingsData);
        setPortfolioSettings(settingsData.data);
        setProfileForm({
          displayName: settingsData.data.displayName,
          title: settingsData.data.title,
          bio: settingsData.data.bio
        });
      } else {
        const errorText = await settingsResponse.text();
        console.error('Settings API error:', settingsResponse.status, errorText);
        throw new Error(`Settings API error: ${settingsResponse.status}`);
      }

      // Fetch available projects
      const projectsResponse = await fetch(`${apiUrl}/api/portfolio/available-projects`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Projects response status:', projectsResponse.status);

      if (projectsResponse.ok) {
        const projectsData = await projectsResponse.json();
        console.log('Projects data:', projectsData);
        setAvailableProjects(projectsData.data);
      } else {
        const errorText = await projectsResponse.text();
        console.error('Projects API error:', projectsResponse.status, errorText);
      }

      // Fetch available templates
      const templatesResponse = await fetch(`${apiUrl}/api/portfolio/templates`);

      console.log('Templates response status:', templatesResponse.status);

      if (templatesResponse.ok) {
        const templatesData = await templatesResponse.json();
        console.log('Templates data:', templatesData);
        setTemplates(templatesData.data);
      } else {
        const errorText = await templatesResponse.text();
        console.error('Templates API error:', templatesResponse.status, errorText);
      }

    } catch (error: any) {
      console.error('Error fetching portfolio data:', error);
      toast.error(`Failed to load portfolio data: ${error.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleProjectToggle = async (projectId: string) => {
    try {
      const updatedProjects = availableProjects.map(project => 
        project.id === projectId 
          ? { ...project, isSelected: !project.isSelected }
          : project
      );
      
      setAvailableProjects(updatedProjects);
      
      const selectedProjectIds = updatedProjects
        .filter(project => project.isSelected)
        .map(project => project.id);

      await updatePortfolioSettings({ selectedProjects: selectedProjectIds });
      
    } catch (error) {
      console.error('Error toggling project:', error);
      toast.error('Failed to update project selection');
    }
  };

  const handleProfileUpdate = async () => {
    try {
      setSaving(true);
      await updatePortfolioSettings(profileForm);
      setEditingProfile(false);
      toast.success('Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const updatePortfolioSettings = async (updates: any) => {
    try {
      const token = localStorage.getItem('token') || 'demo-token';
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3003';

      console.log('Updating portfolio settings:', updates);

      const response = await fetch(`${apiUrl}/api/portfolio/settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updates)
      });

      if (response.ok) {
        const data = await response.json();
        setPortfolioSettings(data.data);
        return data.data;
      } else {
        throw new Error('Failed to update settings');
      }
    } catch (error) {
      console.error('Error updating portfolio settings:', error);
      throw error;
    }
  };

  const handleTemplateSelect = async (templateId: string) => {
    try {
      const selectedTemplate = templates.find(t => t.id === templateId);
      if (!selectedTemplate) return;

      const customization = {
        ...portfolioSettings?.customization,
        template: templateId,
        primaryColor: selectedTemplate.primaryColor,
        secondaryColor: selectedTemplate.secondaryColor
      };

      await updatePortfolioSettings({ customization });
      toast.success(`Template changed to ${selectedTemplate.name}`);
    } catch (error) {
      console.error('Error changing template:', error);
      toast.error('Failed to change template');
    }
  };

  const handleTogglePublic = async () => {
    try {
      const newPublicStatus = !portfolioSettings?.isPublic;
      await updatePortfolioSettings({ isPublic: newPublicStatus });
      toast.success(newPublicStatus ? 'Portfolio is now public' : 'Portfolio is now private');
    } catch (error) {
      console.error('Error toggling portfolio visibility:', error);
      toast.error('Failed to update portfolio visibility');
    }
  };

  const copyShareableLink = () => {
    if (portfolioSettings?.shareableLink) {
      navigator.clipboard.writeText(portfolioSettings.shareableLink);
      toast.success('Portfolio link copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Portfolio Management</h1>
          <p className="mt-2 text-gray-600">Create and manage your professional portfolio to showcase your work</p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'projects', name: 'Projects', icon: BriefcaseIcon },
              { id: 'template', name: 'Template', icon: PencilIcon },
              { id: 'profile', name: 'Profile', icon: UserIcon }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-5 w-5 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Portfolio Settings */}
          <div className="lg:col-span-2 space-y-6">
            {/* Template Selection */}
            {activeTab === 'template' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Choose Template</h2>
                <p className="text-gray-600 mb-6">Select a template that best represents your professional style</p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {templates.map((template) => (
                    <div
                      key={template.id}
                      className={`border-2 rounded-lg p-4 cursor-pointer transition-all hover:shadow-md ${
                        portfolioSettings?.customization.template === template.id
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleTemplateSelect(template.id)}
                    >
                      <div className="aspect-video bg-gradient-to-br rounded-lg mb-4 flex items-center justify-center text-white font-bold text-lg"
                           style={{
                             background: `linear-gradient(135deg, ${template.primaryColor}, ${template.secondaryColor})`
                           }}>
                        {template.name}
                      </div>

                      <h3 className="font-semibold text-gray-900 mb-2">{template.name}</h3>
                      <p className="text-sm text-gray-600 mb-3">{template.description}</p>

                      <div className="space-y-2">
                        <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">Features:</h4>
                        <ul className="text-xs text-gray-600 space-y-1">
                          {template.features.map((feature, index) => (
                            <li key={index} className="flex items-center">
                              <CheckIcon className="h-3 w-3 text-green-500 mr-2" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>

                      {portfolioSettings?.customization.template === template.id && (
                        <div className="mt-3 flex items-center text-primary-600 text-sm font-medium">
                          <CheckIcon className="h-4 w-4 mr-1" />
                          Currently Selected
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Profile Information */}
            {activeTab === 'profile' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <UserIcon className="h-5 w-5 mr-2" />
                  Profile Information
                </h2>
                <button
                  onClick={() => setEditingProfile(!editingProfile)}
                  className="text-primary-600 hover:text-primary-700 flex items-center text-sm"
                >
                  <PencilIcon className="h-4 w-4 mr-1" />
                  Edit
                </button>
              </div>

              {editingProfile ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                    <input
                      type="text"
                      value={profileForm.displayName}
                      onChange={(e) => setProfileForm({ ...profileForm, displayName: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Professional Title</label>
                    <input
                      type="text"
                      value={profileForm.title}
                      onChange={(e) => setProfileForm({ ...profileForm, title: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Bio</label>
                    <textarea
                      rows={3}
                      value={profileForm.bio}
                      onChange={(e) => setProfileForm({ ...profileForm, bio: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                  <div className="flex space-x-3">
                    <button
                      onClick={handleProfileUpdate}
                      disabled={saving}
                      className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
                    >
                      <CheckIcon className="h-4 w-4 mr-1" />
                      {saving ? 'Saving...' : 'Save'}
                    </button>
                    <button
                      onClick={() => setEditingProfile(false)}
                      className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                    >
                      <XMarkIcon className="h-4 w-4 mr-1" />
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{portfolioSettings?.displayName}</h3>
                    <p className="text-gray-600">{portfolioSettings?.title}</p>
                  </div>
                  <p className="text-gray-700">{portfolioSettings?.bio}</p>
                </div>
              )}
              </div>
            )}

            {/* Project Selection */}
            {activeTab === 'projects' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <BriefcaseIcon className="h-5 w-5 mr-2" />
                Select Projects for Portfolio
              </h2>
              <p className="text-gray-600 mb-6">Choose which projects to showcase in your portfolio</p>

              <div className="space-y-4">
                {availableProjects.map((project) => (
                  <div
                    key={project.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      project.isSelected 
                        ? 'border-primary-500 bg-primary-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleProjectToggle(project.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={project.isSelected}
                            onChange={() => handleProjectToggle(project.id)}
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mr-3"
                          />
                          <div>
                            <h3 className="font-medium text-gray-900">{project.name}</h3>
                            <p className="text-sm text-gray-600">{project.client_name}</p>
                          </div>
                        </div>
                        <p className="text-sm text-gray-700 mt-2 ml-7">{project.description}</p>
                      </div>
                      <div className="text-right ml-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          project.status === 'completed' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {project.status}
                        </span>
                        <p className="text-sm text-gray-500 mt-1">${project.budget.toLocaleString()}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            )}
          </div>

          {/* Portfolio Preview & Sharing */}
          <div className="space-y-6">
            {/* Portfolio Status */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Portfolio Status</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Visibility</span>
                  <button
                    onClick={handleTogglePublic}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      portfolioSettings?.isPublic ? 'bg-primary-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        portfolioSettings?.isPublic ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
                
                <div className="text-sm">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    portfolioSettings?.isPublic 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    <GlobeAltIcon className="h-3 w-3 mr-1" />
                    {portfolioSettings?.isPublic ? 'Public' : 'Private'}
                  </span>
                </div>
              </div>
            </div>

            {/* Shareable Link */}
            {portfolioSettings?.isPublic && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <ShareIcon className="h-5 w-5 mr-2" />
                  Share Portfolio
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Shareable Link</label>
                    <div className="flex">
                      <input
                        type="text"
                        value={portfolioSettings.shareableLink}
                        readOnly
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-sm"
                      />
                      <button
                        onClick={copyShareableLink}
                        className="px-3 py-2 bg-primary-600 text-white rounded-r-md hover:bg-primary-700 flex items-center"
                      >
                        <LinkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <a
                      href={portfolioSettings.shareableLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                    >
                      <EyeIcon className="h-4 w-4 mr-2" />
                      Preview
                    </a>
                  </div>
                </div>
              </div>
            )}

            {/* Portfolio Stats */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Portfolio Stats</h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Selected Projects</span>
                  <span className="text-sm font-medium text-gray-900">
                    {availableProjects.filter(p => p.isSelected).length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Total Projects</span>
                  <span className="text-sm font-medium text-gray-900">
                    {availableProjects.length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Completed Projects</span>
                  <span className="text-sm font-medium text-gray-900">
                    {availableProjects.filter(p => p.status === 'completed').length}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
