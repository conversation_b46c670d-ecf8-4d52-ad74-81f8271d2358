'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/lib/auth';
import {
  MicrophoneIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  CalendarIcon,
  BoltIcon,
  UserGroupIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  PlayIcon,
} from '@heroicons/react/24/outline';

const features = [
  {
    name: 'Meeting Recording & AI Transcription',
    description: 'Record meetings with automatic transcription using Whisper API and AI-powered summaries.',
    icon: MicrophoneIcon,
    color: 'bg-blue-500',
  },
  {
    name: 'Smart Time Tracking',
    description: 'Track time with task integration and smart calendar scheduling for optimal productivity.',
    icon: ClockIcon,
    color: 'bg-green-500',
  },
  {
    name: 'Financial Management',
    description: 'Complete expense tracking, invoicing, and financial analytics for your freelance business.',
    icon: CurrencyDollarIcon,
    color: 'bg-yellow-500',
  },
  {
    name: 'Project Management',
    description: 'Manage projects, tasks, and client relationships with AI-powered task generation.',
    icon: DocumentTextIcon,
    color: 'bg-purple-500',
  },
  {
    name: 'Smart Calendar',
    description: 'Integrated calendar with task time estimates and AI-powered work optimization.',
    icon: CalendarIcon,
    color: 'bg-indigo-500',
  },
  {
    name: 'Workflow Automation',
    description: 'Automate client communication, progress reports, and deadline management.',
    icon: BoltIcon,
    color: 'bg-orange-500',
  },
];

const stats = [
  { name: 'Hours Saved Weekly', value: '15+' },
  { name: 'Revenue Increase', value: '30%' },
  { name: 'Client Satisfaction', value: '95%' },
  { name: 'Active Freelancers', value: '10K+' },
];

export default function HomePage() {
  const { user } = useAuth();
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  if (user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Welcome back, {user.name}!
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Ready to manage your freelance business?
            </p>
            <Link
              href="/dashboard"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              Go to Dashboard
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-primary-600">KaiNote</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/auth/login"
                className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                Sign in
              </Link>
              <Link
                href="/auth/register"
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-primary-600 to-indigo-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              The Complete Freelancer
              <span className="block text-yellow-300">Business Platform</span>
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Record meetings, track time, manage projects, and automate your workflow. 
              KaiNote makes freelancer life 90% easier with AI-powered tools.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/auth/register"
                className="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50"
              >
                Start Free Trial
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </Link>
              <button
                onClick={() => setIsVideoPlaying(true)}
                className="inline-flex items-center px-8 py-4 border-2 border-white text-lg font-medium rounded-md text-white hover:bg-white hover:text-primary-600 transition-colors"
              >
                <PlayIcon className="mr-2 h-5 w-5" />
                Watch Demo
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-primary-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat) => (
              <div key={stat.name} className="text-center">
                <div className="text-3xl font-bold text-white">{stat.value}</div>
                <div className="text-blue-200">{stat.name}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Run Your Freelance Business
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From meeting recording to financial analytics, KaiNote provides all the tools 
              you need to manage clients, projects, and grow your business.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature) => (
              <div key={feature.name} className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
                <div className={`inline-flex items-center justify-center p-3 ${feature.color} rounded-lg mb-4`}>
                  <feature.icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.name}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-primary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Transform Your Freelance Business?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join thousands of freelancers who have streamlined their workflow with KaiNote.
            </p>
            <Link
              href="/auth/register"
              className="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50"
            >
              Get Started Free
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-4">KaiNote</h3>
            <p className="text-gray-400 mb-8">
              The complete freelancer business management platform
            </p>
            <div className="flex justify-center space-x-6">
              <a href="#" className="text-gray-400 hover:text-white">Privacy</a>
              <a href="#" className="text-gray-400 hover:text-white">Terms</a>
              <a href="#" className="text-gray-400 hover:text-white">Support</a>
            </div>
            <div className="mt-8 text-gray-400 text-sm">
              © 2024 KaiNote. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
