'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import toast from 'react-hot-toast';
import {
  PlayIcon,
  StopIcon,
  ClockIcon,
  PlusIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

interface TimeEntry {
  id: string;
  userId: string;
  projectId: string;
  projectName: string;
  description: string;
  startTime: string;
  endTime?: string;
  duration: number;
  hourlyRate: number;
  totalAmount: number;
  isRunning: boolean;
  isBillable: boolean;
  tags: string[];
  createdAt: string;
}

export default function TimeTrackingPage() {
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTimer, setActiveTimer] = useState<TimeEntry | null>(null);
  const [showStartForm, setShowStartForm] = useState(false);
  const [newTimerData, setNewTimerData] = useState({
    projectId: 'demo-project-1',
    description: ''
  });
  useEffect(() => {
    fetchTimeEntries();
  }, []);

  const fetchTimeEntries = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/time-tracking`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTimeEntries(data.data);

        // Find active timer
        const runningTimer = data.data.find((entry: TimeEntry) => entry.isRunning);
        setActiveTimer(runningTimer || null);
      }
    } catch (error) {
      console.error('Error fetching time entries:', error);
      toast.error('Failed to load time entries');
    } finally {
      setLoading(false);
    }
  };

  const startTimer = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/time-tracking/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(newTimerData)
      });

      if (response.ok) {
        toast.success('Timer started!');
        setShowStartForm(false);
        setNewTimerData({ projectId: 'demo-project-1', description: '' });
        fetchTimeEntries();
      } else {
        throw new Error('Failed to start timer');
      }
    } catch (error) {
      console.error('Error starting timer:', error);
      toast.error('Failed to start timer');
    }
  };

  const stopTimer = async (timerId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/time-tracking/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ id: timerId })
      });

      if (response.ok) {
        toast.success('Timer stopped!');
        fetchTimeEntries();
      } else {
        throw new Error('Failed to stop timer');
      }
    } catch (error) {
      console.error('Error stopping timer:', error);
      toast.error('Failed to stop timer');
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Time Tracking</h1>
            <p className="text-gray-600">Track your time and manage productivity</p>
          </div>
          <button
            onClick={() => setShowStartForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
          >
            <PlayIcon className="h-4 w-4 mr-2" />
            Start Timer
          </button>
        </div>

        {/* Active Timer */}
        {activeTimer && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-green-900">Timer Running</h3>
                <p className="text-green-700">{activeTimer.projectName}</p>
                <p className="text-sm text-green-600">{activeTimer.description}</p>
                <p className="text-sm text-green-600">
                  Started at {formatTime(activeTimer.startTime)}
                </p>
              </div>
              <button
                onClick={() => stopTimer(activeTimer.id)}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center"
              >
                <StopIcon className="h-4 w-4 mr-2" />
                Stop Timer
              </button>
            </div>
          </div>
        )}

        {/* Start Timer Form */}
        {showStartForm && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Start New Timer</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Project</label>
                <select
                  value={newTimerData.projectId}
                  onChange={(e) => setNewTimerData({ ...newTimerData, projectId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="demo-project-1">E-commerce Platform Redesign</option>
                  <option value="demo-project-2">SaaS Dashboard Development</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <input
                  type="text"
                  value={newTimerData.description}
                  onChange={(e) => setNewTimerData({ ...newTimerData, description: e.target.value })}
                  placeholder="What are you working on?"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={startTimer}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                >
                  Start Timer
                </button>
                <button
                  onClick={() => setShowStartForm(false)}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Time Entries */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Time Entries</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {timeEntries.length === 0 ? (
              <div className="px-6 py-8 text-center">
                <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No time entries yet</h3>
                <p className="text-gray-600">Start tracking your time to see entries here.</p>
              </div>
            ) : (
              timeEntries.map((entry) => (
                <div key={entry.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h4 className="text-sm font-medium text-gray-900">{entry.projectName}</h4>
                        {entry.isRunning && (
                          <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Running
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600">{entry.description}</p>
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <span>{formatTime(entry.startTime)}</span>
                        {entry.endTime && (
                          <>
                            <span className="mx-2">→</span>
                            <span>{formatTime(entry.endTime)}</span>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {entry.isRunning ? 'Running...' : formatDuration(entry.duration)}
                      </div>
                      <div className="text-sm text-gray-600">
                        ${entry.totalAmount.toFixed(2)}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
