'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import toast from 'react-hot-toast';
import {
  PlayIcon,
  StopIcon,
  ClockIcon,
  PlusIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

interface TimeEntry {
  id: string;
  userId: string;
  projectId: string;
  projectName: string;
  description: string;
  startTime: string;
  endTime?: string;
  duration: number;
  hourlyRate: number;
  totalAmount: number;
  isRunning: boolean;
  isBillable: boolean;
  tags: string[];
  createdAt: string;
}

export default function TimeTrackingPage() {
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTimer, setActiveTimer] = useState<TimeEntry | null>(null);
  const [showStartForm, setShowStartForm] = useState(false);
  const [newTimerData, setNewTimerData] = useState({
    projectId: 'demo-project-1',
    description: ''
  });
  useEffect(() => {
    fetchTimeEntries();
  }, []);

  const fetchTimeEntries = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/time-tracking`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTimeEntries(data.data);

        // Find active timer
        const runningTimer = data.data.find((entry: TimeEntry) => entry.isRunning);
        setActiveTimer(runningTimer || null);
      }
    } catch (error) {
      console.error('Error fetching time entries:', error);
      toast.error('Failed to load time entries');
    } finally {
      setLoading(false);
    }
  };

  const startTimer = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/time-tracking/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(newTimerData)
      });

      if (response.ok) {
        toast.success('Timer started!');
        setShowStartForm(false);
        setNewTimerData({ projectId: 'demo-project-1', description: '' });
        fetchTimeEntries();
      } else {
        throw new Error('Failed to start timer');
      }
    } catch (error) {
      console.error('Error starting timer:', error);
      toast.error('Failed to start timer');
    }
  };

  const stopTimer = async (timerId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/time-tracking/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ id: timerId })
      });

      if (response.ok) {
        toast.success('Timer stopped!');
        fetchTimeEntries();
      } else {
        throw new Error('Failed to stop timer');
      }
    } catch (error) {
      console.error('Error stopping timer:', error);
      toast.error('Failed to stop timer');
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Time Tracking</h1>
            <p className="text-gray-600">Track your time and manage productivity</p>
          </div>
          <button
            onClick={() => setShowStartForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
          >
            <PlayIcon className="h-4 w-4 mr-2" />
            Start Timer
          </button>
        </div>

        {/* Active Timer */}
        {activeTimer && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-green-900">Timer Running</h3>
                <p className="text-green-700">{activeTimer.projectName}</p>
                <p className="text-sm text-green-600">{activeTimer.description}</p>
                <p className="text-sm text-green-600">
                  Started at {formatTime(activeTimer.startTime)}
                </p>
              </div>
              <button
                onClick={() => stopTimer(activeTimer.id)}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center"
              >
                <StopIcon className="h-4 w-4 mr-2" />
                Stop Timer
              </button>
            </div>
          </div>
        )}

        {/* Start Timer Form */}
        {showStartForm && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Start New Timer</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Project</label>
                <select
                  value={newTimerData.projectId}
                  onChange={(e) => setNewTimerData({ ...newTimerData, projectId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="demo-project-1">E-commerce Platform Redesign</option>
                  <option value="demo-project-2">SaaS Dashboard Development</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <input
                  type="text"
                  value={newTimerData.description}
                  onChange={(e) => setNewTimerData({ ...newTimerData, description: e.target.value })}
                  placeholder="What are you working on?"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={startTimer}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                >
                  Start Timer
                </button>
                <button
                  onClick={() => setShowStartForm(false)}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Time Entries */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Time Entries</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {timeEntries.length === 0 ? (
              <div className="px-6 py-8 text-center">
                <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No time entries yet</h3>
                <p className="text-gray-600">Start tracking your time to see entries here.</p>
              </div>
            ) : (
              timeEntries.map((entry) => (
                <div key={entry.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h4 className="text-sm font-medium text-gray-900">{entry.projectName}</h4>
                        {entry.isRunning && (
                          <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Running
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600">{entry.description}</p>
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <span>{formatTime(entry.startTime)}</span>
                        {entry.endTime && (
                          <>
                            <span className="mx-2">→</span>
                            <span>{formatTime(entry.endTime)}</span>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {entry.isRunning ? 'Running...' : formatDuration(entry.duration)}
                      </div>
                      <div className="text-sm text-gray-600">
                        ${entry.totalAmount.toFixed(2)}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
    'active-timer',
    apiHelpers.getActiveTimer,
    {
      enabled: isAuthenticated,
      refetchInterval: 1000, // Update every second for live timer
    }
  );

  // Fetch time entries
  const { data: timeEntriesResponse, isLoading: entriesLoading } = useQuery(
    ['time-entries', selectedPeriod],
    () => apiHelpers.getTimeEntries({ limit: 50 }),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch analytics
  const { data: analyticsResponse } = useQuery(
    ['time-analytics', selectedPeriod],
    () => apiHelpers.getTimeAnalytics({ period: selectedPeriod }),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch projects for timer
  const { data: projectsResponse } = useQuery(
    'projects',
    apiHelpers.getProjects,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Stop timer mutation
  const stopTimerMutation = useMutation(
    (data: { description?: string }) => apiHelpers.stopTimer(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('active-timer');
        queryClient.invalidateQueries('time-entries');
        queryClient.invalidateQueries('time-analytics');
      },
    }
  );

  const handleStopTimer = async (description?: string) => {
    try {
      await stopTimerMutation.mutateAsync({ description });
    } catch (error) {
      console.error('Error stopping timer:', error);
    }
  };

  // Calculate elapsed time for active timer
  const getElapsedTime = (startTime: string) => {
    const start = new Date(startTime);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Format duration
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const timeEntries = Array.isArray(timeEntriesResponse) ? timeEntriesResponse : [];
  const analytics = analyticsResponse?.summary;
  const projects = Array.isArray(projectsResponse) ? projectsResponse : [];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Time Tracking</h1>
              <p className="text-gray-600">Track your time and boost productivity</p>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value as any)}
                className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
              </select>
              <button
                onClick={() => setShowNewEntryForm(true)}
                className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center space-x-2"
              >
                <PlusIcon className="h-5 w-5" />
                <span>Add Entry</span>
              </button>
            </div>
          </div>
        </div>

        {/* Active Timer */}
        {activeTimer?.data && (
          <div className="bg-gradient-to-r from-green-500 to-blue-600 rounded-lg p-6 mb-8 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="bg-white bg-opacity-20 rounded-full p-3">
                  <ClockIcon className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">Timer Running</h3>
                  <p className="text-green-100">
                    {activeTimer.data.project?.name} - {activeTimer.data.project?.client_name}
                  </p>
                  {activeTimer.data.description && (
                    <p className="text-green-100 text-sm">{activeTimer.data.description}</p>
                  )}
                </div>
              </div>
              <div className="text-right">
                <div className="text-3xl font-mono font-bold">
                  {getElapsedTime(activeTimer.data.start_time)}
                </div>
                <button
                  onClick={() => handleStopTimer()}
                  className="mt-2 bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg hover:bg-opacity-30 flex items-center space-x-2"
                >
                  <StopIcon className="h-4 w-4" />
                  <span>Stop Timer</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Cards */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-lg p-3">
                  <ClockIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Hours</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.totalHours}h</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-3">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Billable Hours</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.billableHours}h</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-purple-100 rounded-lg p-3">
                  <ChartBarIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">${analytics.totalRevenue}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-orange-100 rounded-lg p-3">
                  <CalendarIcon className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Avg Rate</p>
                  <p className="text-2xl font-bold text-gray-900">${analytics.averageHourlyRate}/h</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Time Entries List */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Recent Time Entries</h2>
          </div>
          
          {entriesLoading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            </div>
          ) : timeEntries.length === 0 ? (
            <div className="p-6 text-center">
              <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No time entries yet</h3>
              <p className="mt-1 text-sm text-gray-500">Start tracking your time to see entries here.</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {timeEntries.map((entry: TimeEntry) => (
                <div key={entry.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${entry.is_billable ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {entry.project?.name} - {entry.project?.client_name}
                          </p>
                          {entry.description && (
                            <p className="text-sm text-gray-600">{entry.description}</p>
                          )}
                          {entry.task && (
                            <p className="text-xs text-gray-500">Task: {entry.task.title}</p>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-6 text-sm text-gray-500">
                      <div>
                        <p>{format(new Date(entry.start_time), 'MMM d, yyyy')}</p>
                        <p>{format(new Date(entry.start_time), 'h:mm a')} - {entry.end_time ? format(new Date(entry.end_time), 'h:mm a') : 'Running'}</p>
                      </div>
                      
                      <div className="text-right">
                        <p className="font-medium text-gray-900">
                          {entry.duration_minutes ? formatDuration(entry.duration_minutes) : 'Running'}
                        </p>
                        {entry.is_billable && entry.duration_minutes && (
                          <p className="text-green-600">
                            ${((entry.duration_minutes / 60) * entry.hourly_rate).toFixed(2)}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
