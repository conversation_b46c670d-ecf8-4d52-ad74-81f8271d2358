'use client';

import { 
  EnvelopeIcon,
  GlobeAltIcon,
  MapPinIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  StarIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import Image from 'next/image';

interface PortfolioData {
  freelancer: {
    displayName: string;
    title: string;
    bio: string;
    email: string;
    location: string;
    website: string;
    skills: string[];
  };
  projects: any[];
  customization: {
    primaryColor: string;
    secondaryColor: string;
    showContactInfo: boolean;
    showProjectDetails: boolean;
    showClientTestimonials: boolean;
    showSkills: boolean;
    showStats: boolean;
  };
  stats: {
    totalProjects: number;
    completedProjects: number;
    happyClients: number;
    yearsExperience: number;
  };
}

interface CreativeTemplateProps {
  data: PortfolioData;
}

export default function CreativeTemplate({ data }: CreativeTemplateProps) {
  const { freelancer, projects, customization, stats } = data;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50">
      {/* Creative Header */}
      <div className="relative overflow-hidden">
        <div 
          className="absolute inset-0 opacity-20"
          style={{
            background: `radial-gradient(circle at 20% 50%, ${customization.primaryColor} 0%, transparent 50%), radial-gradient(circle at 80% 20%, ${customization.secondaryColor} 0%, transparent 50%)`
          }}
        ></div>
        
        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <div 
              className="w-40 h-40 rounded-full mx-auto mb-8 flex items-center justify-center text-white font-bold text-3xl shadow-2xl transform rotate-3"
              style={{
                background: `linear-gradient(135deg, ${customization.primaryColor}, ${customization.secondaryColor})`
              }}
            >
              {freelancer.displayName.split(' ').map(n => n[0]).join('')}
            </div>
            
            <h1 className="text-6xl font-extrabold mb-4 bg-gradient-to-r from-purple-600 via-pink-600 to-orange-600 bg-clip-text text-transparent">
              {freelancer.displayName}
            </h1>
            <p className="text-2xl font-bold mb-6" style={{ color: customization.primaryColor }}>
              {freelancer.title}
            </p>
            <p className="text-lg max-w-3xl mx-auto mb-8 text-gray-700">{freelancer.bio}</p>
            
            {customization.showContactInfo && (
              <div className="flex flex-wrap justify-center gap-8 text-gray-600">
                <div className="flex items-center bg-white rounded-full px-4 py-2 shadow-lg">
                  <EnvelopeIcon className="h-5 w-5 mr-2" style={{ color: customization.primaryColor }} />
                  <a href={`mailto:${freelancer.email}`} className="hover:text-gray-900">
                    {freelancer.email}
                  </a>
                </div>
                <div className="flex items-center bg-white rounded-full px-4 py-2 shadow-lg">
                  <MapPinIcon className="h-5 w-5 mr-2" style={{ color: customization.primaryColor }} />
                  {freelancer.location}
                </div>
                <div className="flex items-center bg-white rounded-full px-4 py-2 shadow-lg">
                  <GlobeAltIcon className="h-5 w-5 mr-2" style={{ color: customization.primaryColor }} />
                  <a href={freelancer.website} target="_blank" rel="noopener noreferrer" className="hover:text-gray-900">
                    Portfolio
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Creative Stats */}
        {customization.showStats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16 -mt-10 relative z-10">
            {[
              { value: stats.totalProjects, label: 'Creative Projects', icon: '🎨' },
              { value: stats.completedProjects, label: 'Completed Works', icon: '✨' },
              { value: stats.happyClients, label: 'Happy Clients', icon: '😊' },
              { value: stats.yearsExperience, label: 'Years Creating', icon: '🚀' }
            ].map((stat, index) => (
              <div 
                key={index}
                className="bg-white rounded-2xl shadow-xl p-6 text-center transform hover:scale-105 transition-transform"
                style={{
                  background: `linear-gradient(135deg, white, ${customization.primaryColor}10)`
                }}
              >
                <div className="text-3xl mb-2">{stat.icon}</div>
                <div className="text-3xl font-bold mb-2" style={{ color: customization.primaryColor }}>
                  {stat.value}
                </div>
                <div className="text-sm text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        )}

        {/* Creative Skills */}
        {customization.showSkills && (
          <div className="mb-16">
            <h2 className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Creative Arsenal
            </h2>
            <div className="flex flex-wrap justify-center gap-4">
              {freelancer.skills.map((skill, index) => (
                <span
                  key={index}
                  className="px-6 py-3 rounded-full text-white font-bold shadow-lg transform hover:scale-110 transition-transform cursor-default"
                  style={{
                    background: `linear-gradient(135deg, ${customization.primaryColor}, ${customization.secondaryColor})`,
                    transform: `rotate(${(index % 3 - 1) * 2}deg)`
                  }}
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Creative Projects Showcase */}
        <div className="mb-16">
          <h2 className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            Creative Showcase
          </h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {projects.map((project, index) => (
              <div 
                key={project.id} 
                className="bg-white rounded-3xl shadow-2xl overflow-hidden transform hover:scale-105 transition-all duration-300"
                style={{
                  transform: `rotate(${(index % 2 === 0 ? 1 : -1) * 1}deg)`
                }}
              >
                {/* Creative Project Header */}
                <div 
                  className="h-4"
                  style={{
                    background: `linear-gradient(90deg, ${customization.primaryColor}, ${customization.secondaryColor})`
                  }}
                ></div>

                {/* Project Image */}
                {project.images && project.images.length > 0 && (
                  <div className="relative h-64 overflow-hidden">
                    <Image
                      src={project.images[0]}
                      alt={project.name}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60"></div>
                    <div className="absolute bottom-4 left-4 text-white">
                      <h3 className="text-2xl font-bold">{project.name}</h3>
                      <p className="text-sm opacity-90 flex items-center">
                        <SparklesIcon className="h-4 w-4 mr-1" />
                        {project.client_name}
                      </p>
                    </div>
                  </div>
                )}

                <div className="p-8">
                  {!project.images?.length && (
                    <div className="mb-6">
                      <h3 className="text-2xl font-bold mb-2" style={{ color: customization.primaryColor }}>
                        {project.name}
                      </h3>
                      <p className="text-gray-600 flex items-center">
                        <SparklesIcon className="h-4 w-4 mr-2" />
                        {project.client_name}
                      </p>
                    </div>
                  )}

                  <p className="text-gray-700 mb-6 leading-relaxed">{project.description}</p>

                  {customization.showProjectDetails && (
                    <>
                      <div className="grid grid-cols-2 gap-4 mb-6">
                        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4">
                          <div className="flex items-center text-gray-600 mb-1">
                            <CalendarIcon className="h-4 w-4 mr-2" />
                            <span className="text-xs font-medium uppercase tracking-wide">Timeline</span>
                          </div>
                          <div className="text-sm font-semibold">
                            {formatDate(project.start_date)} - {formatDate(project.end_date)}
                          </div>
                        </div>
                        <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl p-4">
                          <div className="flex items-center text-gray-600 mb-1">
                            <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                            <span className="text-xs font-medium uppercase tracking-wide">Investment</span>
                          </div>
                          <div className="text-sm font-semibold">
                            {formatCurrency(project.budget)}
                          </div>
                        </div>
                      </div>

                      {/* Creative Technologies */}
                      <div className="mb-6">
                        <h4 className="text-sm font-bold text-gray-900 mb-3 uppercase tracking-wide">Creative Tools</h4>
                        <div className="flex flex-wrap gap-2">
                          {project.technologies?.map((tech: string, techIndex: number) => (
                            <span
                              key={techIndex}
                              className="px-3 py-1 text-xs font-bold rounded-full text-white"
                              style={{ 
                                backgroundColor: techIndex % 2 === 0 ? customization.primaryColor : customization.secondaryColor
                              }}
                            >
                              {tech}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Creative Highlights */}
                      <div className="mb-6">
                        <h4 className="text-sm font-bold text-gray-900 mb-3 uppercase tracking-wide">Creative Impact</h4>
                        <ul className="text-sm text-gray-700 space-y-2">
                          {project.highlights?.map((highlight: string, highlightIndex: number) => (
                            <li key={highlightIndex} className="flex items-start">
                              <span 
                                className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0"
                                style={{ backgroundColor: customization.secondaryColor }}
                              ></span>
                              {highlight}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </>
                  )}

                  {/* Creative Testimonial */}
                  {customization.showClientTestimonials && project.testimonial && (
                    <div 
                      className="rounded-2xl p-6 relative overflow-hidden"
                      style={{
                        background: `linear-gradient(135deg, ${customization.primaryColor}15, ${customization.secondaryColor}15)`
                      }}
                    >
                      <div className="absolute top-2 right-2 text-6xl opacity-20">💬</div>
                      <div className="relative">
                        <div className="flex text-yellow-400 mb-3">
                          {[...Array(5)].map((_, i) => (
                            <StarIcon key={i} className="h-5 w-5 fill-current" />
                          ))}
                        </div>
                        <p className="text-gray-700 italic mb-4 font-medium">"{project.testimonial.text}"</p>
                        <div className="text-sm">
                          <p className="font-bold text-gray-900">{project.testimonial.author}</p>
                          <p className="text-gray-600">{project.testimonial.position}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Creative Contact */}
        {customization.showContactInfo && (
          <div 
            className="rounded-3xl p-12 text-center text-white relative overflow-hidden"
            style={{
              background: `linear-gradient(135deg, ${customization.primaryColor}, ${customization.secondaryColor})`
            }}
          >
            <div className="absolute inset-0 opacity-20">
              <div className="absolute top-10 left-10 text-6xl">🎨</div>
              <div className="absolute top-20 right-20 text-4xl">✨</div>
              <div className="absolute bottom-10 left-20 text-5xl">🚀</div>
              <div className="absolute bottom-20 right-10 text-3xl">💡</div>
            </div>
            
            <div className="relative">
              <h2 className="text-4xl font-bold mb-6">Let's Create Something Amazing!</h2>
              <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
                Ready to bring your wildest ideas to life? Let's collaborate and create something that will blow minds and exceed expectations!
              </p>
              <a
                href={`mailto:${freelancer.email}?subject=Creative Collaboration&body=Hi ${freelancer.displayName}, I love your creative work and would like to discuss a project!`}
                className="inline-flex items-center px-10 py-4 bg-white text-gray-900 rounded-full font-bold hover:bg-gray-100 transition-colors shadow-2xl transform hover:scale-105"
              >
                <EnvelopeIcon className="h-6 w-6 mr-3" />
                Start Creating Together
              </a>
            </div>
          </div>
        )}
      </div>

      {/* Creative Footer */}
      <footer className="bg-white py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-600 text-sm">
            Creative portfolio powered by <span className="font-bold" style={{ color: customization.primaryColor }}>KaiNote</span> ✨
          </p>
        </div>
      </footer>
    </div>
  );
}
