import express, { Request, Response } from 'express';

const router = express.Router();

// Get project details for client review (no auth required)
router.get('/project/:projectId/review-info', async (req: Request, res: Response) => {
  try {
    const { projectId } = req.params;

    // Demo project data for client review
    const projectInfo = {
      id: projectId,
      name: 'E-commerce Platform Redesign',
      freelancer: {
        name: '<PERSON>',
        title: 'Full-Stack Developer & UI/UX Designer',
        email: '<EMAIL>'
      },
      client: {
        name: 'TechCorp Solutions',
        email: '<EMAIL>'
      },
      description: 'Complete redesign and development of a modern e-commerce platform with improved user experience and performance.',
      completedDate: '2024-03-20',
      status: 'completed',
      deliverables: [
        'Responsive web design',
        'E-commerce functionality',
        'Payment integration',
        'Admin dashboard',
        'Mobile optimization'
      ],
      existingReview: null // Will be populated if review already exists
    };

    res.json({
      success: true,
      data: projectInfo
    });

  } catch (error: any) {
    console.error('Error getting project review info:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get project information',
      details: error.message
    });
  }
});

// Submit client review (no auth required)
router.post('/project/:projectId/submit-review', async (req: Request, res: Response) => {
  try {
    const { projectId } = req.params;
    const {
      clientName,
      clientEmail,
      clientPosition,
      rating,
      reviewText,
      wouldRecommend,
      projectSatisfaction,
      communicationRating,
      qualityRating,
      timelinessRating,
      allowPortfolioDisplay
    } = req.body;

    // Validate required fields
    if (!clientName || !clientEmail || !rating || !reviewText) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: clientName, clientEmail, rating, reviewText'
      });
    }

    // Validate rating
    if (rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        error: 'Rating must be between 1 and 5'
      });
    }

    // Create review object
    const review = {
      id: `review_${Date.now()}`,
      projectId,
      clientName,
      clientEmail,
      clientPosition: clientPosition || '',
      rating: parseInt(rating),
      reviewText,
      wouldRecommend: wouldRecommend || false,
      projectSatisfaction: projectSatisfaction || rating,
      communicationRating: communicationRating || rating,
      qualityRating: qualityRating || rating,
      timelinessRating: timelinessRating || rating,
      allowPortfolioDisplay: allowPortfolioDisplay !== false, // Default to true
      submittedAt: new Date().toISOString(),
      status: 'pending', // pending, approved, rejected
      isVerified: false
    };

    // In a real app, save to database
    console.log('New review submitted:', review);

    // Send confirmation email to freelancer (in real app)
    console.log('Sending notification to freelancer about new review');

    res.json({
      success: true,
      data: {
        reviewId: review.id,
        message: 'Review submitted successfully! The freelancer will be notified.',
        status: 'pending'
      }
    });

  } catch (error: any) {
    console.error('Error submitting review:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to submit review',
      details: error.message
    });
  }
});

// Get reviews for a project (freelancer access)
router.get('/project/:projectId/reviews', async (req: Request, res: Response) => {
  try {
    const { projectId } = req.params;

    // Demo reviews data
    const reviews = [
      {
        id: 'review_1',
        projectId,
        clientName: 'Sarah Chen',
        clientEmail: '<EMAIL>',
        clientPosition: 'CTO, TechCorp Solutions',
        rating: 5,
        reviewText: 'Alex delivered an outstanding e-commerce platform that exceeded our expectations. The new design increased our conversion rate significantly and the code quality is excellent.',
        wouldRecommend: true,
        projectSatisfaction: 5,
        communicationRating: 5,
        qualityRating: 5,
        timelinessRating: 4,
        allowPortfolioDisplay: true,
        submittedAt: '2024-03-25T10:30:00Z',
        status: 'approved',
        isVerified: true
      }
    ];

    res.json({
      success: true,
      data: reviews
    });

  } catch (error: any) {
    console.error('Error getting reviews:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get reviews',
      details: error.message
    });
  }
});

// Generate review request link for freelancer to send to client
router.post('/project/:projectId/generate-review-link', async (req: Request, res: Response) => {
  try {
    const { projectId } = req.params;
    const { clientEmail, clientName } = req.body;

    // Generate secure review token
    const reviewToken = `review_${projectId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Create review link
    const reviewLink = `${process.env.FRONTEND_URL || 'http://localhost:3001'}/review/${reviewToken}`;

    // In real app, save token to database with expiration
    console.log('Generated review link:', reviewLink);

    // Email template for client
    const emailTemplate = {
      subject: `Please review your project with Alex Johnson`,
      body: `Dear ${clientName},

Thank you for working with me on your recent project. I hope you're happy with the results!

I would greatly appreciate if you could take a few minutes to share your feedback about our collaboration. Your review helps me improve my services and shows potential clients the quality of work I deliver.

Please click the link below to submit your review:
${reviewLink}

The review should take less than 5 minutes to complete, and you can choose whether to allow it to be displayed on my portfolio.

Thank you for your time and for choosing to work with me!

Best regards,
Alex Johnson
Full-Stack Developer & UI/UX Designer

---
This review request was sent through KaiNote. If you have any questions, please contact me directly.`
    };

    res.json({
      success: true,
      data: {
        reviewLink,
        reviewToken,
        emailTemplate,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      }
    });

  } catch (error: any) {
    console.error('Error generating review link:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate review link',
      details: error.message
    });
  }
});

// Validate review token and get project info
router.get('/review-token/:token', async (req: Request, res: Response) => {
  try {
    const { token } = req.params;

    // In real app, validate token from database
    if (!token.startsWith('review_')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid review token'
      });
    }

    // Extract project ID from token (demo implementation)
    const projectId = token.split('_')[1] || 'demo-project-1';

    // Get project info for review
    const projectInfo = {
      id: projectId,
      name: 'E-commerce Platform Redesign',
      freelancer: {
        name: 'Alex Johnson',
        title: 'Full-Stack Developer & UI/UX Designer',
        email: '<EMAIL>'
      },
      client: {
        name: 'TechCorp Solutions',
        email: '<EMAIL>'
      },
      description: 'Complete redesign and development of a modern e-commerce platform with improved user experience and performance.',
      completedDate: '2024-03-20',
      deliverables: [
        'Responsive web design',
        'E-commerce functionality', 
        'Payment integration',
        'Admin dashboard',
        'Mobile optimization'
      ]
    };

    res.json({
      success: true,
      data: {
        token,
        project: projectInfo,
        isValid: true,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      }
    });

  } catch (error: any) {
    console.error('Error validating review token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate review token',
      details: error.message
    });
  }
});

export default router;
