-- Smart Scheduling and Automation Schema Extension
-- Add this to your existing database schema for smart scheduling capabilities

-- Task dependencies table for smart scheduling
CREATE TABLE IF NOT EXISTS public.task_dependencies (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES public.project_tasks(id) ON DELETE CASCADE,
  depends_on_task_id UUID REFERENCES public.project_tasks(id) ON DELETE CASCADE,
  dependency_type TEXT DEFAULT 'finish_to_start' CHECK (dependency_type IN ('finish_to_start', 'start_to_start', 'finish_to_finish', 'start_to_finish')),
  lag_days INTEGER DEFAULT 0, -- delay in days after dependency is met
  is_critical BOOLEAN DEFAULT false, -- critical path dependency
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(task_id, depends_on_task_id)
);

-- Smart scheduling configurations
CREATE TABLE IF NOT EXISTS public.project_schedules (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  schedule_name TEXT NOT NULL,
  auto_schedule_enabled BOOLEAN DEFAULT true,
  working_days INTEGER[] DEFAULT '{1,2,3,4,5}', -- Monday=1, Sunday=7
  working_hours_start TIME DEFAULT '09:00:00',
  working_hours_end TIME DEFAULT '17:00:00',
  buffer_time_hours DECIMAL(5,2) DEFAULT 2.0, -- buffer between tasks
  client_communication_enabled BOOLEAN DEFAULT true,
  notification_settings JSONB DEFAULT '{"task_completion": true, "milestone_reached": true, "deadline_approaching": true}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Task scheduling metadata
CREATE TABLE IF NOT EXISTS public.task_schedules (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES public.project_tasks(id) ON DELETE CASCADE,
  project_schedule_id UUID REFERENCES public.project_schedules(id) ON DELETE CASCADE,
  estimated_hours DECIMAL(5,2) NOT NULL,
  scheduled_start TIMESTAMP WITH TIME ZONE,
  scheduled_end TIMESTAMP WITH TIME ZONE,
  actual_start TIMESTAMP WITH TIME ZONE,
  actual_end TIMESTAMP WITH TIME ZONE,
  is_milestone BOOLEAN DEFAULT false,
  auto_scheduled BOOLEAN DEFAULT true,
  scheduling_notes TEXT,
  client_visible BOOLEAN DEFAULT true, -- whether client can see this task
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced automation rules for smart scheduling
CREATE TABLE IF NOT EXISTS public.smart_automation_rules (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
  rule_name TEXT NOT NULL,
  rule_type TEXT NOT NULL CHECK (rule_type IN ('task_completion', 'deadline_approaching', 'milestone_reached', 'project_status_change', 'client_communication')),
  trigger_conditions JSONB NOT NULL, -- complex trigger conditions
  actions JSONB NOT NULL, -- array of actions to perform
  client_communication_config JSONB DEFAULT '{}', -- email templates, timing, etc.
  is_active BOOLEAN DEFAULT true,
  priority INTEGER DEFAULT 1, -- execution priority
  last_executed TIMESTAMP WITH TIME ZONE,
  execution_count INTEGER DEFAULT 0,
  success_count INTEGER DEFAULT 0,
  failure_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Client communication templates for automation
CREATE TABLE IF NOT EXISTS public.automation_email_templates (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  template_name TEXT NOT NULL,
  template_type TEXT NOT NULL CHECK (template_type IN ('task_completion', 'milestone_update', 'deadline_reminder', 'project_update', 'schedule_change')),
  subject_template TEXT NOT NULL,
  body_template TEXT NOT NULL,
  variables JSONB DEFAULT '[]', -- available template variables
  send_timing TEXT DEFAULT 'immediate' CHECK (send_timing IN ('immediate', 'daily_digest', 'weekly_digest')),
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Scheduled communications queue
CREATE TABLE IF NOT EXISTS public.scheduled_communications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  task_id UUID REFERENCES public.project_tasks(id) ON DELETE SET NULL,
  automation_rule_id UUID REFERENCES public.smart_automation_rules(id) ON DELETE SET NULL,
  template_id UUID REFERENCES public.automation_email_templates(id) ON DELETE SET NULL,
  recipient_email TEXT NOT NULL,
  recipient_name TEXT,
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  communication_type TEXT DEFAULT 'email' CHECK (communication_type IN ('email', 'sms', 'slack', 'webhook')),
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE,
  status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'sent', 'failed', 'cancelled')),
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Project milestones for client communication
CREATE TABLE IF NOT EXISTS public.project_milestones (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  milestone_name TEXT NOT NULL,
  description TEXT,
  target_date DATE NOT NULL,
  completion_date DATE,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'delayed')),
  completion_percentage DECIMAL(5,2) DEFAULT 0.00,
  associated_tasks UUID[], -- array of task IDs
  client_notification_sent BOOLEAN DEFAULT false,
  is_critical BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_task_dependencies_task_id ON public.task_dependencies(task_id);
CREATE INDEX IF NOT EXISTS idx_task_dependencies_depends_on ON public.task_dependencies(depends_on_task_id);
CREATE INDEX IF NOT EXISTS idx_project_schedules_project_id ON public.project_schedules(project_id);
CREATE INDEX IF NOT EXISTS idx_task_schedules_task_id ON public.task_schedules(task_id);
CREATE INDEX IF NOT EXISTS idx_task_schedules_scheduled_start ON public.task_schedules(scheduled_start);
CREATE INDEX IF NOT EXISTS idx_smart_automation_rules_user_id ON public.smart_automation_rules(user_id);
CREATE INDEX IF NOT EXISTS idx_smart_automation_rules_project_id ON public.smart_automation_rules(project_id);
CREATE INDEX IF NOT EXISTS idx_smart_automation_rules_type ON public.smart_automation_rules(rule_type);
CREATE INDEX IF NOT EXISTS idx_scheduled_communications_scheduled_for ON public.scheduled_communications(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_scheduled_communications_status ON public.scheduled_communications(status);
CREATE INDEX IF NOT EXISTS idx_project_milestones_project_id ON public.project_milestones(project_id);
CREATE INDEX IF NOT EXISTS idx_project_milestones_target_date ON public.project_milestones(target_date);

-- Row Level Security policies
ALTER TABLE public.task_dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.smart_automation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.automation_email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scheduled_communications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_milestones ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view task dependencies for their projects" ON public.task_dependencies FOR SELECT USING (
  EXISTS (SELECT 1 FROM public.project_tasks pt JOIN public.projects p ON pt.project_id = p.id WHERE pt.id = task_id AND p.user_id = auth.uid())
);

CREATE POLICY "Users can manage their project schedules" ON public.project_schedules FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage their task schedules" ON public.task_schedules FOR ALL USING (
  EXISTS (SELECT 1 FROM public.project_tasks pt JOIN public.projects p ON pt.project_id = p.id WHERE pt.id = task_id AND p.user_id = auth.uid())
);

CREATE POLICY "Users can manage their automation rules" ON public.smart_automation_rules FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage their email templates" ON public.automation_email_templates FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view their scheduled communications" ON public.scheduled_communications FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage their project milestones" ON public.project_milestones FOR ALL USING (auth.uid() = user_id);

-- Triggers for updated_at
CREATE TRIGGER update_project_schedules_updated_at BEFORE UPDATE ON public.project_schedules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_task_schedules_updated_at BEFORE UPDATE ON public.task_schedules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_smart_automation_rules_updated_at BEFORE UPDATE ON public.smart_automation_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_automation_email_templates_updated_at BEFORE UPDATE ON public.automation_email_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_project_milestones_updated_at BEFORE UPDATE ON public.project_milestones FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
