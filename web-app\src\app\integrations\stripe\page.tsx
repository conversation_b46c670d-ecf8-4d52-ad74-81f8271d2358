'use client';

import { useState, useEffect } from 'react';
import { 
  CreditCardIcon, 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  BanknotesIcon,
  ChartBarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';

interface StripeStatus {
  connected: boolean;
  accountId?: string;
  onboardingRequired: boolean;
  paymentsEnabled: boolean;
  detailsSubmitted: boolean;
  country?: string;
  currency?: string;
  businessType?: string;
  email?: string;
  displayName?: string;
  dashboardUrl?: string;
}

export default function StripeIntegrationPage() {
  const [stripeStatus, setStripeStatus] = useState<StripeStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState(false);

  useEffect(() => {
    fetchStripeStatus();
  }, []);

  const fetchStripeStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/freelancer-billing/stripe-status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setStripeStatus(data.data);
      }
    } catch (error) {
      console.error('Error fetching Stripe status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConnectStripe = async () => {
    try {
      setConnecting(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/freelancer-billing/stripe-connect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          country: 'US',
          businessType: 'individual'
        })
      });

      if (response.ok) {
        const data = await response.json();
        window.location.href = data.data.onboardingUrl;
      }
    } catch (error) {
      console.error('Error connecting Stripe:', error);
    } finally {
      setConnecting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Stripe Integration</h1>
          <p className="mt-2 text-gray-600">Connect your Stripe account to accept online payments from clients</p>
        </div>

        {!stripeStatus?.connected ? (
          // Not Connected State
          <div className="space-y-8">
            {/* Benefits Section */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
              <div className="text-center mb-8">
                <CreditCardIcon className="mx-auto h-16 w-16 text-primary-600 mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Accept Online Payments
                </h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Connect your Stripe account to let clients pay invoices online. Money goes directly to your account - KaiNote never touches your funds.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <BanknotesIcon className="mx-auto h-8 w-8 text-green-600 mb-3" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Direct Deposits</h3>
                  <p className="text-gray-600">Payments go directly to your bank account. No middleman, no delays.</p>
                </div>
                
                <div className="text-center">
                  <CheckCircleIcon className="mx-auto h-8 w-8 text-blue-600 mb-3" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Secure Processing</h3>
                  <p className="text-gray-600">Bank-level security with PCI DSS compliance and fraud protection.</p>
                </div>
                
                <div className="text-center">
                  <ChartBarIcon className="mx-auto h-8 w-8 text-purple-600 mb-3" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Real-time Tracking</h3>
                  <p className="text-gray-600">Track payments, view analytics, and manage your finances in one place.</p>
                </div>
              </div>

              <div className="text-center">
                <button
                  onClick={handleConnectStripe}
                  disabled={connecting}
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {connecting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Connecting...
                    </>
                  ) : (
                    <>
                      <CreditCardIcon className="h-5 w-5 mr-2" />
                      Connect Stripe Account
                    </>
                  )}
                </button>
                <p className="mt-2 text-sm text-gray-500">
                  Free to connect • No setup fees • Competitive rates
                </p>
              </div>
            </div>

            {/* How it Works */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">How it Works</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-semibold text-sm">1</span>
                  </div>
                  <div className="ml-3">
                    <h4 className="text-sm font-medium text-gray-900">Connect Your Account</h4>
                    <p className="text-sm text-gray-600">Link your existing Stripe account or create a new one in minutes.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-semibold text-sm">2</span>
                  </div>
                  <div className="ml-3">
                    <h4 className="text-sm font-medium text-gray-900">Create Invoices</h4>
                    <p className="text-sm text-gray-600">Add "Pay Online" option to your invoices with one click.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-semibold text-sm">3</span>
                  </div>
                  <div className="ml-3">
                    <h4 className="text-sm font-medium text-gray-900">Get Paid Faster</h4>
                    <p className="text-sm text-gray-600">Clients pay instantly online, money goes directly to your account.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Connected State
          <div className="space-y-8">
            {/* Connection Status */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <CheckCircleIcon className="h-8 w-8 text-green-600 mr-3" />
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">Stripe Connected</h2>
                    <p className="text-gray-600">Your account is ready to accept payments</p>
                  </div>
                </div>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  Active
                </span>
              </div>
            </div>

            {/* Account Details */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Account Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Account Information</h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p><span className="font-medium">Account ID:</span> {stripeStatus.accountId}</p>
                    <p><span className="font-medium">Email:</span> {stripeStatus.email}</p>
                    <p><span className="font-medium">Country:</span> {stripeStatus.country}</p>
                    <p><span className="font-medium">Currency:</span> {stripeStatus.currency?.toUpperCase()}</p>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Status</h4>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <CheckCircleIcon className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-sm text-gray-600">Payments enabled</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircleIcon className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-sm text-gray-600">Details submitted</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircleIcon className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-sm text-gray-600">Account verified</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Manage Your Account</h3>
              <div className="flex flex-wrap gap-4">
                <a
                  href={stripeStatus.dashboardUrl || '#'}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <ChartBarIcon className="h-4 w-4 mr-2" />
                  View Stripe Dashboard
                </a>
                
                <a
                  href="/freelancer-billing"
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
                >
                  <BanknotesIcon className="h-4 w-4 mr-2" />
                  Create Invoice
                </a>
                
                <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                  <Cog6ToothIcon className="h-4 w-4 mr-2" />
                  Account Settings
                </button>
              </div>
            </div>

            {/* Fees Information */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Payment Processing Fees</h4>
              <p className="text-sm text-blue-800">
                Stripe charges 2.9% + 30¢ per successful transaction. These fees are deducted automatically from each payment.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
