import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Providers } from './providers'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'KaiNote - Freelancer Business Management Platform',
  description: 'Complete business management platform for freelancers with meeting recording, AI summaries, project management, time tracking, and financial analytics.',
  keywords: 'freelancer, business management, meeting recording, AI transcription, project management, time tracking, invoicing',
  authors: [{ name: 'KaiNote Team' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'KaiNote - Freelancer Business Management Platform',
    description: 'Complete business management platform for freelancers',
    type: 'website',
    locale: 'en_US',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} h-full bg-gray-50`}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}
