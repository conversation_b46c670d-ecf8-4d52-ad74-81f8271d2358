import { Router } from 'express';
import { SmartSchedulingService } from '../services/smartSchedulingService';
import { AutomationRulesService } from '../services/automationRulesService';
import { ClientCommunicationService } from '../services/clientCommunicationService';
import { authMiddleware } from '../middleware/auth';
import { supabaseAdmin } from '../config/supabase';

const router = Router();
const schedulingService = new SmartSchedulingService();
const automationService = new AutomationRulesService();
const communicationService = new ClientCommunicationService();

// Apply auth middleware to all routes
router.use(authMiddleware);

/**
 * Auto-schedule project tasks
 * POST /api/smart-scheduling/projects/:projectId/schedule
 */
router.post('/projects/:projectId/schedule', async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user.userId;

    const scheduledTasks = await schedulingService.scheduleProject(projectId, userId);

    res.json({
      success: true,
      data: scheduledTasks,
      message: 'Project tasks scheduled successfully'
    });

  } catch (error) {
    console.error('Error scheduling project:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to schedule project tasks'
    });
  }
});

/**
 * Get project schedule configuration
 * GET /api/smart-scheduling/projects/:projectId/config
 */
router.get('/projects/:projectId/config', async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user.userId;

    const { data, error } = await supabaseAdmin
      .from('project_schedules')
      .select('*')
      .eq('project_id', projectId)
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    res.json({
      success: true,
      data: data || null
    });

  } catch (error) {
    console.error('Error getting schedule config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get schedule configuration'
    });
  }
});

/**
 * Update project schedule configuration
 * PUT /api/smart-scheduling/projects/:projectId/config
 */
router.put('/projects/:projectId/config', async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user.userId;
    const configData = req.body;

    const { data, error } = await supabaseAdmin
      .from('project_schedules')
      .upsert({
        project_id: projectId,
        user_id: userId,
        ...configData
      }, { onConflict: 'project_id,user_id' })
      .select()
      .single();

    if (error) throw error;

    res.json({
      success: true,
      data,
      message: 'Schedule configuration updated successfully'
    });

  } catch (error) {
    console.error('Error updating schedule config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update schedule configuration'
    });
  }
});

/**
 * Get task schedules for a project
 * GET /api/smart-scheduling/projects/:projectId/task-schedules
 */
router.get('/projects/:projectId/task-schedules', async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user.userId;

    const { data, error } = await supabaseAdmin
      .from('task_schedules')
      .select(`
        *,
        task:project_tasks(id, title, description, status, priority)
      `)
      .eq('project_tasks.project_id', projectId)
      .eq('project_tasks.user_id', userId);

    if (error) throw error;

    res.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error getting task schedules:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get task schedules'
    });
  }
});

/**
 * Create task dependency
 * POST /api/smart-scheduling/tasks/:taskId/dependencies
 */
router.post('/tasks/:taskId/dependencies', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { depends_on_task_id, dependency_type, lag_days, is_critical } = req.body;

    const { data, error } = await supabaseAdmin
      .from('task_dependencies')
      .insert({
        task_id: taskId,
        depends_on_task_id,
        dependency_type: dependency_type || 'finish_to_start',
        lag_days: lag_days || 0,
        is_critical: is_critical || false
      })
      .select()
      .single();

    if (error) throw error;

    res.json({
      success: true,
      data,
      message: 'Task dependency created successfully'
    });

  } catch (error) {
    console.error('Error creating task dependency:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create task dependency'
    });
  }
});

/**
 * Get task dependencies
 * GET /api/smart-scheduling/tasks/:taskId/dependencies
 */
router.get('/tasks/:taskId/dependencies', async (req, res) => {
  try {
    const { taskId } = req.params;

    const { data, error } = await supabaseAdmin
      .from('task_dependencies')
      .select(`
        *,
        depends_on_task:project_tasks!task_dependencies_depends_on_task_id_fkey(id, title, status)
      `)
      .eq('task_id', taskId);

    if (error) throw error;

    res.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error getting task dependencies:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get task dependencies'
    });
  }
});

/**
 * Create automation rule
 * POST /api/smart-scheduling/automation-rules
 */
router.post('/automation-rules', async (req, res) => {
  try {
    const userId = req.user.userId;
    const ruleData = {
      user_id: userId,
      ...req.body
    };

    const rule = await automationService.createAutomationRule(ruleData);

    res.status(201).json({
      success: true,
      data: rule,
      message: 'Automation rule created successfully'
    });

  } catch (error) {
    console.error('Error creating automation rule:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create automation rule'
    });
  }
});

/**
 * Get automation rules
 * GET /api/smart-scheduling/automation-rules
 */
router.get('/automation-rules', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { project_id } = req.query;

    const rules = await automationService.getAutomationRules(
      userId, 
      project_id as string
    );

    res.json({
      success: true,
      data: rules
    });

  } catch (error) {
    console.error('Error getting automation rules:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get automation rules'
    });
  }
});

/**
 * Execute automation rules manually
 * POST /api/smart-scheduling/automation-rules/execute
 */
router.post('/automation-rules/execute', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { trigger_type, trigger_data, project_id } = req.body;

    const executions = await automationService.executeAutomationRules(
      trigger_type,
      trigger_data,
      userId,
      project_id
    );

    res.json({
      success: true,
      data: executions,
      message: 'Automation rules executed successfully'
    });

  } catch (error) {
    console.error('Error executing automation rules:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to execute automation rules'
    });
  }
});

/**
 * Get project milestones
 * GET /api/smart-scheduling/projects/:projectId/milestones
 */
router.get('/projects/:projectId/milestones', async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user.userId;

    const { data, error } = await supabaseAdmin
      .from('project_milestones')
      .select('*')
      .eq('project_id', projectId)
      .eq('user_id', userId)
      .order('target_date', { ascending: true });

    if (error) throw error;

    res.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error getting project milestones:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get project milestones'
    });
  }
});

/**
 * Create project milestone
 * POST /api/smart-scheduling/projects/:projectId/milestones
 */
router.post('/projects/:projectId/milestones', async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user.userId;
    const milestoneData = {
      project_id: projectId,
      user_id: userId,
      ...req.body
    };

    const { data, error } = await supabaseAdmin
      .from('project_milestones')
      .insert(milestoneData)
      .select()
      .single();

    if (error) throw error;

    res.status(201).json({
      success: true,
      data,
      message: 'Milestone created successfully'
    });

  } catch (error) {
    console.error('Error creating milestone:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create milestone'
    });
  }
});

/**
 * Send client communication manually
 * POST /api/smart-scheduling/communications/send
 */
router.post('/communications/send', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { type, task_id, milestone_id, project_id, custom_message } = req.body;

    switch (type) {
      case 'task_completion':
        await communicationService.sendTaskCompletionNotification(task_id, userId, custom_message);
        break;
      case 'milestone_update':
        await communicationService.sendMilestoneNotification(milestone_id, userId);
        break;
      case 'project_update':
        await communicationService.sendProjectStatusUpdate(project_id, userId, req.body.status_change);
        break;
      default:
        throw new Error('Invalid communication type');
    }

    res.json({
      success: true,
      message: 'Communication sent successfully'
    });

  } catch (error) {
    console.error('Error sending communication:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send communication'
    });
  }
});

/**
 * Get scheduled communications
 * GET /api/smart-scheduling/communications/scheduled
 */
router.get('/communications/scheduled', async (req, res) => {
  try {
    const userId = req.user.userId;

    const { data, error } = await supabaseAdmin
      .from('scheduled_communications')
      .select(`
        *,
        project:projects(name, client_name),
        task:project_tasks(title)
      `)
      .eq('user_id', userId)
      .order('scheduled_for', { ascending: true });

    if (error) throw error;

    res.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error getting scheduled communications:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get scheduled communications'
    });
  }
});

/**
 * Get automation execution history
 * GET /api/smart-scheduling/automation-rules/executions
 */
router.get('/automation-rules/executions', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { limit = 50 } = req.query;

    const { data, error } = await supabaseAdmin
      .from('automation_executions')
      .select(`
        *,
        rule:smart_automation_rules(rule_name, rule_type)
      `)
      .eq('user_id', userId)
      .order('executed_at', { ascending: false })
      .limit(parseInt(limit as string));

    if (error) throw error;

    res.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error getting automation executions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get automation executions'
    });
  }
});

export default router;
