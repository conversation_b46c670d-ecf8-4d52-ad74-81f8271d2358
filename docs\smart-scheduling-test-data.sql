-- Smart Scheduling and Automation Test Data
-- Run this after the smart-scheduling-schema.sql

-- Insert demo project schedule configuration
INSERT INTO public.project_schedules (
  id,
  project_id,
  user_id,
  schedule_name,
  auto_schedule_enabled,
  working_days,
  working_hours_start,
  working_hours_end,
  buffer_time_hours,
  client_communication_enabled,
  notification_settings
) VALUES (
  'demo-schedule-1',
  'demo-project-1',
  'demo-user-id',
  'Default Project Schedule',
  true,
  '{1,2,3,4,5}',
  '09:00:00',
  '17:00:00',
  2.0,
  true,
  '{"task_completion": true, "milestone_reached": true, "deadline_approaching": true}'
);

-- Insert demo task dependencies
INSERT INTO public.task_dependencies (
  id,
  task_id,
  depends_on_task_id,
  dependency_type,
  lag_days,
  is_critical
) VALUES 
  ('dep-1', 'demo-task-2', 'demo-task-1', 'finish_to_start', 1, true),
  ('dep-2', 'demo-task-3', 'demo-task-2', 'finish_to_start', 0, false),
  ('dep-3', 'demo-task-4', 'demo-task-1', 'start_to_start', 2, true);

-- Insert demo task schedules
INSERT INTO public.task_schedules (
  id,
  task_id,
  project_schedule_id,
  estimated_hours,
  scheduled_start,
  scheduled_end,
  is_milestone,
  auto_scheduled,
  client_visible
) VALUES 
  (
    'schedule-1',
    'demo-task-1',
    'demo-schedule-1',
    16.0,
    NOW() + INTERVAL '1 day',
    NOW() + INTERVAL '3 days',
    false,
    true,
    true
  ),
  (
    'schedule-2',
    'demo-task-2',
    'demo-schedule-1',
    24.0,
    NOW() + INTERVAL '4 days',
    NOW() + INTERVAL '7 days',
    true,
    true,
    true
  ),
  (
    'schedule-3',
    'demo-task-3',
    'demo-schedule-1',
    8.0,
    NOW() + INTERVAL '8 days',
    NOW() + INTERVAL '9 days',
    false,
    true,
    true
  );

-- Insert demo smart automation rules
INSERT INTO public.smart_automation_rules (
  id,
  user_id,
  project_id,
  rule_name,
  rule_type,
  trigger_conditions,
  actions,
  client_communication_config,
  is_active,
  priority,
  execution_count,
  success_count,
  failure_count,
  last_executed
) VALUES 
  (
    'rule-1',
    'demo-user-id',
    'demo-project-1',
    'Task Completion Notification',
    'task_completion',
    '{"task_status": "completed"}',
    '[
      {
        "type": "send_client_email",
        "subject": "Task Completed: {{task_title}}",
        "content": "Hi {{client_name}},\n\nGreat news! I have completed the task \"{{task_title}}\" for your {{project_name}} project.\n\nTask Details:\n• Title: {{task_title}}\n• Description: {{task_description}}\n• Completed: {{completion_date}}\n\nThe project is progressing well, and I will keep you updated on the next milestones.\n\nBest regards,\n{{freelancer_name}}",
        "delay": 0
      }
    ]',
    '{"template_id": "task_completion", "send_timing": "immediate"}',
    true,
    1,
    15,
    14,
    1,
    NOW() - INTERVAL '2 hours'
  ),
  (
    'rule-2',
    'demo-user-id',
    'demo-project-1',
    'Deadline Reminder',
    'deadline_approaching',
    '{"days_until_deadline": 3}',
    '[
      {
        "type": "send_client_email",
        "subject": "Upcoming Deadline: {{task_title}}",
        "content": "Hi {{client_name}},\n\nThis is a friendly reminder about an upcoming deadline for your {{project_name}} project.\n\nTask Details:\n• Title: {{task_title}}\n• Due Date: {{due_date}}\n• Days Remaining: {{days_until_deadline}}\n\nEverything is on track, and I am committed to meeting this deadline. I will keep you updated on the progress.\n\nBest regards,\n{{freelancer_name}}",
        "delay": 0
      }
    ]',
    '{"template_id": "deadline_reminder", "send_timing": "immediate"}',
    true,
    2,
    8,
    8,
    0,
    NOW() - INTERVAL '1 day'
  ),
  (
    'rule-3',
    'demo-user-id',
    'demo-project-1',
    'Milestone Achievement',
    'milestone_reached',
    '{"milestone_percentage": 50}',
    '[
      {
        "type": "send_client_email",
        "subject": "Milestone Reached: {{milestone_name}}",
        "content": "Hi {{client_name}},\n\nExcellent progress! We have reached an important milestone in your {{project_name}} project.\n\nMilestone Details:\n• Name: {{milestone_name}}\n• Description: {{milestone_description}}\n• Completion: {{completion_percentage}}%\n• Target Date: {{target_date}}\n\nThis milestone represents a significant step forward in delivering your project on time and to your specifications.\n\nBest regards,\n{{freelancer_name}}",
        "delay": 0
      },
      {
        "type": "create_task",
        "title": "Review milestone deliverables",
        "description": "Review and document milestone completion for {{milestone_name}}",
        "priority": "medium",
        "due_date": {"relative": true, "days": 2}
      }
    ]',
    '{"template_id": "milestone_update", "send_timing": "immediate"}',
    true,
    1,
    5,
    5,
    0,
    NOW() - INTERVAL '3 days'
  );

-- Insert demo email templates
INSERT INTO public.automation_email_templates (
  id,
  user_id,
  template_name,
  template_type,
  subject_template,
  body_template,
  variables,
  send_timing,
  is_default
) VALUES 
  (
    'template-1',
    'demo-user-id',
    'Task Completion Notification',
    'task_completion',
    '✅ Task Completed: {{task_title}} - {{project_name}}',
    'Hi {{client_name}},\n\nGreat news! I have completed the task "{{task_title}}" for your {{project_name}} project.\n\nTask Details:\n• Title: {{task_title}}\n• Description: {{task_description}}\n• Completed: {{completion_date}}\n\n{{custom_message}}\n\nThe project is progressing well, and I will keep you updated on the next milestones.\n\nBest regards,\n{{freelancer_name}}\n\n---\nThis update was sent automatically by KaiNote. You can view the full project details in your client portal.',
    '["client_name", "project_name", "task_title", "task_description", "completion_date", "freelancer_name", "custom_message"]',
    'immediate',
    true
  ),
  (
    'template-2',
    'demo-user-id',
    'Milestone Update',
    'milestone_update',
    '🎯 Milestone Reached: {{milestone_name}} - {{project_name}}',
    'Hi {{client_name}},\n\nExcellent progress! We have reached an important milestone in your {{project_name}} project.\n\nMilestone Details:\n• Name: {{milestone_name}}\n• Description: {{milestone_description}}\n• Completion: {{completion_percentage}}%\n• Target Date: {{target_date}}\n\nThis milestone represents a significant step forward in delivering your project on time and to your specifications.\n\nBest regards,\n{{freelancer_name}}',
    '["client_name", "project_name", "milestone_name", "milestone_description", "completion_percentage", "target_date", "freelancer_name"]',
    'immediate',
    true
  ),
  (
    'template-3',
    'demo-user-id',
    'Deadline Reminder',
    'deadline_reminder',
    '⏰ Upcoming Deadline: {{task_title}} - {{project_name}}',
    'Hi {{client_name}},\n\nThis is a friendly reminder about an upcoming deadline for your {{project_name}} project.\n\nTask Details:\n• Title: {{task_title}}\n• Due Date: {{due_date}}\n• Days Remaining: {{days_until_deadline}}\n\nEverything is on track, and I am committed to meeting this deadline. I will keep you updated on the progress.\n\nBest regards,\n{{freelancer_name}}',
    '["client_name", "project_name", "task_title", "due_date", "days_until_deadline", "freelancer_name"]',
    'immediate',
    true
  );

-- Insert demo scheduled communications
INSERT INTO public.scheduled_communications (
  id,
  user_id,
  project_id,
  task_id,
  automation_rule_id,
  template_id,
  recipient_email,
  recipient_name,
  subject,
  content,
  communication_type,
  scheduled_for,
  status
) VALUES 
  (
    'comm-1',
    'demo-user-id',
    'demo-project-1',
    'demo-task-1',
    'rule-1',
    'template-1',
    '<EMAIL>',
    'Demo Client',
    'Weekly Project Update - Demo Project',
    'Hi Demo Client,\n\nHere is your weekly project update...',
    'email',
    NOW() + INTERVAL '2 hours',
    'scheduled'
  ),
  (
    'comm-2',
    'demo-user-id',
    'demo-project-1',
    'demo-task-2',
    'rule-1',
    'template-1',
    '<EMAIL>',
    'Demo Client',
    'Task Completed: User Authentication System',
    'Hi Demo Client,\n\nGreat news! I have completed the User Authentication System...',
    'email',
    NOW() + INTERVAL '1 day',
    'scheduled'
  ),
  (
    'comm-3',
    'demo-user-id',
    'demo-project-1',
    NULL,
    'rule-3',
    'template-2',
    '<EMAIL>',
    'Demo Client',
    'Milestone Reached: Phase 1 Complete',
    'Hi Demo Client,\n\nExcellent progress! We have reached the Phase 1 milestone...',
    'email',
    NOW() - INTERVAL '2 hours',
    'sent'
  );

-- Insert demo project milestones
INSERT INTO public.project_milestones (
  id,
  project_id,
  user_id,
  milestone_name,
  description,
  target_date,
  status,
  completion_percentage,
  associated_tasks,
  client_notification_sent,
  is_critical
) VALUES 
  (
    'milestone-1',
    'demo-project-1',
    'demo-user-id',
    'Phase 1: Design & Planning',
    'Complete initial design mockups and project planning',
    CURRENT_DATE + INTERVAL '1 week',
    'completed',
    100.00,
    '["demo-task-1"]',
    true,
    true
  ),
  (
    'milestone-2',
    'demo-project-1',
    'demo-user-id',
    'Phase 2: Development',
    'Implement core functionality and features',
    CURRENT_DATE + INTERVAL '3 weeks',
    'in_progress',
    60.00,
    '["demo-task-2", "demo-task-3"]',
    false,
    true
  ),
  (
    'milestone-3',
    'demo-project-1',
    'demo-user-id',
    'Phase 3: Testing & Launch',
    'Final testing, bug fixes, and project launch',
    CURRENT_DATE + INTERVAL '5 weeks',
    'pending',
    0.00,
    '["demo-task-4"]',
    false,
    false
  );
