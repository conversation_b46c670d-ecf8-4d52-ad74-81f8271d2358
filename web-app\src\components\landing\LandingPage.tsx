'use client';

import Link from 'next/link';
import {
  MicrophoneIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  EnvelopeIcon,
  BoltIcon,
  ComputerDesktopIcon,
  ChartBarIcon,
  UserGroupIcon,
  CalendarIcon,
  CogIcon,
  StarIcon,
  PlayIcon
} from '@heroicons/react/24/outline';

const coreFeatures = [
  {
    name: 'AI Meeting Intelligence',
    description: 'Record, transcribe, and extract action items from Google Meet, Zoom, and Teams automatically.',
    icon: MicrophoneIcon,
    color: 'bg-blue-500',
  },
  {
    name: 'Meeting Bot',
    description: 'Send our AI bot to join meetings for you. Get transcripts and summaries without attending.',
    icon: ComputerDesktopIcon,
    color: 'bg-purple-500',
  },
  {
    name: 'Smart Scheduling & Automation',
    description: 'Automate client communications, task scheduling, and project workflows with AI.',
    icon: BoltIcon,
    color: 'bg-yellow-500',
  },
  {
    name: 'Financial Dashboard',
    description: 'Track income, expenses, and profitability with comprehensive business analytics.',
    icon: ChartBarIcon,
    color: 'bg-green-500',
  },
  {
    name: 'Client Management',
    description: 'Professional client portals, automated invoicing, and communication tracking.',
    icon: UserGroupIcon,
    color: 'bg-indigo-500',
  },
  {
    name: 'Time Tracking',
    description: 'Automatic time tracking with project categorization and billing integration.',
    icon: ClockIcon,
    color: 'bg-red-500',
  },
];

const advancedFeatures = [
  {
    name: 'AI Document Generation',
    description: 'Generate contracts, proposals, and reports from your project data automatically.',
    icon: DocumentTextIcon,
  },
  {
    name: 'Expense Management',
    description: 'Track business expenses with receipt scanning and automatic categorization.',
    icon: CurrencyDollarIcon,
  },
  {
    name: 'Professional Invoicing',
    description: 'Create and send professional invoices with payment tracking and reminders.',
    icon: EnvelopeIcon,
  },
  {
    name: 'Task Automation',
    description: 'AI-powered task generation from meetings and project documentation.',
    icon: CogIcon,
  },
  {
    name: 'Client Communication',
    description: 'Automated professional emails and project updates for your clients.',
    icon: CalendarIcon,
  },
  {
    name: 'Business Analytics',
    description: 'Comprehensive insights into your freelance business performance.',
    icon: ChartBarIcon,
  },
];

const testimonials = [
  {
    name: 'Sarah Chen',
    role: 'UX Designer',
    content: 'KaiNote has transformed how I manage client meetings. The AI summaries are so professional that my clients are impressed!',
    rating: 5,
  },
  {
    name: 'Marcus Rodriguez',
    role: 'Web Developer',
    content: 'The meeting bot is a game-changer. I can focus on coding while it handles all my client check-ins.',
    rating: 5,
  },
  {
    name: 'Emily Johnson',
    role: 'Marketing Consultant',
    content: 'Smart scheduling saves me 10+ hours per week. The automation handles all my client communications perfectly.',
    rating: 5,
  },
];

const pricing = [
  {
    name: 'Starter',
    price: '$0',
    description: 'Perfect for trying KaiNote',
    features: [
      '5 meetings per month',
      'Basic transcription',
      'Action item extraction',
      'Email reminders',
      'Client summaries',
    ],
    cta: 'Get Started Free',
    popular: false,
  },
  {
    name: 'Professional',
    price: '$19',
    description: 'For growing freelancers',
    features: [
      'Unlimited meetings',
      'Meeting Bot automation',
      'Smart scheduling',
      'Financial dashboard',
      'Client management',
      'Time tracking',
      'AI document generation',
    ],
    cta: 'Start 14-Day Trial',
    popular: true,
  },
  {
    name: 'Business',
    price: '$39',
    description: 'For established freelancers',
    features: [
      'Everything in Professional',
      'Advanced automation',
      'Custom integrations',
      'Priority support',
      'White-label client portals',
      'Advanced analytics',
      'API access',
    ],
    cta: 'Contact Sales',
    popular: false,
  },
];

export function LandingPage() {
  return (
    <div className="bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gray-900">KaiNote</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/auth/signin"
                className="text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                Sign In
              </Link>
              <Link
                href="/auth/signup"
                className="btn btn-primary"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="relative z-10 pb-8 sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="sm:text-center lg:text-left">
                <div className="mb-6">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800">
                    <BoltIcon className="h-4 w-4 mr-2" />
                    AI-Powered Freelancer Platform
                  </span>
                </div>
                <h1 className="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                  <span className="block xl:inline">Make freelancing</span>{' '}
                  <span className="block text-primary-600 xl:inline">90% more efficient</span>
                </h1>
                <p className="mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                  The complete AI-powered platform for freelancers. Automate meetings, manage clients,
                  track finances, and grow your business with intelligent automation.
                </p>

                {/* Key Benefits */}
                <div className="mt-6 flex flex-wrap gap-4 sm:justify-center lg:justify-start">
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    AI Meeting Bot
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    Smart Automation
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    Financial Dashboard
                  </div>
                </div>

                <div className="mt-8 sm:flex sm:justify-center lg:justify-start">
                  <div className="rounded-md shadow">
                    <Link
                      href="/auth/signup"
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10 transition-colors"
                    >
                      <PlayIcon className="h-5 w-5 mr-2" />
                      Start Free Trial
                    </Link>
                  </div>
                  <div className="mt-3 sm:mt-0 sm:ml-3">
                    <Link
                      href="#demo"
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10 transition-colors"
                    >
                      Watch Demo
                    </Link>
                  </div>
                </div>

                <div className="mt-6 text-sm text-gray-500 sm:text-center lg:text-left">
                  ✨ No credit card required • 14-day free trial • Cancel anytime
                </div>
              </div>
            </main>
          </div>
        </div>
        <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
          <div className="h-56 w-full bg-gradient-to-br from-primary-400 via-purple-500 to-blue-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center relative overflow-hidden">
            {/* Animated Background Elements */}
            <div className="absolute inset-0 opacity-20">
              <div className="absolute top-10 left-10 w-20 h-20 bg-white rounded-full animate-pulse"></div>
              <div className="absolute top-32 right-16 w-16 h-16 bg-yellow-300 rounded-full animate-bounce"></div>
              <div className="absolute bottom-20 left-20 w-12 h-12 bg-green-300 rounded-full animate-ping"></div>
            </div>

            <div className="text-white text-center relative z-10">
              <div className="flex justify-center space-x-4 mb-6">
                <div className="bg-white/20 p-3 rounded-lg backdrop-blur-sm">
                  <MicrophoneIcon className="h-8 w-8" />
                </div>
                <div className="bg-white/20 p-3 rounded-lg backdrop-blur-sm">
                  <ComputerDesktopIcon className="h-8 w-8" />
                </div>
                <div className="bg-white/20 p-3 rounded-lg backdrop-blur-sm">
                  <BoltIcon className="h-8 w-8" />
                </div>
              </div>
              <p className="text-xl font-semibold mb-2">AI-Powered Freelancing</p>
              <p className="text-sm opacity-90">Record • Automate • Grow</p>
            </div>
          </div>
        </div>
      </div>

      {/* Core Features Section */}
      <div id="features" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-primary-600 font-semibold tracking-wide uppercase">Core Features</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              The complete freelancer productivity suite
            </p>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
              Everything you need to run your freelance business efficiently, powered by AI automation.
            </p>
          </div>

          <div className="mt-16">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {coreFeatures.map((feature) => (
                <div key={feature.name} className="relative group">
                  <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-6 border border-gray-100">
                    <div className={`inline-flex items-center justify-center h-12 w-12 rounded-lg ${feature.color} text-white mb-4`}>
                      <feature.icon className="h-6 w-6" aria-hidden="true" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.name}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Features Section */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center mb-12">
            <h2 className="text-base text-primary-600 font-semibold tracking-wide uppercase">Advanced Features</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Powerful tools for growing businesses
            </p>
          </div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {advancedFeatures.map((feature) => (
              <div key={feature.name} className="flex items-start space-x-4 p-4">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-primary-100 text-primary-600">
                    <feature.icon className="h-5 w-5" aria-hidden="true" />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">{feature.name}</h3>
                  <p className="mt-1 text-gray-600">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Testimonials Section */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center mb-12">
            <h2 className="text-base text-primary-600 font-semibold tracking-wide uppercase">Testimonials</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Loved by freelancers worldwide
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {testimonials.map((testimonial) => (
              <div key={testimonial.name} className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <StarIcon key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-700 mb-4">"{testimonial.content}"</p>
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-600">{testimonial.role}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div className="bg-gray-50">
        <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:py-20 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-primary-600 font-semibold tracking-wide uppercase">Pricing</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Choose the perfect plan for your business
            </p>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
              Start free, scale as you grow. All plans include our core AI features.
            </p>
          </div>

          <div className="mt-16 space-y-4 sm:mt-20 sm:space-y-0 sm:grid sm:grid-cols-3 sm:gap-6 lg:max-w-6xl lg:mx-auto">
            {pricing.map((tier) => (
              <div
                key={tier.name}
                className={`relative bg-white border rounded-xl shadow-lg divide-y divide-gray-200 ${
                  tier.popular
                    ? 'border-primary-500 ring-2 ring-primary-500 transform scale-105'
                    : 'border-gray-200'
                }`}
              >
                {tier.popular && (
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <span className="inline-flex px-4 py-1 rounded-full text-sm font-semibold tracking-wide uppercase bg-gradient-to-r from-primary-500 to-purple-600 text-white">
                      Most Popular
                    </span>
                  </div>
                )}
                <div className="p-6">
                  <h2 className="text-xl leading-6 font-bold text-gray-900">{tier.name}</h2>
                  <p className="mt-2 text-sm text-gray-500">{tier.description}</p>
                  <p className="mt-6">
                    <span className="text-4xl font-extrabold text-gray-900">{tier.price}</span>
                    {tier.price !== '$0' && <span className="text-lg font-medium text-gray-500">/month</span>}
                  </p>
                  <Link
                    href="/auth/signup"
                    className={`mt-6 block w-full border border-transparent rounded-lg py-3 px-6 text-center text-sm font-semibold transition-colors ${
                      tier.popular
                        ? 'bg-gradient-to-r from-primary-500 to-purple-600 text-white hover:from-primary-600 hover:to-purple-700'
                        : 'bg-primary-50 text-primary-700 hover:bg-primary-100'
                    }`}
                  >
                    {tier.cta}
                  </Link>
                </div>
                <div className="pt-6 pb-8 px-6">
                  <h3 className="text-xs font-medium text-gray-900 tracking-wide uppercase">What's included</h3>
                  <ul className="mt-4 space-y-3">
                    {tier.features.map((feature) => (
                      <li key={feature} className="flex items-start space-x-3">
                        <CheckCircleIcon className="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" aria-hidden="true" />
                        <span className="text-sm text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <p className="text-sm text-gray-500">
              All plans include 14-day free trial • No setup fees • Cancel anytime
            </p>
          </div>
        </div>
      </div>

      {/* Demo Section */}
      <div id="demo" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center mb-12">
            <h2 className="text-base text-primary-600 font-semibold tracking-wide uppercase">See It In Action</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Watch KaiNote transform your workflow
            </p>
          </div>

          <div className="bg-gray-900 rounded-xl overflow-hidden shadow-2xl">
            <div className="aspect-w-16 aspect-h-9 bg-gradient-to-br from-primary-400 to-purple-600 flex items-center justify-center">
              <div className="text-center text-white">
                <PlayIcon className="h-16 w-16 mx-auto mb-4 opacity-80" />
                <p className="text-xl font-semibold">Interactive Demo Coming Soon</p>
                <p className="text-sm opacity-75 mt-2">See how KaiNote automates your entire freelance workflow</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-primary-600 via-purple-600 to-blue-600">
        <div className="max-w-4xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
            <span className="block">Ready to make freelancing 90% more efficient?</span>
          </h2>
          <p className="mt-4 text-xl leading-6 text-blue-100">
            Join thousands of freelancers who are already saving 10+ hours per week with KaiNote's AI automation.
          </p>

          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/signup"
              className="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-primary-600 bg-white hover:bg-gray-50 transition-colors"
            >
              <PlayIcon className="h-5 w-5 mr-2" />
              Start Free Trial
            </Link>
            <Link
              href="/auth/signin"
              className="inline-flex items-center justify-center px-8 py-3 border-2 border-white text-base font-medium rounded-lg text-white hover:bg-white hover:text-primary-600 transition-colors"
            >
              Sign In
            </Link>
          </div>

          <div className="mt-6 text-sm text-blue-100">
            ✨ 14-day free trial • No credit card required • Setup in 2 minutes
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold text-white mb-4">KaiNote</h3>
              <p className="text-gray-400 mb-4">
                The AI-powered platform that makes freelancing 90% more efficient.
                Automate meetings, manage clients, and grow your business.
              </p>
              <div className="flex space-x-4">
                <div className="bg-primary-600 p-2 rounded-lg">
                  <MicrophoneIcon className="h-5 w-5 text-white" />
                </div>
                <div className="bg-purple-600 p-2 rounded-lg">
                  <ComputerDesktopIcon className="h-5 w-5 text-white" />
                </div>
                <div className="bg-blue-600 p-2 rounded-lg">
                  <BoltIcon className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-white font-semibold mb-4">Features</h4>
              <ul className="space-y-2 text-gray-400">
                <li>AI Meeting Bot</li>
                <li>Smart Automation</li>
                <li>Financial Dashboard</li>
                <li>Client Management</li>
              </ul>
            </div>

            <div>
              <h4 className="text-white font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li>About</li>
                <li>Privacy Policy</li>
                <li>Terms of Service</li>
                <li>Support</li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              &copy; 2024 KaiNote. All rights reserved.
            </p>
            <p className="text-gray-400 text-sm mt-4 md:mt-0">
              Built for freelancers, by freelancers. 🚀
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
