import { supabaseAdmin } from '../config/supabase';
import { addDays, format, isAfter, isBefore } from 'date-fns';
import nodemailer from 'nodemailer';

export interface EmailTemplate {
  id: string;
  template_name: string;
  template_type: 'task_completion' | 'milestone_update' | 'deadline_reminder' | 'project_update' | 'schedule_change';
  subject_template: string;
  body_template: string;
  variables: string[];
  send_timing: 'immediate' | 'daily_digest' | 'weekly_digest';
}

export interface ScheduledCommunication {
  id: string;
  user_id: string;
  project_id: string;
  task_id?: string;
  recipient_email: string;
  recipient_name?: string;
  subject: string;
  content: string;
  communication_type: 'email' | 'sms' | 'slack' | 'webhook';
  scheduled_for: Date;
  status: 'scheduled' | 'sent' | 'failed' | 'cancelled';
}

export class ClientCommunicationService {

  private emailTransporter: nodemailer.Transporter;

  constructor() {
    // Initialize email transporter (configure with your email service)
    this.emailTransporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  /**
   * Send task completion notification to client
   */
  async sendTaskCompletionNotification(
    taskId: string,
    userId: string,
    customMessage?: string
  ): Promise<void> {
    try {
      // Get task and project information
      const taskData = await this.getTaskWithProject(taskId);
      if (!taskData || !taskData.project.client_email) {
        console.log('No client email found for task completion notification');
        return;
      }

      // Get or create email template
      const template = await this.getEmailTemplate(userId, 'task_completion');
      
      // Prepare template variables
      const variables = {
        client_name: taskData.project.client_name,
        project_name: taskData.project.name,
        task_title: taskData.title,
        task_description: taskData.description,
        completion_date: format(new Date(), 'MMMM dd, yyyy'),
        freelancer_name: taskData.user.name,
        custom_message: customMessage || ''
      };

      // Generate email content
      const subject = this.interpolateTemplate(template.subject_template, variables);
      const content = this.interpolateTemplate(template.body_template, variables);

      // Schedule or send immediately
      if (template.send_timing === 'immediate') {
        await this.sendEmailImmediately(
          taskData.project.client_email,
          taskData.project.client_name,
          subject,
          content,
          userId,
          taskData.project.id,
          taskId
        );
      } else {
        await this.scheduleEmail(
          taskData.project.client_email,
          taskData.project.client_name,
          subject,
          content,
          template.send_timing,
          userId,
          taskData.project.id,
          taskId
        );
      }

    } catch (error) {
      console.error('Error sending task completion notification:', error);
      throw error;
    }
  }

  /**
   * Send milestone reached notification
   */
  async sendMilestoneNotification(
    milestoneId: string,
    userId: string
  ): Promise<void> {
    try {
      // Get milestone and project information
      const { data: milestone, error } = await supabaseAdmin
        .from('project_milestones')
        .select(`
          *,
          project:projects(id, name, client_name, client_email),
          user:users(name, email)
        `)
        .eq('id', milestoneId)
        .single();

      if (error || !milestone || !milestone.project.client_email) {
        console.log('No client email found for milestone notification');
        return;
      }

      // Get email template
      const template = await this.getEmailTemplate(userId, 'milestone_update');
      
      // Calculate completion percentage for associated tasks
      const completionPercentage = await this.calculateMilestoneCompletion(milestoneId);
      
      // Prepare template variables
      const variables = {
        client_name: milestone.project.client_name,
        project_name: milestone.project.name,
        milestone_name: milestone.milestone_name,
        milestone_description: milestone.description,
        completion_percentage: completionPercentage.toString(),
        target_date: format(new Date(milestone.target_date), 'MMMM dd, yyyy'),
        freelancer_name: milestone.user.name
      };

      // Generate email content
      const subject = this.interpolateTemplate(template.subject_template, variables);
      const content = this.interpolateTemplate(template.body_template, variables);

      // Send notification
      await this.sendEmailImmediately(
        milestone.project.client_email,
        milestone.project.client_name,
        subject,
        content,
        userId,
        milestone.project.id
      );

      // Mark milestone notification as sent
      await supabaseAdmin
        .from('project_milestones')
        .update({ client_notification_sent: true })
        .eq('id', milestoneId);

    } catch (error) {
      console.error('Error sending milestone notification:', error);
      throw error;
    }
  }

  /**
   * Send deadline reminder notification
   */
  async sendDeadlineReminder(
    taskId: string,
    userId: string,
    daysUntilDeadline: number
  ): Promise<void> {
    try {
      const taskData = await this.getTaskWithProject(taskId);
      if (!taskData || !taskData.project.client_email) return;

      const template = await this.getEmailTemplate(userId, 'deadline_reminder');
      
      const variables = {
        client_name: taskData.project.client_name,
        project_name: taskData.project.name,
        task_title: taskData.title,
        days_until_deadline: daysUntilDeadline.toString(),
        due_date: format(new Date(taskData.due_date), 'MMMM dd, yyyy'),
        freelancer_name: taskData.user.name
      };

      const subject = this.interpolateTemplate(template.subject_template, variables);
      const content = this.interpolateTemplate(template.body_template, variables);

      await this.sendEmailImmediately(
        taskData.project.client_email,
        taskData.project.client_name,
        subject,
        content,
        userId,
        taskData.project.id,
        taskId
      );

    } catch (error) {
      console.error('Error sending deadline reminder:', error);
      throw error;
    }
  }

  /**
   * Send project status update
   */
  async sendProjectStatusUpdate(
    projectId: string,
    userId: string,
    statusChange: { from: string; to: string }
  ): Promise<void> {
    try {
      // Get project information
      const { data: project, error } = await supabaseAdmin
        .from('projects')
        .select(`
          *,
          user:users(name, email)
        `)
        .eq('id', projectId)
        .single();

      if (error || !project || !project.client_email) return;

      const template = await this.getEmailTemplate(userId, 'project_update');
      
      // Get project progress summary
      const progressSummary = await this.getProjectProgressSummary(projectId);
      
      const variables = {
        client_name: project.client_name,
        project_name: project.name,
        old_status: statusChange.from,
        new_status: statusChange.to,
        total_tasks: progressSummary.totalTasks.toString(),
        completed_tasks: progressSummary.completedTasks.toString(),
        completion_percentage: progressSummary.completionPercentage.toString(),
        freelancer_name: project.user.name
      };

      const subject = this.interpolateTemplate(template.subject_template, variables);
      const content = this.interpolateTemplate(template.body_template, variables);

      await this.sendEmailImmediately(
        project.client_email,
        project.client_name,
        subject,
        content,
        userId,
        projectId
      );

    } catch (error) {
      console.error('Error sending project status update:', error);
      throw error;
    }
  }

  /**
   * Send schedule change notification
   */
  async sendScheduleChangeNotification(
    projectId: string,
    userId: string,
    changedTasks: any[]
  ): Promise<void> {
    try {
      const { data: project, error } = await supabaseAdmin
        .from('projects')
        .select(`
          *,
          user:users(name, email)
        `)
        .eq('id', projectId)
        .single();

      if (error || !project || !project.client_email) return;

      const template = await this.getEmailTemplate(userId, 'schedule_change');
      
      // Format changed tasks for email
      const taskChanges = changedTasks.map(task => 
        `• ${task.title}: ${format(new Date(task.scheduled_start), 'MMM dd')} - ${format(new Date(task.scheduled_end), 'MMM dd')}`
      ).join('\n');
      
      const variables = {
        client_name: project.client_name,
        project_name: project.name,
        task_changes: taskChanges,
        change_reason: 'Schedule optimization and dependency management',
        freelancer_name: project.user.name
      };

      const subject = this.interpolateTemplate(template.subject_template, variables);
      const content = this.interpolateTemplate(template.body_template, variables);

      await this.sendEmailImmediately(
        project.client_email,
        project.client_name,
        subject,
        content,
        userId,
        projectId
      );

    } catch (error) {
      console.error('Error sending schedule change notification:', error);
      throw error;
    }
  }

  /**
   * Process scheduled communications
   */
  async processScheduledCommunications(): Promise<void> {
    try {
      // Get communications scheduled for now or earlier
      const { data: communications, error } = await supabaseAdmin
        .from('scheduled_communications')
        .select('*')
        .eq('status', 'scheduled')
        .lte('scheduled_for', new Date().toISOString())
        .order('scheduled_for', { ascending: true });

      if (error) throw error;

      for (const comm of communications || []) {
        try {
          await this.sendScheduledCommunication(comm);
        } catch (error) {
          console.error(`Failed to send communication ${comm.id}:`, error);
          await this.markCommunicationFailed(comm.id, error.message);
        }
      }

    } catch (error) {
      console.error('Error processing scheduled communications:', error);
    }
  }

  /**
   * Get email template or create default
   */
  private async getEmailTemplate(userId: string, templateType: string): Promise<EmailTemplate> {
    const { data, error } = await supabaseAdmin
      .from('automation_email_templates')
      .select('*')
      .eq('user_id', userId)
      .eq('template_type', templateType)
      .single();

    if (error || !data) {
      return this.getDefaultEmailTemplate(templateType);
    }

    return data;
  }

  /**
   * Get default email templates
   */
  private getDefaultEmailTemplate(templateType: string): EmailTemplate {
    const templates = {
      task_completion: {
        id: 'default_task_completion',
        template_name: 'Task Completion Notification',
        template_type: 'task_completion' as const,
        subject_template: '✅ Task Completed: {{task_title}} - {{project_name}}',
        body_template: `Hi {{client_name}},

Great news! I've completed the task "{{task_title}}" for your {{project_name}} project.

Task Details:
• Title: {{task_title}}
• Description: {{task_description}}
• Completed: {{completion_date}}

{{custom_message}}

The project is progressing well, and I'll keep you updated on the next milestones.

Best regards,
{{freelancer_name}}

---
This update was sent automatically by KaiNote. You can view the full project details in your client portal.`,
        variables: ['client_name', 'project_name', 'task_title', 'task_description', 'completion_date', 'freelancer_name', 'custom_message'],
        send_timing: 'immediate' as const
      },
      milestone_update: {
        id: 'default_milestone_update',
        template_name: 'Milestone Update',
        template_type: 'milestone_update' as const,
        subject_template: '🎯 Milestone Reached: {{milestone_name}} - {{project_name}}',
        body_template: `Hi {{client_name}},

Excellent progress! We've reached an important milestone in your {{project_name}} project.

Milestone Details:
• Name: {{milestone_name}}
• Description: {{milestone_description}}
• Completion: {{completion_percentage}}%
• Target Date: {{target_date}}

This milestone represents a significant step forward in delivering your project on time and to your specifications.

Best regards,
{{freelancer_name}}`,
        variables: ['client_name', 'project_name', 'milestone_name', 'milestone_description', 'completion_percentage', 'target_date', 'freelancer_name'],
        send_timing: 'immediate' as const
      },
      deadline_reminder: {
        id: 'default_deadline_reminder',
        template_name: 'Deadline Reminder',
        template_type: 'deadline_reminder' as const,
        subject_template: '⏰ Upcoming Deadline: {{task_title}} - {{project_name}}',
        body_template: `Hi {{client_name}},

This is a friendly reminder about an upcoming deadline for your {{project_name}} project.

Task Details:
• Title: {{task_title}}
• Due Date: {{due_date}}
• Days Remaining: {{days_until_deadline}}

Everything is on track, and I'm committed to meeting this deadline. I'll keep you updated on the progress.

Best regards,
{{freelancer_name}}`,
        variables: ['client_name', 'project_name', 'task_title', 'due_date', 'days_until_deadline', 'freelancer_name'],
        send_timing: 'immediate' as const
      },
      project_update: {
        id: 'default_project_update',
        template_name: 'Project Status Update',
        template_type: 'project_update' as const,
        subject_template: '📊 Project Update: {{project_name}} Status Changed',
        body_template: `Hi {{client_name}},

I wanted to update you on the status of your {{project_name}} project.

Status Change: {{old_status}} → {{new_status}}

Current Progress:
• Total Tasks: {{total_tasks}}
• Completed Tasks: {{completed_tasks}}
• Overall Completion: {{completion_percentage}}%

The project continues to progress smoothly, and I'm excited about the results we're achieving together.

Best regards,
{{freelancer_name}}`,
        variables: ['client_name', 'project_name', 'old_status', 'new_status', 'total_tasks', 'completed_tasks', 'completion_percentage', 'freelancer_name'],
        send_timing: 'immediate' as const
      },
      schedule_change: {
        id: 'default_schedule_change',
        template_name: 'Schedule Change Notification',
        template_type: 'schedule_change' as const,
        subject_template: '📅 Schedule Update: {{project_name}}',
        body_template: `Hi {{client_name}},

I wanted to inform you about some schedule adjustments for your {{project_name}} project.

Updated Task Schedule:
{{task_changes}}

Reason for Changes: {{change_reason}}

These adjustments will help ensure we deliver the highest quality work while maintaining our timeline commitments.

Best regards,
{{freelancer_name}}`,
        variables: ['client_name', 'project_name', 'task_changes', 'change_reason', 'freelancer_name'],
        send_timing: 'immediate' as const
      }
    };

    return templates[templateType] || templates.task_completion;
  }

  /**
   * Send email immediately
   */
  private async sendEmailImmediately(
    recipientEmail: string,
    recipientName: string,
    subject: string,
    content: string,
    userId: string,
    projectId: string,
    taskId?: string
  ): Promise<void> {
    try {
      // In demo mode, just log the email
      if (process.env.NODE_ENV === 'development') {
        console.log('📧 Email would be sent:', {
          to: recipientEmail,
          subject,
          content: content.substring(0, 200) + '...'
        });
        return;
      }

      // Send actual email in production
      await this.emailTransporter.sendMail({
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: recipientEmail,
        subject,
        text: content,
        html: this.convertToHtml(content)
      });

      // Log successful communication
      await this.logCommunication(
        userId,
        projectId,
        taskId,
        recipientEmail,
        recipientName,
        subject,
        content,
        'sent'
      );

    } catch (error) {
      console.error('Error sending email:', error);
      
      // Log failed communication
      await this.logCommunication(
        userId,
        projectId,
        taskId,
        recipientEmail,
        recipientName,
        subject,
        content,
        'failed'
      );
      
      throw error;
    }
  }

  /**
   * Schedule email for later delivery
   */
  private async scheduleEmail(
    recipientEmail: string,
    recipientName: string,
    subject: string,
    content: string,
    timing: string,
    userId: string,
    projectId: string,
    taskId?: string
  ): Promise<void> {
    let scheduledFor = new Date();
    
    if (timing === 'daily_digest') {
      scheduledFor = addDays(scheduledFor, 1);
      scheduledFor.setHours(9, 0, 0, 0); // 9 AM next day
    } else if (timing === 'weekly_digest') {
      scheduledFor = addDays(scheduledFor, 7);
      scheduledFor.setHours(9, 0, 0, 0); // 9 AM next week
    }

    const { error } = await supabaseAdmin
      .from('scheduled_communications')
      .insert({
        user_id: userId,
        project_id: projectId,
        task_id: taskId,
        recipient_email: recipientEmail,
        recipient_name: recipientName,
        subject,
        content,
        communication_type: 'email',
        scheduled_for: scheduledFor.toISOString(),
        status: 'scheduled'
      });

    if (error) throw error;
  }

  /**
   * Helper methods
   */
  private async getTaskWithProject(taskId: string) {
    const { data, error } = await supabaseAdmin
      .from('project_tasks')
      .select(`
        *,
        project:projects(id, name, client_name, client_email),
        user:users(name, email)
      `)
      .eq('id', taskId)
      .single();

    if (error) throw error;
    return data;
  }

  private async calculateMilestoneCompletion(milestoneId: string): Promise<number> {
    // Simplified calculation - can be enhanced
    return 75; // Default completion percentage
  }

  private async getProjectProgressSummary(projectId: string) {
    const { data: tasks, error } = await supabaseAdmin
      .from('project_tasks')
      .select('status')
      .eq('project_id', projectId);

    if (error) throw error;

    const totalTasks = tasks?.length || 0;
    const completedTasks = tasks?.filter(t => t.status === 'completed').length || 0;
    const completionPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    return { totalTasks, completedTasks, completionPercentage };
  }

  private interpolateTemplate(template: string, variables: any): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] || match;
    });
  }

  private convertToHtml(text: string): string {
    return text
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>');
  }

  private async sendScheduledCommunication(comm: ScheduledCommunication): Promise<void> {
    if (comm.communication_type === 'email') {
      await this.emailTransporter.sendMail({
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: comm.recipient_email,
        subject: comm.subject,
        text: comm.content,
        html: this.convertToHtml(comm.content)
      });
    }

    // Mark as sent
    await supabaseAdmin
      .from('scheduled_communications')
      .update({ 
        status: 'sent',
        sent_at: new Date().toISOString()
      })
      .eq('id', comm.id);
  }

  private async markCommunicationFailed(commId: string, errorMessage: string): Promise<void> {
    await supabaseAdmin
      .from('scheduled_communications')
      .update({ 
        status: 'failed',
        error_message: errorMessage
      })
      .eq('id', commId);
  }

  private async logCommunication(
    userId: string,
    projectId: string,
    taskId: string | undefined,
    recipientEmail: string,
    recipientName: string,
    subject: string,
    content: string,
    status: string
  ): Promise<void> {
    await supabaseAdmin
      .from('scheduled_communications')
      .insert({
        user_id: userId,
        project_id: projectId,
        task_id: taskId,
        recipient_email: recipientEmail,
        recipient_name: recipientName,
        subject,
        content,
        communication_type: 'email',
        scheduled_for: new Date().toISOString(),
        sent_at: status === 'sent' ? new Date().toISOString() : null,
        status
      });
  }
}
