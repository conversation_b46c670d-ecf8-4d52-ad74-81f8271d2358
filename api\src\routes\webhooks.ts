import express from 'express';
import stripe, { stripeConfig } from '../config/stripe';

const router = express.Router();

// Stripe webhook handler
router.post('/stripe', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'] as string;
  let event: any;

  try {
    // Verify webhook signature
    event = stripe.webhooks.constructEvent(req.body, sig, stripeConfig.webhookSecret);
  } catch (err: any) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  console.log('Received Stripe webhook event:', event.type);

  try {
    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object);
        break;

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object);
        break;

      case 'customer.created':
        await handleCustomerCreated(event.data.object);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.json({ received: true });
  } catch (error: any) {
    console.error('Webhook handler error:', error);
    res.status(500).json({ error: 'Webhook handler failed' });
  }
});

// Webhook event handlers
async function handleCheckoutSessionCompleted(session: any) {
  console.log('Checkout session completed:', session.id);
  
  // In a real app, you would:
  // 1. Get the customer and subscription from the session
  // 2. Update the user's subscription in your database
  // 3. Send a welcome email
  // 4. Grant access to premium features
  
  const userId = session.metadata?.userId;
  const planId = session.metadata?.planId;
  
  if (userId && planId) {
    console.log(`User ${userId} subscribed to ${planId} plan`);
    // Update user subscription in database
    // await updateUserSubscription(userId, planId, session.subscription);
  }
}

async function handleSubscriptionCreated(subscription: any) {
  console.log('Subscription created:', subscription.id);
  
  // In a real app, you would:
  // 1. Update user's subscription status in database
  // 2. Grant access to premium features
  // 3. Send confirmation email
  
  const userId = subscription.metadata?.userId;
  if (userId) {
    console.log(`Subscription created for user ${userId}`);
    // await activateUserSubscription(userId, subscription);
  }
}

async function handleSubscriptionUpdated(subscription: any) {
  console.log('Subscription updated:', subscription.id);
  
  // In a real app, you would:
  // 1. Update subscription details in database
  // 2. Handle plan changes
  // 3. Update feature access
  
  const userId = subscription.metadata?.userId;
  if (userId) {
    console.log(`Subscription updated for user ${userId}`);
    // await updateUserSubscription(userId, subscription);
  }
}

async function handleSubscriptionDeleted(subscription: any) {
  console.log('Subscription deleted:', subscription.id);
  
  // In a real app, you would:
  // 1. Revoke premium access
  // 2. Update user status in database
  // 3. Send cancellation confirmation
  
  const userId = subscription.metadata?.userId;
  if (userId) {
    console.log(`Subscription canceled for user ${userId}`);
    // await deactivateUserSubscription(userId, subscription);
  }
}

async function handleInvoicePaymentSucceeded(invoice: any) {
  console.log('Invoice payment succeeded:', invoice.id);
  
  // In a real app, you would:
  // 1. Update payment status in database
  // 2. Send receipt email
  // 3. Extend subscription period
  
  const subscriptionId = invoice.subscription;
  if (subscriptionId) {
    console.log(`Payment succeeded for subscription ${subscriptionId}`);
    // await recordSuccessfulPayment(invoice);
  }
}

async function handleInvoicePaymentFailed(invoice: any) {
  console.log('Invoice payment failed:', invoice.id);
  
  // In a real app, you would:
  // 1. Update payment status in database
  // 2. Send payment failure notification
  // 3. Handle dunning management
  
  const subscriptionId = invoice.subscription;
  if (subscriptionId) {
    console.log(`Payment failed for subscription ${subscriptionId}`);
    // await handleFailedPayment(invoice);
  }
}

async function handleCustomerCreated(customer: any) {
  console.log('Customer created:', customer.id);
  
  // In a real app, you would:
  // 1. Link Stripe customer to user in database
  // 2. Update user profile with customer ID
  
  const email = customer.email;
  if (email) {
    console.log(`Customer created for email ${email}`);
    // await linkCustomerToUser(customer);
  }
}

export default router;
