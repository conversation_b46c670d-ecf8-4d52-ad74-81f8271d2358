'use client';

import { useState, useEffect } from 'react';
import {
  CreditCardIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowUpIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface Subscription {
  id: string;
  plan: string;
  status: string;
  currentPeriodEnd: string;
  transcriptionMinutesUsed: number;
  transcriptionMinutesLimit: number;
  features: Record<string, boolean>;
}

interface SubscriptionManagerProps {
  onUpgrade?: () => void;
}

export function SubscriptionManager({ onUpgrade }: SubscriptionManagerProps) {
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  useEffect(() => {
    fetchSubscription();
  }, []);

  const fetchSubscription = async () => {
    try {
      const response = await fetch('/api/subscription/current', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSubscription(data.data);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
      toast.error('Failed to load subscription information');
    } finally {
      setLoading(false);
    }
  };

  const getUsagePercentage = () => {
    if (!subscription || subscription.transcriptionMinutesLimit === -1) return 0;
    return (subscription.transcriptionMinutesUsed / subscription.transcriptionMinutesLimit) * 100;
  };

  const getUsageColor = () => {
    const percentage = getUsagePercentage();
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const formatMinutes = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return hours > 0 ? `${hours}h ${remainingMinutes}m` : `${minutes}m`;
  };

  const getPlanDisplayName = (plan: string) => {
    const names = {
      starter: 'Starter',
      professional: 'Professional',
      enterprise: 'Enterprise',
    };
    return names[plan as keyof typeof names] || plan;
  };

  const handleUpgrade = () => {
    setShowUpgradeModal(true);
    onUpgrade?.();
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center">
          <ExclamationTriangleIcon className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Subscription Found</h3>
          <p className="text-gray-600 mb-4">You don't have an active subscription.</p>
          <button
            onClick={handleUpgrade}
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700"
          >
            Choose a Plan
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Plan */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Current Plan</h3>
            <p className="text-gray-600">Manage your subscription and usage</p>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              subscription.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {subscription.status}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <div className="flex items-center space-x-3 mb-4">
              <div className="bg-primary-100 rounded-lg p-2">
                <CreditCardIcon className="h-6 w-6 text-primary-600" />
              </div>
              <div>
                <h4 className="text-xl font-bold text-gray-900">
                  {getPlanDisplayName(subscription.plan)}
                </h4>
                <p className="text-gray-600">
                  Renews on {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                </p>
              </div>
            </div>

            {subscription.plan !== 'enterprise' && (
              <button
                onClick={handleUpgrade}
                className="flex items-center space-x-2 text-primary-600 hover:text-primary-800 font-medium"
              >
                <ArrowUpIcon className="h-4 w-4" />
                <span>Upgrade Plan</span>
              </button>
            )}
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Transcription Usage</h4>
            <div className="mb-2">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>
                  {formatMinutes(subscription.transcriptionMinutesUsed)} used
                </span>
                <span>
                  {subscription.transcriptionMinutesLimit === -1 
                    ? 'Unlimited' 
                    : `${formatMinutes(subscription.transcriptionMinutesLimit)} total`
                  }
                </span>
              </div>
              {subscription.transcriptionMinutesLimit !== -1 && (
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${getUsageColor()}`}
                    style={{ width: `${Math.min(getUsagePercentage(), 100)}%` }}
                  ></div>
                </div>
              )}
            </div>
            
            {getUsagePercentage() >= 90 && subscription.transcriptionMinutesLimit !== -1 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mt-3">
                <div className="flex items-center">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-2" />
                  <span className="text-red-700 text-sm font-medium">
                    You're running low on transcription minutes
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Features */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Plan Features</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-center">
              <CheckCircleIcon className={`h-5 w-5 mr-3 ${
                subscription.features.liveTranscription ? 'text-green-500' : 'text-gray-300'
              }`} />
              <span className={`text-sm ${
                subscription.features.liveTranscription ? 'text-gray-900' : 'text-gray-500'
              }`}>
                Live Real-time Transcription
              </span>
              {!subscription.features.liveTranscription && (
                <span className="ml-2 text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
                  PRO
                </span>
              )}
            </div>
            
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
              <span className="text-sm text-gray-900">Meeting Recording & AI Summaries</span>
            </div>
            
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
              <span className="text-sm text-gray-900">Project & Time Management</span>
            </div>
            
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
              <span className="text-sm text-gray-900">Financial Management</span>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center">
              <CheckCircleIcon className={`h-5 w-5 mr-3 ${
                subscription.features.workflowAutomation ? 'text-green-500' : 'text-gray-300'
              }`} />
              <span className={`text-sm ${
                subscription.features.workflowAutomation ? 'text-gray-900' : 'text-gray-500'
              }`}>
                Workflow Automation
              </span>
              {!subscription.features.workflowAutomation && (
                <span className="ml-2 text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
                  PRO
                </span>
              )}
            </div>
            
            <div className="flex items-center">
              <CheckCircleIcon className={`h-5 w-5 mr-3 ${
                subscription.features.portfolioGeneration ? 'text-green-500' : 'text-gray-300'
              }`} />
              <span className={`text-sm ${
                subscription.features.portfolioGeneration ? 'text-gray-900' : 'text-gray-500'
              }`}>
                Portfolio Website Generation
              </span>
              {!subscription.features.portfolioGeneration && (
                <span className="ml-2 text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
                  PRO
                </span>
              )}
            </div>
            
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
              <span className="text-sm text-gray-900">Client Portals</span>
            </div>
            
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
              <span className="text-sm text-gray-900">Smart Calendar</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={handleUpgrade}
            className="flex items-center justify-center space-x-2 p-3 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            <ArrowUpIcon className="h-5 w-5 text-gray-600" />
            <span className="text-sm font-medium text-gray-900">Upgrade Plan</span>
          </button>
          
          <button className="flex items-center justify-center space-x-2 p-3 border border-gray-300 rounded-lg hover:bg-gray-50">
            <DocumentTextIcon className="h-5 w-5 text-gray-600" />
            <span className="text-sm font-medium text-gray-900">Billing History</span>
          </button>
          
          <button className="flex items-center justify-center space-x-2 p-3 border border-gray-300 rounded-lg hover:bg-gray-50">
            <CreditCardIcon className="h-5 w-5 text-gray-600" />
            <span className="text-sm font-medium text-gray-900">Update Payment</span>
          </button>
        </div>
      </div>
    </div>
  );
}
