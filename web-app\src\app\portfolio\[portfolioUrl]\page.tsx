'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { 
  EnvelopeIcon,
  GlobeAltIcon,
  MapPinIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  StarIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import Image from 'next/image';

interface PortfolioData {
  freelancer: {
    id: string;
    displayName: string;
    title: string;
    bio: string;
    email: string;
    location: string;
    website: string;
    skills: string[];
  };
  projects: Array<{
    id: string;
    name: string;
    client_name: string;
    description: string;
    technologies: string[];
    status: string;
    start_date: string;
    end_date: string;
    budget: number;
    images: string[];
    highlights: string[];
    testimonial: {
      text: string;
      author: string;
      position: string;
    };
  }>;
  customization: {
    theme: string;
    primaryColor: string;
    showContactInfo: boolean;
    showProjectDetails: boolean;
    showClientTestimonials: boolean;
  };
  stats: {
    totalProjects: number;
    completedProjects: number;
    happyClients: number;
    yearsExperience: number;
  };
}

export default function PublicPortfolioPage() {
  const params = useParams();
  const portfolioUrl = params.portfolioUrl as string;
  const [portfolioData, setPortfolioData] = useState<PortfolioData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProject, setSelectedProject] = useState(0);

  useEffect(() => {
    fetchPortfolioData();
  }, [portfolioUrl]);

  const fetchPortfolioData = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/portfolio/public/${portfolioUrl}`);
      
      if (response.ok) {
        const data = await response.json();
        setPortfolioData(data.data);
      } else {
        setError('Portfolio not found');
      }
    } catch (error) {
      console.error('Error fetching portfolio:', error);
      setError('Failed to load portfolio');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !portfolioData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Portfolio Not Found</h1>
          <p className="text-gray-600">{error || 'The portfolio you\'re looking for doesn\'t exist.'}</p>
        </div>
      </div>
    );
  }

  const { freelancer, projects, customization, stats } = portfolioData;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto mb-4 flex items-center justify-center">
              <span className="text-2xl font-bold text-white">
                {freelancer.displayName.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">{freelancer.displayName}</h1>
            <p className="text-xl text-gray-600 mb-4">{freelancer.title}</p>
            <p className="text-gray-700 max-w-3xl mx-auto mb-6">{freelancer.bio}</p>
            
            {customization.showContactInfo && (
              <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-600">
                <div className="flex items-center">
                  <EnvelopeIcon className="h-4 w-4 mr-2" />
                  <a href={`mailto:${freelancer.email}`} className="hover:text-blue-600">
                    {freelancer.email}
                  </a>
                </div>
                <div className="flex items-center">
                  <MapPinIcon className="h-4 w-4 mr-2" />
                  {freelancer.location}
                </div>
                <div className="flex items-center">
                  <GlobeAltIcon className="h-4 w-4 mr-2" />
                  <a href={freelancer.website} target="_blank" rel="noopener noreferrer" className="hover:text-blue-600">
                    Portfolio Website
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">{stats.totalProjects}</div>
            <div className="text-sm text-gray-600">Total Projects</div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">{stats.completedProjects}</div>
            <div className="text-sm text-gray-600">Completed</div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">{stats.happyClients}</div>
            <div className="text-sm text-gray-600">Happy Clients</div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">{stats.yearsExperience}</div>
            <div className="text-sm text-gray-600">Years Experience</div>
          </div>
        </div>

        {/* Skills */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Skills & Technologies</h2>
          <div className="flex flex-wrap gap-3">
            {freelancer.skills.map((skill, index) => (
              <span
                key={index}
                className="px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
              >
                {skill}
              </span>
            ))}
          </div>
        </div>

        {/* Featured Projects */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured Projects</h2>
          
          {projects.map((project, index) => (
            <div key={project.id} className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8 overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
                {/* Project Images */}
                <div className="relative">
                  {project.images.length > 0 && (
                    <div className="relative h-64 rounded-lg overflow-hidden">
                      <Image
                        src={project.images[0]}
                        alt={project.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  {project.images.length > 1 && (
                    <div className="flex mt-4 space-x-2">
                      {project.images.slice(1).map((image, imgIndex) => (
                        <div key={imgIndex} className="relative w-20 h-20 rounded-lg overflow-hidden">
                          <Image
                            src={image}
                            alt={`${project.name} ${imgIndex + 2}`}
                            fill
                            className="object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Project Details */}
                <div>
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{project.name}</h3>
                      <p className="text-gray-600 mb-2">Client: {project.client_name}</p>
                    </div>
                    <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">
                      {project.status}
                    </span>
                  </div>

                  <p className="text-gray-700 mb-4">{project.description}</p>

                  {customization.showProjectDetails && (
                    <>
                      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                        <div className="flex items-center text-gray-600">
                          <CalendarIcon className="h-4 w-4 mr-2" />
                          {formatDate(project.start_date)} - {formatDate(project.end_date)}
                        </div>
                        <div className="flex items-center text-gray-600">
                          <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                          {formatCurrency(project.budget)}
                        </div>
                      </div>

                      {/* Technologies */}
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Technologies Used:</h4>
                        <div className="flex flex-wrap gap-2">
                          {project.technologies.map((tech, techIndex) => (
                            <span
                              key={techIndex}
                              className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs"
                            >
                              {tech}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Highlights */}
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Key Achievements:</h4>
                        <ul className="text-sm text-gray-700 space-y-1">
                          {project.highlights.map((highlight, highlightIndex) => (
                            <li key={highlightIndex} className="flex items-start">
                              <span className="text-green-500 mr-2">•</span>
                              {highlight}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </>
                  )}

                  {/* Client Testimonial */}
                  {customization.showClientTestimonials && project.testimonial && (
                    <div className="bg-gray-50 rounded-lg p-4 mt-4">
                      <div className="flex items-start">
                        <div className="flex text-yellow-400 mr-3 mt-1">
                          {[...Array(5)].map((_, i) => (
                            <StarIcon key={i} className="h-4 w-4 fill-current" />
                          ))}
                        </div>
                        <div className="flex-1">
                          <p className="text-gray-700 italic mb-2">"{project.testimonial.text}"</p>
                          <div className="text-sm">
                            <p className="font-medium text-gray-900">{project.testimonial.author}</p>
                            <p className="text-gray-600">{project.testimonial.position}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Contact CTA */}
        {customization.showContactInfo && (
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-center text-white">
            <h2 className="text-2xl font-bold mb-4">Ready to Work Together?</h2>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              I'm always excited to take on new challenges and help bring your ideas to life. 
              Let's discuss how we can work together on your next project.
            </p>
            <a
              href={`mailto:${freelancer.email}?subject=Project Inquiry&body=Hi ${freelancer.displayName}, I saw your portfolio and would like to discuss a potential project.`}
              className="inline-flex items-center px-6 py-3 bg-white text-blue-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              <EnvelopeIcon className="h-5 w-5 mr-2" />
              Get In Touch
            </a>
          </div>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-600 text-sm">
            Portfolio powered by <span className="font-semibold text-blue-600">KaiNote</span>
          </p>
        </div>
      </footer>
    </div>
  );
}
