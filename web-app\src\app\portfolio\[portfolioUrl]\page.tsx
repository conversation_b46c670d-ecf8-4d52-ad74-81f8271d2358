'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import ModernTemplate from '@/components/portfolio/templates/ModernTemplate';
import ProfessionalTemplate from '@/components/portfolio/templates/ProfessionalTemplate';
import CreativeTemplate from '@/components/portfolio/templates/CreativeTemplate';

interface PortfolioData {
  freelancer: {
    id: string;
    displayName: string;
    title: string;
    bio: string;
    email: string;
    location: string;
    website: string;
    skills: string[];
  };
  projects: Array<{
    id: string;
    name: string;
    client_name: string;
    description: string;
    technologies: string[];
    status: string;
    start_date: string;
    end_date: string;
    budget: number;
    images: string[];
    highlights: string[];
    testimonial: {
      text: string;
      author: string;
      position: string;
    };
  }>;
  customization: {
    template: string;
    primaryColor: string;
    secondaryColor: string;
    showContactInfo: boolean;
    showProjectDetails: boolean;
    showClientTestimonials: boolean;
    showSkills: boolean;
    showStats: boolean;
    layout: string;
  };
  stats: {
    totalProjects: number;
    completedProjects: number;
    happyClients: number;
    yearsExperience: number;
  };
}

export default function PublicPortfolioPage() {
  const params = useParams();
  const portfolioUrl = params.portfolioUrl as string;
  const [portfolioData, setPortfolioData] = useState<PortfolioData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProject, setSelectedProject] = useState(0);

  useEffect(() => {
    fetchPortfolioData();
  }, [portfolioUrl]);

  const fetchPortfolioData = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/portfolio/public/${portfolioUrl}`);
      
      if (response.ok) {
        const data = await response.json();
        setPortfolioData(data.data);
      } else {
        setError('Portfolio not found');
      }
    } catch (error) {
      console.error('Error fetching portfolio:', error);
      setError('Failed to load portfolio');
    } finally {
      setLoading(false);
    }
  };

  const renderTemplate = () => {
    if (!portfolioData) return null;

    const template = portfolioData.customization.template || 'modern';

    switch (template) {
      case 'professional':
        return <ProfessionalTemplate data={portfolioData} />;
      case 'creative':
        return <CreativeTemplate data={portfolioData} />;
      case 'modern':
      default:
        return <ModernTemplate data={portfolioData} />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !portfolioData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Portfolio Not Found</h1>
          <p className="text-gray-600">{error || 'The portfolio you\'re looking for doesn\'t exist.'}</p>
        </div>
      </div>
    );
  }

  return renderTemplate();
}
