const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3003';

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const token = localStorage.getItem('token');

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Auth endpoints
  async login(email: string, password: string) {
    return this.request('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async register(email: string, password: string, name: string) {
    return this.request('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({ email, password, name }),
    });
  }

  // Projects
  async getProjects() {
    return this.request('/api/projects');
  }

  async getProject(id: string) {
    return this.request(`/api/projects/${id}`);
  }

  async createProject(data: any) {
    return this.request('/api/projects', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateProject(id: string, data: any) {
    return this.request(`/api/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Tasks
  async getProjectTasks(projectId: string) {
    return this.request(`/api/projects/${projectId}/tasks`);
  }

  async createTask(projectId: string, data: any) {
    return this.request(`/api/projects/${projectId}/tasks`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateTask(projectId: string, taskId: string, data: any) {
    return this.request(`/api/projects/${projectId}/tasks/${taskId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Meetings
  async getMeetings() {
    return this.request('/api/meetings');
  }

  async getMeeting(id: string) {
    return this.request(`/api/meetings/${id}`);
  }

  async createMeeting(data: any) {
    return this.request('/api/meetings', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Time Tracking
  async getTimeEntries() {
    return this.request('/api/time-tracking');
  }

  async startTimer(data: any) {
    return this.request('/api/time-tracking/start', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async stopTimer(id: string) {
    return this.request(`/api/time-tracking/stop`, {
      method: 'POST',
      body: JSON.stringify({ id }),
    });
  }

  // Expenses
  async getExpenses() {
    return this.request('/api/expenses');
  }

  async createExpense(data: any) {
    return this.request('/api/expenses', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Financial
  async getFinancialDashboard() {
    return this.request('/api/financial/dashboard');
  }

  // Calendar
  async getCalendarEvents(startDate?: string, endDate?: string, view?: string) {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (view) params.append('view', view);
    
    const query = params.toString();
    return this.request(`/api/calendar/events${query ? `?${query}` : ''}`);
  }

  // Smart Scheduling
  async getAutomationRules() {
    return this.request('/api/smart-scheduling/automation-rules');
  }

  async executeAutomationRules() {
    return this.request('/api/smart-scheduling/automation-rules/execute', {
      method: 'POST',
    });
  }

  // Invoices
  async getInvoices() {
    return this.request('/api/invoices');
  }

  async createInvoice(data: any) {
    return this.request('/api/invoices', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Clients
  async getClients() {
    return this.request('/api/clients');
  }

  async createClient(data: any) {
    return this.request('/api/clients', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Portfolio
  async getPortfolios() {
    return this.request('/api/portfolio');
  }

  async createPortfolio(data: any) {
    return this.request('/api/portfolio', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // AI Features
  async generateTasks(data: any) {
    return this.request('/api/ai/generate-tasks', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async generateDocument(data: any) {
    return this.request('/api/ai/generate-document', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}

export const api = new ApiClient(API_BASE_URL);
