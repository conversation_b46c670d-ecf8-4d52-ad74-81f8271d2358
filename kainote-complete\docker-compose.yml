version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:14-alpine
    container_name: kainote-database
    environment:
      POSTGRES_DB: kainote
      POSTGRES_USER: kainote
      POSTGRES_PASSWORD: kainote_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - kainote-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U kainote -d kainote"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: kainote-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - kainote-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: kainote-backend
    environment:
      NODE_ENV: development
      PORT: 3003
      DATABASE_URL: ***************************************************/kainote
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your_super_secret_jwt_key_for_development
      JWT_EXPIRES_IN: 7d
      OPENAI_API_KEY: ${OPENAI_API_KEY:-your_openai_api_key_here}
      FRONTEND_URL: http://localhost:3001
      SMTP_HOST: ${SMTP_HOST:-smtp.gmail.com}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USER: ${SMTP_USER:-<EMAIL>}
      SMTP_PASS: ${SMTP_PASS:-your_app_password}
      MAX_FILE_SIZE: 10485760
      ALLOWED_FILE_TYPES: pdf,doc,docx,txt,jpg,jpeg,png
      LOG_LEVEL: info
      ENABLE_DEMO_MODE: true
    ports:
      - "3003:3003"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - uploads_data:/app/uploads
    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - kainote-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: kainote-frontend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:3003
      NEXT_PUBLIC_WS_URL: ws://localhost:3003
      NEXT_PUBLIC_APP_NAME: KaiNote
      NEXT_PUBLIC_APP_URL: http://localhost:3001
      NEXT_PUBLIC_ENABLE_DEMO_MODE: true
      NEXT_PUBLIC_ENABLE_STRIPE: false
    ports:
      - "3001:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - kainote-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: kainote-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - frontend
      - backend
    networks:
      - kainote-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local

networks:
  kainote-network:
    driver: bridge
