import express from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHand<PERSON> } from '../middleware/errorHandler';

const router = express.Router();

// Get calendar events
router.get('/events', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { start_date, end_date, view = 'month' } = req.query;
  
  // Calculate date range
  const startDate = start_date ? new Date(start_date as string) : new Date();
  const endDate = end_date ? new Date(end_date as string) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
  
  // Mock calendar events combining tasks, meetings, and time blocks
  const calendarEvents = [
    // Task Deadlines
    {
      id: 'task-deadline-1',
      type: 'task_deadline',
      title: 'Design System Creation - Due',
      description: 'Deadline for comprehensive design system with components and guidelines',
      start_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      end_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(),
      all_day: false,
      status: 'upcoming',
      priority: 'high',
      project: {
        id: 'demo-project-1',
        name: 'E-commerce Platform Redesign',
        client_name: 'TechCorp Solutions'
      },
      task: {
        id: '550e8400-e29b-41d4-a716-************',
        title: 'Design System Creation',
        estimated_hours: 40.0,
        actual_hours: 24.5,
        progress: 61.25
      },
      color: '#ef4444'
    },
    // Work Blocks
    {
      id: 'work-block-1',
      type: 'work_block',
      title: 'Design System Work',
      description: 'Focused work session on design system components',
      start_time: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000 + 9 * 60 * 60 * 1000).toISOString(),
      end_time: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000 + 13 * 60 * 60 * 1000).toISOString(),
      all_day: false,
      status: 'scheduled',
      priority: 'high',
      project: {
        id: 'demo-project-1',
        name: 'E-commerce Platform Redesign',
        client_name: 'TechCorp Solutions'
      },
      task: {
        id: '550e8400-e29b-41d4-a716-************',
        title: 'Design System Creation',
        estimated_hours: 40.0,
        actual_hours: 24.5,
        progress: 61.25
      },
      time_allocation: {
        planned_hours: 4.0,
        hourly_rate: 75.0,
        estimated_earnings: 300.0
      },
      color: '#3b82f6'
    },
    // Meetings
    {
      id: 'meeting-1',
      type: 'meeting',
      title: 'Weekly Client Check-in',
      description: 'Weekly progress review with TechCorp Solutions team',
      start_time: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000 + 14 * 60 * 60 * 1000).toISOString(),
      end_time: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000 + 15 * 60 * 60 * 1000).toISOString(),
      all_day: false,
      status: 'scheduled',
      priority: 'medium',
      project: {
        id: 'demo-project-1',
        name: 'E-commerce Platform Redesign',
        client_name: 'TechCorp Solutions'
      },
      meeting: {
        platform: 'zoom',
        meeting_url: 'https://zoom.us/j/123456789',
        attendees: ['<EMAIL>', '<EMAIL>']
      },
      color: '#10b981'
    },
    // Smart Scheduling Events
    {
      id: 'auto-schedule-1',
      type: 'auto_scheduled',
      title: 'Auto: Task Completion Email',
      description: 'Automated email to client when Design System task is completed',
      start_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 1 * 60 * 60 * 1000).toISOString(),
      end_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 1.25 * 60 * 60 * 1000).toISOString(),
      all_day: false,
      status: 'scheduled',
      priority: 'medium',
      project: {
        id: 'demo-project-1',
        name: 'E-commerce Platform Redesign',
        client_name: 'TechCorp Solutions'
      },
      automation: {
        rule_id: 'rule-1',
        rule_name: 'Task Completion Notification',
        trigger: 'task_completion',
        action: 'send_client_email',
        template: 'Task Completed: {{task_title}}',
        recipient: '<EMAIL>'
      },
      color: '#f97316'
    },
    {
      id: 'auto-schedule-4',
      type: 'smart_work_block',
      title: 'Smart: Optimal Work Time',
      description: 'AI-scheduled optimal work block for Database Migration based on productivity patterns',
      start_time: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000 + 10 * 60 * 60 * 1000).toISOString(),
      end_time: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000 + 14 * 60 * 60 * 1000).toISOString(),
      all_day: false,
      status: 'scheduled',
      priority: 'high',
      project: {
        id: 'demo-project-1',
        name: 'E-commerce Platform Redesign',
        client_name: 'TechCorp Solutions'
      },
      task: {
        id: '550e8400-e29b-41d4-a716-446655440014',
        title: 'Database Migration',
        estimated_hours: 80.0,
        actual_hours: 32.0,
        progress: 40.0
      },
      smart_scheduling: {
        algorithm: 'productivity_optimization',
        confidence_score: 0.87,
        factors: ['historical_productivity', 'task_complexity', 'deadline_pressure'],
        suggested_by: 'AI Scheduler',
        optimal_reasons: ['Peak productivity hours', 'No conflicting meetings', 'High focus task']
      },
      time_allocation: {
        planned_hours: 4.0,
        hourly_rate: 75.0,
        estimated_earnings: 300.0
      },
      color: '#06b6d4'
    }
  ];

  // Filter events by date range
  const filteredEvents = calendarEvents.filter(event => {
    const eventDate = new Date(event.start_time);
    return eventDate >= startDate && eventDate <= endDate;
  });

  // Calculate summary statistics
  const summary = {
    total_events: filteredEvents.length,
    upcoming_deadlines: filteredEvents.filter(e => e.type === 'task_deadline' && e.status === 'upcoming').length,
    scheduled_work_hours: filteredEvents
      .filter(e => e.type === 'work_block' && e.status === 'scheduled')
      .reduce((sum, e) => sum + (e.time_allocation?.planned_hours || 0), 0),
    estimated_earnings: filteredEvents
      .filter(e => e.type === 'work_block' && e.status === 'scheduled')
      .reduce((sum, e) => sum + (e.time_allocation?.estimated_earnings || 0), 0),
    meetings_count: filteredEvents.filter(e => e.type === 'meeting').length,
    completed_hours: filteredEvents
      .filter(e => e.type === 'time_session' && e.status === 'completed')
      .reduce((sum, e) => sum + (e.time_tracking?.actual_hours || 0), 0)
  };

  res.json({
    success: true,
    data: {
      events: filteredEvents,
      summary,
      date_range: {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
        view
      }
    }
  });
}));

export default router;
