'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  LockClosedIcon,
  StarIcon,
  ArrowRightIcon,
  MicrophoneIcon,
} from '@heroicons/react/24/outline';

interface FeatureGateProps {
  feature: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
}

interface FeatureAccess {
  hasAccess: boolean;
  plan: string;
  upgradeRequired: boolean;
}

export function FeatureGate({ 
  feature, 
  children, 
  fallback, 
  showUpgradePrompt = true 
}: FeatureGateProps) {
  const [featureAccess, setFeatureAccess] = useState<FeatureAccess | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkFeatureAccess();
  }, [feature]);

  const checkFeatureAccess = async () => {
    try {
      const response = await fetch(`/api/subscription/features/${feature}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setFeatureAccess(data.data);
      } else {
        // Default to no access if API fails
        setFeatureAccess({
          hasAccess: false,
          plan: 'starter',
          upgradeRequired: true,
        });
      }
    } catch (error) {
      console.error('Error checking feature access:', error);
      // Default to no access if API fails
      setFeatureAccess({
        hasAccess: false,
        plan: 'starter',
        upgradeRequired: true,
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="h-32 bg-gray-200 rounded"></div>
      </div>
    );
  }

  // If user has access, render the children
  if (featureAccess?.hasAccess) {
    return <>{children}</>;
  }

  // If fallback is provided and no upgrade prompt needed, show fallback
  if (fallback && !showUpgradePrompt) {
    return <>{fallback}</>;
  }

  // Show upgrade prompt for live transcription
  if (feature === 'liveTranscription') {
    return (
      <div className="bg-gradient-to-br from-purple-50 to-indigo-100 rounded-lg p-8 text-center">
        <div className="bg-white rounded-full p-4 w-16 h-16 mx-auto mb-6 shadow-lg">
          <MicrophoneIcon className="h-8 w-8 text-purple-600 mx-auto" />
        </div>
        
        <h3 className="text-2xl font-bold text-gray-900 mb-4">
          🔴 Live Real-Time Transcription
        </h3>
        
        <p className="text-lg text-gray-600 mb-6 max-w-md mx-auto">
          Experience revolutionary live transcription with 1-2 second latency. 
          See your words appear as you speak during meetings.
        </p>

        <div className="bg-white rounded-lg p-6 mb-6 shadow-sm">
          <h4 className="font-semibold text-gray-900 mb-3">What you get with Live Transcription:</h4>
          <ul className="text-left space-y-2 text-gray-600">
            <li className="flex items-center">
              <StarIcon className="h-4 w-4 text-yellow-500 mr-2" />
              Real-time speech-to-text with 95%+ accuracy
            </li>
            <li className="flex items-center">
              <StarIcon className="h-4 w-4 text-yellow-500 mr-2" />
              Instant action item detection during meetings
            </li>
            <li className="flex items-center">
              <StarIcon className="h-4 w-4 text-yellow-500 mr-2" />
              Live confidence scoring for transcription quality
            </li>
            <li className="flex items-center">
              <StarIcon className="h-4 w-4 text-yellow-500 mr-2" />
              Audio level monitoring and professional controls
            </li>
          </ul>
        </div>

        <div className="space-y-4">
          <Link
            href="/pricing"
            className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors"
          >
            Upgrade to Professional
            <ArrowRightIcon className="ml-2 h-5 w-5" />
          </Link>
          
          <div className="text-sm text-gray-500">
            Starting at $49/month • 3,000 transcription minutes • 14-day free trial
          </div>
        </div>
      </div>
    );
  }

  // Generic upgrade prompt for other features
  return (
    <div className="bg-gray-50 rounded-lg p-6 text-center border-2 border-dashed border-gray-300">
      <div className="bg-gray-100 rounded-full p-3 w-12 h-12 mx-auto mb-4">
        <LockClosedIcon className="h-6 w-6 text-gray-500 mx-auto" />
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Premium Feature
      </h3>
      
      <p className="text-gray-600 mb-4">
        This feature is available in Professional and Enterprise plans.
      </p>

      <div className="space-y-3">
        <Link
          href="/pricing"
          className="inline-flex items-center px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"
        >
          View Plans
          <ArrowRightIcon className="ml-2 h-4 w-4" />
        </Link>
        
        <div className="text-sm text-gray-500">
          Current plan: {featureAccess?.plan || 'Starter'}
        </div>
      </div>
    </div>
  );
}

// Hook for checking feature access in components
export function useFeatureAccess(feature: string) {
  const [featureAccess, setFeatureAccess] = useState<FeatureAccess | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAccess = async () => {
      try {
        const response = await fetch(`/api/subscription/features/${feature}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setFeatureAccess(data.data);
        }
      } catch (error) {
        console.error('Error checking feature access:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, [feature]);

  return { featureAccess, loading };
}

// Component for showing usage limits
export function UsageLimitWarning({ 
  used, 
  limit, 
  feature = 'transcription minutes' 
}: { 
  used: number; 
  limit: number; 
  feature?: string; 
}) {
  const percentage = (used / limit) * 100;
  
  if (percentage < 75) return null;

  return (
    <div className={`rounded-lg p-4 mb-4 ${
      percentage >= 90 ? 'bg-red-50 border border-red-200' : 'bg-yellow-50 border border-yellow-200'
    }`}>
      <div className="flex items-center">
        <LockClosedIcon className={`h-5 w-5 mr-3 ${
          percentage >= 90 ? 'text-red-500' : 'text-yellow-500'
        }`} />
        <div className="flex-1">
          <h4 className={`font-medium ${
            percentage >= 90 ? 'text-red-800' : 'text-yellow-800'
          }`}>
            {percentage >= 90 ? 'Usage Limit Nearly Reached' : 'High Usage Warning'}
          </h4>
          <p className={`text-sm ${
            percentage >= 90 ? 'text-red-700' : 'text-yellow-700'
          }`}>
            You've used {used} of {limit} {feature} this month ({Math.round(percentage)}%).
          </p>
        </div>
        <Link
          href="/pricing"
          className={`ml-4 px-3 py-1 rounded text-sm font-medium ${
            percentage >= 90 
              ? 'bg-red-600 text-white hover:bg-red-700' 
              : 'bg-yellow-600 text-white hover:bg-yellow-700'
          }`}
        >
          Upgrade
        </Link>
      </div>
    </div>
  );
}
