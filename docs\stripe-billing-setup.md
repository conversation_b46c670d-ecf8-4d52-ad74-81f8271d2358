# KaiNote Stripe Billing Integration

This document explains how to set up and configure Stripe billing for KaiNote.

## Overview

KaiNote includes a complete Stripe billing integration with:
- Subscription management
- Payment processing
- Customer portal
- Webhook handling
- Invoice management
- Usage tracking

## Features Implemented

### 1. Subscription Plans
- **Starter Plan**: Free tier with basic features
- **Professional Plan**: $19/month with full features
- **Business Plan**: $39/month with advanced features

### 2. Frontend Components
- `/pricing` - Pricing page with Stripe checkout
- `/billing` - Billing dashboard and subscription management
- `/billing/success` - Post-checkout success page
- `/billing/cancel` - Checkout cancellation page
- `/billing/manage` - Customer portal (demo)

### 3. API Endpoints
- `GET /api/billing/plans` - Get subscription plans
- `POST /api/billing/create-checkout-session` - Create Stripe checkout
- `POST /api/billing/create-portal-session` - Customer portal access
- `GET /api/billing/subscription` - Current subscription details
- `GET /api/billing/invoices` - Billing history
- `POST /api/billing/cancel-subscription` - Cancel subscription
- `POST /api/webhooks/stripe` - Stripe webhook handler

## Setup Instructions

### 1. Create Stripe Account
1. Sign up at [stripe.com](https://stripe.com)
2. Get your API keys from the Stripe Dashboard
3. Note your publishable and secret keys

### 2. Create Products and Prices
In your Stripe Dashboard, create:

#### Starter Plan (Free)
- Product: "KaiNote Starter"
- Price: $0.00 (one-time or recurring)
- Price ID: Copy this for `STRIPE_STARTER_PRICE_ID`

#### Professional Plan
- Product: "KaiNote Professional"
- Price: $19.00/month recurring
- Price ID: Copy this for `STRIPE_PROFESSIONAL_PRICE_ID`

#### Business Plan
- Product: "KaiNote Business"
- Price: $39.00/month recurring
- Price ID: Copy this for `STRIPE_BUSINESS_PRICE_ID`

### 3. Configure Environment Variables
Copy `api/.env.example` to `api/.env` and update:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_actual_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Stripe Price IDs
STRIPE_STARTER_PRICE_ID=price_actual_starter_id
STRIPE_PROFESSIONAL_PRICE_ID=price_actual_professional_id
STRIPE_BUSINESS_PRICE_ID=price_actual_business_id

# Frontend URL
FRONTEND_URL=http://localhost:3001
```

### 4. Set Up Webhooks
1. In Stripe Dashboard, go to Webhooks
2. Add endpoint: `https://yourdomain.com/api/webhooks/stripe`
3. Select events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.created`
4. Copy the webhook secret to `STRIPE_WEBHOOK_SECRET`

## Testing

### 1. Test Mode
- Use Stripe test keys (starting with `sk_test_` and `pk_test_`)
- Use test card numbers:
  - Success: `4242 4242 4242 4242`
  - Decline: `4000 0000 0000 0002`
  - 3D Secure: `4000 0025 0000 3155`

### 2. Test Flow
1. Go to `/pricing`
2. Click "Start Free Trial" on Professional plan
3. Complete checkout with test card
4. Verify redirect to `/billing/success`
5. Check `/billing` for subscription details

### 3. Webhook Testing
Use Stripe CLI to forward webhooks locally:
```bash
stripe listen --forward-to localhost:3003/api/webhooks/stripe
```

## Production Deployment

### 1. Environment Setup
- Replace test keys with live keys
- Update webhook endpoint to production URL
- Set `NODE_ENV=production`

### 2. Security Considerations
- Verify webhook signatures
- Use HTTPS for all endpoints
- Implement proper authentication
- Store sensitive data securely

### 3. Database Integration
Replace demo data with real database:
- User subscription status
- Customer IDs
- Payment history
- Usage tracking

## Customization

### 1. Plans and Pricing
Update `api/src/config/stripe.ts`:
```typescript
export const subscriptionPlans = {
  // Modify plans, features, and pricing
};
```

### 2. Checkout Flow
Customize checkout in `web-app/src/app/pricing/page.tsx`:
- Add custom fields
- Modify success/cancel URLs
- Add promotional codes

### 3. Customer Portal
Extend billing management in `web-app/src/app/billing/`:
- Add usage analytics
- Custom invoice templates
- Payment method management

## Troubleshooting

### Common Issues
1. **Webhook signature verification fails**
   - Check webhook secret is correct
   - Ensure raw body is passed to verification

2. **Checkout session creation fails**
   - Verify price IDs exist in Stripe
   - Check API keys are correct
   - Ensure user is authenticated

3. **Customer portal access denied**
   - Verify customer exists in Stripe
   - Check customer ID mapping

### Debug Mode
Enable debug logging:
```typescript
// In stripe config
const stripe = new Stripe(secretKey, {
  apiVersion: '2025-05-28.basil',
  typescript: true,
});
```

## Support

For Stripe-specific issues:
- [Stripe Documentation](https://stripe.com/docs)
- [Stripe Support](https://support.stripe.com)

For KaiNote billing issues:
- Check server logs
- Verify environment variables
- Test with Stripe CLI
