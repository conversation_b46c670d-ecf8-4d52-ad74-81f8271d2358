# KaiNote API Documentation

Complete API reference for KaiNote backend services.

## 🔗 Base URLs

- **Development**: `http://localhost:3003`
- **Production**: `https://api.kainote.com`
- **WebSocket**: `ws://localhost:3003` (development)

## 🔐 Authentication

All API endpoints (except auth) require JWT authentication.

### Headers
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### Getting a Token
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

## 📚 API Endpoints

### Authentication
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/auth/register` | Register new user |
| POST | `/api/auth/login` | User login |
| POST | `/api/auth/logout` | User logout |
| GET | `/api/auth/me` | Get current user |
| POST | `/api/auth/refresh` | Refresh JWT token |

### Meetings
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/meetings` | List all meetings |
| POST | `/api/meetings` | Create new meeting |
| GET | `/api/meetings/:id` | Get meeting details |
| PUT | `/api/meetings/:id` | Update meeting |
| DELETE | `/api/meetings/:id` | Delete meeting |
| POST | `/api/meetings/:id/transcribe` | Start transcription |
| GET | `/api/meetings/:id/transcript` | Get transcript |
| POST | `/api/meetings/:id/summary` | Generate AI summary |

### Live Transcription (WebSocket)
| Event | Description |
|-------|-------------|
| `connection` | Establish WebSocket connection |
| `start_transcription` | Begin live transcription |
| `audio_chunk` | Send audio data |
| `transcription_result` | Receive transcription |
| `stop_transcription` | End transcription session |

### Projects
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/projects` | List all projects |
| POST | `/api/projects` | Create new project |
| GET | `/api/projects/:id` | Get project details |
| PUT | `/api/projects/:id` | Update project |
| DELETE | `/api/projects/:id` | Delete project |
| GET | `/api/projects/:id/tasks` | Get project tasks |
| POST | `/api/projects/:id/tasks` | Create task |
| GET | `/api/projects/:id/meetings` | Get project meetings |
| GET | `/api/projects/:id/expenses` | Get project expenses |

### Tasks
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/tasks` | List all tasks |
| POST | `/api/tasks` | Create new task |
| GET | `/api/tasks/:id` | Get task details |
| PUT | `/api/tasks/:id` | Update task |
| DELETE | `/api/tasks/:id` | Delete task |
| POST | `/api/tasks/:id/complete` | Mark task complete |
| POST | `/api/tasks/generate` | AI task generation |

### Time Tracking
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/time-entries` | List time entries |
| POST | `/api/time-entries` | Create time entry |
| GET | `/api/time-entries/:id` | Get time entry |
| PUT | `/api/time-entries/:id` | Update time entry |
| DELETE | `/api/time-entries/:id` | Delete time entry |
| POST | `/api/time-entries/start` | Start time tracking |
| POST | `/api/time-entries/stop` | Stop time tracking |

### Expenses
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/expenses` | List all expenses |
| POST | `/api/expenses` | Create new expense |
| GET | `/api/expenses/:id` | Get expense details |
| PUT | `/api/expenses/:id` | Update expense |
| DELETE | `/api/expenses/:id` | Delete expense |
| POST | `/api/expenses/upload` | Upload receipt |

### Invoices
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/invoices` | List all invoices |
| POST | `/api/invoices` | Create new invoice |
| GET | `/api/invoices/:id` | Get invoice details |
| PUT | `/api/invoices/:id` | Update invoice |
| DELETE | `/api/invoices/:id` | Delete invoice |
| POST | `/api/invoices/:id/send` | Send invoice to client |
| GET | `/api/invoices/:id/pdf` | Download invoice PDF |

### Clients
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/clients` | List all clients |
| POST | `/api/clients` | Create new client |
| GET | `/api/clients/:id` | Get client details |
| PUT | `/api/clients/:id` | Update client |
| DELETE | `/api/clients/:id` | Delete client |
| GET | `/api/clients/:id/projects` | Get client projects |
| GET | `/api/clients/:id/invoices` | Get client invoices |

### Portfolio
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/portfolio` | Get portfolio data |
| PUT | `/api/portfolio` | Update portfolio |
| GET | `/api/portfolio/public/:id` | Public portfolio view |
| POST | `/api/portfolio/generate` | Generate portfolio website |

### AI Services
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/ai/transcribe` | Transcribe audio file |
| POST | `/api/ai/summarize` | Generate meeting summary |
| POST | `/api/ai/generate-tasks` | Generate tasks from content |
| POST | `/api/ai/generate-document` | Generate document |
| POST | `/api/ai/chat` | AI chat interface |

### Subscription
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/subscription/current` | Get current subscription |
| GET | `/api/subscription/plans` | Get available plans |
| GET | `/api/subscription/features/:feature` | Check feature access |
| GET | `/api/subscription/usage/transcription` | Get usage stats |
| POST | `/api/subscription/checkout` | Create checkout session |
| PUT | `/api/subscription/update` | Update subscription |
| POST | `/api/subscription/cancel` | Cancel subscription |

### Admin (Admin Only)
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/admin/dashboard` | Admin dashboard data |
| GET | `/api/admin/users` | List all users |
| GET | `/api/admin/users/:id` | Get user details |
| POST | `/api/admin/users/:id/suspend` | Suspend user |
| POST | `/api/admin/users/:id/unsuspend` | Unsuspend user |
| POST | `/api/admin/users/:id/refund` | Process refund |
| GET | `/api/admin/analytics` | Platform analytics |
| GET | `/api/admin/system` | System metrics |

## 📝 Request/Response Examples

### Create Meeting
```http
POST /api/meetings
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Client Strategy Meeting",
  "description": "Quarterly planning session",
  "scheduledFor": "2024-01-15T14:00:00Z",
  "duration": 60,
  "attendees": ["<EMAIL>"],
  "projectId": "proj_123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "meeting_456",
    "title": "Client Strategy Meeting",
    "description": "Quarterly planning session",
    "scheduledFor": "2024-01-15T14:00:00Z",
    "duration": 60,
    "status": "scheduled",
    "attendees": ["<EMAIL>"],
    "projectId": "proj_123",
    "createdAt": "2024-01-10T10:00:00Z",
    "updatedAt": "2024-01-10T10:00:00Z"
  }
}
```

### Start Live Transcription (WebSocket)
```javascript
const ws = new WebSocket('ws://localhost:3003/ws/transcription');

// Connection established
ws.onopen = () => {
  ws.send(JSON.stringify({
    type: 'start_transcription',
    meetingId: 'meeting_456',
    language: 'en-US'
  }));
};

// Send audio chunks
const sendAudioChunk = (audioBuffer) => {
  ws.send(JSON.stringify({
    type: 'audio_chunk',
    data: audioBuffer,
    timestamp: Date.now()
  }));
};

// Receive transcription results
ws.onmessage = (event) => {
  const result = JSON.parse(event.data);
  if (result.type === 'transcription_result') {
    console.log('Transcription:', result.text);
    console.log('Confidence:', result.confidence);
  }
};
```

### Generate AI Tasks
```http
POST /api/ai/generate-tasks
Authorization: Bearer <token>
Content-Type: application/json

{
  "content": "We need to redesign the company website with modern UI, implement user authentication, add payment processing, and deploy to production.",
  "projectId": "proj_123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "title": "Design Modern UI Mockups",
        "description": "Create wireframes and visual designs for the new website",
        "estimatedHours": 16,
        "priority": "high",
        "category": "design"
      },
      {
        "title": "Implement User Authentication",
        "description": "Set up secure login/registration system",
        "estimatedHours": 12,
        "priority": "high",
        "category": "development"
      },
      {
        "title": "Integrate Payment Processing",
        "description": "Add Stripe payment gateway integration",
        "estimatedHours": 8,
        "priority": "medium",
        "category": "development"
      },
      {
        "title": "Deploy to Production",
        "description": "Set up production environment and deploy application",
        "estimatedHours": 4,
        "priority": "medium",
        "category": "deployment"
      }
    ]
  }
}
```

## 🔄 WebSocket Events

### Live Transcription Events
```javascript
// Client to Server
{
  "type": "start_transcription",
  "meetingId": "meeting_123",
  "language": "en-US",
  "sampleRate": 16000
}

{
  "type": "audio_chunk",
  "data": "base64_audio_data",
  "timestamp": 1640995200000
}

{
  "type": "stop_transcription",
  "meetingId": "meeting_123"
}

// Server to Client
{
  "type": "transcription_result",
  "text": "Hello, this is a test transcription",
  "confidence": 0.95,
  "timestamp": 1640995200000,
  "isFinal": false
}

{
  "type": "transcription_complete",
  "meetingId": "meeting_123",
  "finalTranscript": "Complete meeting transcript...",
  "duration": 3600
}
```

## ❌ Error Handling

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  },
  "timestamp": "2024-01-10T10:00:00Z"
}
```

### Common Error Codes
| Code | Description |
|------|-------------|
| `UNAUTHORIZED` | Invalid or missing authentication |
| `FORBIDDEN` | Insufficient permissions |
| `VALIDATION_ERROR` | Invalid request data |
| `NOT_FOUND` | Resource not found |
| `RATE_LIMITED` | Too many requests |
| `SUBSCRIPTION_REQUIRED` | Feature requires paid subscription |
| `USAGE_LIMIT_EXCEEDED` | Monthly usage limit reached |

## 🔒 Rate Limiting

- **General API**: 100 requests per minute per user
- **Live Transcription**: 10 concurrent sessions per user
- **AI Services**: 50 requests per hour per user
- **File Uploads**: 10 uploads per minute per user

## 📊 Response Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 429 | Rate Limited |
| 500 | Internal Server Error |
