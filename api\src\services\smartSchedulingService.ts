import { supabaseAdmin } from '../config/supabase';
import { addDays, addHours, isWeekend, format, parseISO, differenceInDays } from 'date-fns';

export interface TaskDependency {
  id: string;
  task_id: string;
  depends_on_task_id: string;
  dependency_type: 'finish_to_start' | 'start_to_start' | 'finish_to_finish' | 'start_to_finish';
  lag_days: number;
  is_critical: boolean;
}

export interface TaskSchedule {
  id: string;
  task_id: string;
  estimated_hours: number;
  scheduled_start: Date;
  scheduled_end: Date;
  is_milestone: boolean;
  auto_scheduled: boolean;
  client_visible: boolean;
}

export interface ProjectScheduleConfig {
  id: string;
  project_id: string;
  auto_schedule_enabled: boolean;
  working_days: number[];
  working_hours_start: string;
  working_hours_end: string;
  buffer_time_hours: number;
  client_communication_enabled: boolean;
}

export class SmartSchedulingService {
  
  /**
   * Auto-schedule all tasks in a project based on dependencies and constraints
   */
  async scheduleProject(projectId: string, userId: string): Promise<TaskSchedule[]> {
    try {
      // Get project schedule configuration
      const scheduleConfig = await this.getProjectScheduleConfig(projectId, userId);
      
      // Get all tasks for the project
      const tasks = await this.getProjectTasks(projectId);
      
      // Get task dependencies
      const dependencies = await this.getTaskDependencies(tasks.map(t => t.id));
      
      // Build dependency graph and calculate critical path
      const dependencyGraph = this.buildDependencyGraph(tasks, dependencies);
      const criticalPath = this.calculateCriticalPath(dependencyGraph);
      
      // Schedule tasks using topological sort and resource constraints
      const scheduledTasks = await this.scheduleTasksWithConstraints(
        tasks,
        dependencies,
        scheduleConfig,
        criticalPath
      );
      
      // Save scheduled tasks to database
      await this.saveTaskSchedules(scheduledTasks);
      
      // Trigger client communication if enabled
      if (scheduleConfig.client_communication_enabled) {
        await this.triggerScheduleUpdateCommunication(projectId, userId, scheduledTasks);
      }
      
      return scheduledTasks;
      
    } catch (error) {
      console.error('Error scheduling project:', error);
      throw new Error('Failed to schedule project tasks');
    }
  }

  /**
   * Get project schedule configuration or create default
   */
  private async getProjectScheduleConfig(projectId: string, userId: string): Promise<ProjectScheduleConfig> {
    const { data, error } = await supabaseAdmin
      .from('project_schedules')
      .select('*')
      .eq('project_id', projectId)
      .eq('user_id', userId)
      .single();

    if (error || !data) {
      // Create default schedule configuration
      const defaultConfig = {
        project_id: projectId,
        user_id: userId,
        schedule_name: 'Default Schedule',
        auto_schedule_enabled: true,
        working_days: [1, 2, 3, 4, 5], // Monday to Friday
        working_hours_start: '09:00:00',
        working_hours_end: '17:00:00',
        buffer_time_hours: 2.0,
        client_communication_enabled: true,
        notification_settings: {
          task_completion: true,
          milestone_reached: true,
          deadline_approaching: true
        }
      };

      const { data: newConfig, error: createError } = await supabaseAdmin
        .from('project_schedules')
        .insert(defaultConfig)
        .select()
        .single();

      if (createError) throw createError;
      return newConfig;
    }

    return data;
  }

  /**
   * Get all tasks for a project
   */
  private async getProjectTasks(projectId: string) {
    const { data, error } = await supabaseAdmin
      .from('project_tasks')
      .select(`
        id,
        title,
        description,
        status,
        priority,
        due_date,
        estimated_hours:task_schedules(estimated_hours)
      `)
      .eq('project_id', projectId)
      .order('priority', { ascending: false });

    if (error) throw error;
    
    // Add default estimated hours if not set
    return data.map(task => ({
      ...task,
      estimated_hours: task.estimated_hours?.[0]?.estimated_hours || this.getDefaultEstimatedHours(task.priority)
    }));
  }

  /**
   * Get task dependencies
   */
  private async getTaskDependencies(taskIds: string[]): Promise<TaskDependency[]> {
    const { data, error } = await supabaseAdmin
      .from('task_dependencies')
      .select('*')
      .in('task_id', taskIds);

    if (error) throw error;
    return data || [];
  }

  /**
   * Build dependency graph for scheduling
   */
  private buildDependencyGraph(tasks: any[], dependencies: TaskDependency[]) {
    const graph = new Map();
    const inDegree = new Map();

    // Initialize graph
    tasks.forEach(task => {
      graph.set(task.id, []);
      inDegree.set(task.id, 0);
    });

    // Build edges and calculate in-degrees
    dependencies.forEach(dep => {
      graph.get(dep.depends_on_task_id)?.push({
        taskId: dep.task_id,
        type: dep.dependency_type,
        lag: dep.lag_days,
        isCritical: dep.is_critical
      });
      inDegree.set(dep.task_id, (inDegree.get(dep.task_id) || 0) + 1);
    });

    return { graph, inDegree };
  }

  /**
   * Calculate critical path using longest path algorithm
   */
  private calculateCriticalPath(dependencyGraph: any): string[] {
    // Simplified critical path calculation
    // In a real implementation, this would use proper CPM algorithm
    const { graph } = dependencyGraph;
    const criticalTasks: string[] = [];
    
    // For now, mark tasks with critical dependencies
    graph.forEach((edges, taskId) => {
      const hasCriticalDependency = edges.some((edge: any) => edge.isCritical);
      if (hasCriticalDependency) {
        criticalTasks.push(taskId);
      }
    });

    return criticalTasks;
  }

  /**
   * Schedule tasks with resource and time constraints
   */
  private async scheduleTasksWithConstraints(
    tasks: any[],
    dependencies: TaskDependency[],
    config: ProjectScheduleConfig,
    criticalPath: string[]
  ): Promise<TaskSchedule[]> {
    const scheduledTasks: TaskSchedule[] = [];
    const taskStartTimes = new Map<string, Date>();
    
    // Start scheduling from current date
    let currentDate = new Date();
    
    // Topological sort for dependency-aware scheduling
    const { graph, inDegree } = this.buildDependencyGraph(tasks, dependencies);
    const queue: string[] = [];
    
    // Find tasks with no dependencies
    inDegree.forEach((degree, taskId) => {
      if (degree === 0) {
        queue.push(taskId);
      }
    });

    while (queue.length > 0) {
      const taskId = queue.shift()!;
      const task = tasks.find(t => t.id === taskId);
      
      if (!task) continue;

      // Calculate start time based on dependencies
      const startTime = this.calculateTaskStartTime(
        taskId,
        dependencies,
        taskStartTimes,
        config,
        currentDate
      );

      // Calculate end time based on estimated hours and working schedule
      const endTime = this.calculateTaskEndTime(
        startTime,
        task.estimated_hours,
        config
      );

      // Create task schedule
      const taskSchedule: TaskSchedule = {
        id: `schedule_${taskId}`,
        task_id: taskId,
        estimated_hours: task.estimated_hours,
        scheduled_start: startTime,
        scheduled_end: endTime,
        is_milestone: this.isMilestoneTask(task),
        auto_scheduled: true,
        client_visible: true
      };

      scheduledTasks.push(taskSchedule);
      taskStartTimes.set(taskId, startTime);

      // Process dependent tasks
      graph.get(taskId)?.forEach((edge: any) => {
        const dependentTaskId = edge.taskId;
        inDegree.set(dependentTaskId, inDegree.get(dependentTaskId)! - 1);
        
        if (inDegree.get(dependentTaskId) === 0) {
          queue.push(dependentTaskId);
        }
      });
    }

    return scheduledTasks;
  }

  /**
   * Calculate when a task should start based on dependencies
   */
  private calculateTaskStartTime(
    taskId: string,
    dependencies: TaskDependency[],
    taskStartTimes: Map<string, Date>,
    config: ProjectScheduleConfig,
    defaultStart: Date
  ): Date {
    const taskDependencies = dependencies.filter(dep => dep.task_id === taskId);
    
    if (taskDependencies.length === 0) {
      return this.getNextWorkingDateTime(defaultStart, config);
    }

    let latestStart = defaultStart;

    taskDependencies.forEach(dep => {
      const dependencyStartTime = taskStartTimes.get(dep.depends_on_task_id);
      if (dependencyStartTime) {
        // Add lag time and buffer
        const adjustedTime = addDays(
          addHours(dependencyStartTime, config.buffer_time_hours),
          dep.lag_days
        );
        
        if (adjustedTime > latestStart) {
          latestStart = adjustedTime;
        }
      }
    });

    return this.getNextWorkingDateTime(latestStart, config);
  }

  /**
   * Calculate task end time based on working hours
   */
  private calculateTaskEndTime(
    startTime: Date,
    estimatedHours: number,
    config: ProjectScheduleConfig
  ): Date {
    const workingHoursPerDay = this.calculateWorkingHoursPerDay(config);
    const daysNeeded = Math.ceil(estimatedHours / workingHoursPerDay);
    
    let endDate = startTime;
    let remainingHours = estimatedHours;

    for (let i = 0; i < daysNeeded; i++) {
      endDate = this.getNextWorkingDay(endDate, config);
      const hoursThisDay = Math.min(remainingHours, workingHoursPerDay);
      endDate = addHours(endDate, hoursThisDay);
      remainingHours -= hoursThisDay;
      
      if (remainingHours <= 0) break;
    }

    return endDate;
  }

  /**
   * Get next working date/time based on schedule configuration
   */
  private getNextWorkingDateTime(date: Date, config: ProjectScheduleConfig): Date {
    let workingDate = new Date(date);
    
    // Skip to next working day if needed
    while (!config.working_days.includes(workingDate.getDay()) || workingDate.getDay() === 0) {
      workingDate = addDays(workingDate, 1);
    }

    // Set to working hours start time
    const [hours, minutes] = config.working_hours_start.split(':').map(Number);
    workingDate.setHours(hours, minutes, 0, 0);

    return workingDate;
  }

  /**
   * Get next working day
   */
  private getNextWorkingDay(date: Date, config: ProjectScheduleConfig): Date {
    let nextDay = addDays(date, 1);
    
    while (!config.working_days.includes(nextDay.getDay()) || nextDay.getDay() === 0) {
      nextDay = addDays(nextDay, 1);
    }

    return nextDay;
  }

  /**
   * Calculate working hours per day
   */
  private calculateWorkingHoursPerDay(config: ProjectScheduleConfig): number {
    const [startHours, startMinutes] = config.working_hours_start.split(':').map(Number);
    const [endHours, endMinutes] = config.working_hours_end.split(':').map(Number);
    
    const startTime = startHours + startMinutes / 60;
    const endTime = endHours + endMinutes / 60;
    
    return endTime - startTime;
  }

  /**
   * Check if task is a milestone
   */
  private isMilestoneTask(task: any): boolean {
    return task.priority === 'urgent' || 
           task.title.toLowerCase().includes('milestone') ||
           task.title.toLowerCase().includes('delivery');
  }

  /**
   * Get default estimated hours based on priority
   */
  private getDefaultEstimatedHours(priority: string): number {
    switch (priority) {
      case 'urgent': return 8;
      case 'high': return 16;
      case 'medium': return 24;
      case 'low': return 8;
      default: return 16;
    }
  }

  /**
   * Save task schedules to database
   */
  private async saveTaskSchedules(schedules: TaskSchedule[]): Promise<void> {
    const scheduleData = schedules.map(schedule => ({
      task_id: schedule.task_id,
      estimated_hours: schedule.estimated_hours,
      scheduled_start: schedule.scheduled_start.toISOString(),
      scheduled_end: schedule.scheduled_end.toISOString(),
      is_milestone: schedule.is_milestone,
      auto_scheduled: schedule.auto_scheduled,
      client_visible: schedule.client_visible
    }));

    const { error } = await supabaseAdmin
      .from('task_schedules')
      .upsert(scheduleData, { onConflict: 'task_id' });

    if (error) throw error;
  }

  /**
   * Trigger client communication for schedule updates
   */
  private async triggerScheduleUpdateCommunication(
    projectId: string,
    userId: string,
    schedules: TaskSchedule[]
  ): Promise<void> {
    // This will be implemented in the client communication service
    console.log(`Triggering schedule update communication for project ${projectId}`);
  }
}
