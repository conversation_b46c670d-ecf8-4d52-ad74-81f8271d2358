import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { z } from 'zod';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = express.Router();

// Validation schemas
const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  name: z.string().min(1, 'Name is required'),
});

const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

// Register endpoint
router.post('/register', asyncHandler(async (req, res) => {
  const { email, password, name } = registerSchema.parse(req.body);

  // For demo purposes, create a demo user
  const userId = 'demo-user-id';
  const token = jwt.sign(
    { userId, email, name },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn: '7d' }
  );

  logger.info('User registered successfully', { email, name });

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    data: {
      user: {
        id: userId,
        email,
        name,
        subscription_tier: 'pro',
      },
      token,
    },
  });
}));

// Login endpoint
router.post('/login', asyncHandler(async (req, res) => {
  const { email, password } = loginSchema.parse(req.body);

  // For demo purposes, accept any valid email/password
  if (!email || !password) {
    throw createError('Invalid credentials', 401);
  }

  const userId = 'demo-user-id';
  const name = 'Demo User';
  
  const token = jwt.sign(
    { userId, email, name },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn: '7d' }
  );

  logger.info('User logged in successfully', { email });

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: userId,
        email,
        name,
        subscription_tier: 'pro',
        hourly_rate: 75,
      },
      token,
    },
  });
}));

// Refresh token endpoint
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw createError('Refresh token required', 401);
  }

  // For demo purposes, return a new token
  const userId = 'demo-user-id';
  const email = '<EMAIL>';
  const name = 'Demo User';

  const token = jwt.sign(
    { userId, email, name },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn: '7d' }
  );

  res.json({
    success: true,
    data: { token },
  });
}));

// Logout endpoint
router.post('/logout', asyncHandler(async (req, res) => {
  // In a real implementation, you would invalidate the token
  logger.info('User logged out');
  
  res.json({
    success: true,
    message: 'Logged out successfully',
  });
}));

// Get current user
router.get('/me', asyncHandler(async (req, res) => {
  // This would typically require auth middleware
  res.json({
    success: true,
    data: {
      user: {
        id: 'demo-user-id',
        email: '<EMAIL>',
        name: 'Demo User',
        subscription_tier: 'pro',
        hourly_rate: 75,
      },
    },
  });
}));

export default router;
