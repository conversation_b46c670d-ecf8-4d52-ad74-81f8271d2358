'use client';

import { useState, useEffect } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import {
  UsersIcon,
  CurrencyDollarIcon,
  MicrophoneIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';

interface AnalyticsData {
  userMetrics: {
    totalSignups: number;
    activeUsers: number;
    churnRate: number;
    retentionRate: number;
  };
  revenueMetrics: {
    totalRevenue: number;
    monthlyRecurringRevenue: number;
    averageRevenuePerUser: number;
    lifetimeValue: number;
  };
  usageMetrics: {
    totalTranscriptionMinutes: number;
    liveTranscriptionSessions: number;
    averageSessionDuration: number;
    peakUsageHours: string[];
  };
  conversionMetrics: {
    trialToSubscription: number;
    starterToProfessional: number;
    professionalToEnterprise: number;
  };
  chartData: {
    userGrowth: Array<{ date: string; users: number }>;
    revenueGrowth: Array<{ date: string; revenue: number }>;
    usageGrowth: Array<{ date: string; minutes: number }>;
  };
}

export default function AdminAnalyticsPage() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');

  useEffect(() => {
    fetchAnalytics();
  }, [selectedPeriod]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/analytics?period=${selectedPeriod}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAnalyticsData(data.data);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!analyticsData) {
    return (
      <AdminLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Failed to load analytics</h2>
            <button
              onClick={fetchAnalytics}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
              <p className="text-gray-600">Detailed insights and performance metrics</p>
            </div>
            
            {/* Period selector */}
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-red-500 focus:border-red-500"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
          </div>
        </div>

        {/* User Metrics */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">User Metrics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-lg p-2">
                  <UsersIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Signups</p>
                  <p className="text-2xl font-bold text-gray-900">{formatNumber(analyticsData.userMetrics.totalSignups)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-2">
                  <UsersIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Users</p>
                  <p className="text-2xl font-bold text-gray-900">{formatNumber(analyticsData.userMetrics.activeUsers)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-red-100 rounded-lg p-2">
                  <ArrowDownIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Churn Rate</p>
                  <p className="text-2xl font-bold text-gray-900">{formatPercentage(analyticsData.userMetrics.churnRate)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-purple-100 rounded-lg p-2">
                  <ArrowUpIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Retention Rate</p>
                  <p className="text-2xl font-bold text-gray-900">{formatPercentage(analyticsData.userMetrics.retentionRate)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Revenue Metrics */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Revenue Metrics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-yellow-100 rounded-lg p-2">
                  <CurrencyDollarIcon className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(analyticsData.revenueMetrics.totalRevenue)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-2">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Monthly Recurring Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(analyticsData.revenueMetrics.monthlyRecurringRevenue)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-lg p-2">
                  <CurrencyDollarIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Average Revenue Per User</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(analyticsData.revenueMetrics.averageRevenuePerUser)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-purple-100 rounded-lg p-2">
                  <CurrencyDollarIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Customer Lifetime Value</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(analyticsData.revenueMetrics.lifetimeValue)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Usage Metrics */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Usage Metrics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-indigo-100 rounded-lg p-2">
                  <MicrophoneIcon className="h-6 w-6 text-indigo-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Transcription Minutes</p>
                  <p className="text-2xl font-bold text-gray-900">{formatNumber(analyticsData.usageMetrics.totalTranscriptionMinutes)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-red-100 rounded-lg p-2">
                  <MicrophoneIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Live Transcription Sessions</p>
                  <p className="text-2xl font-bold text-gray-900">{formatNumber(analyticsData.usageMetrics.liveTranscriptionSessions)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-2">
                  <CalendarIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Average Session Duration</p>
                  <p className="text-2xl font-bold text-gray-900">{analyticsData.usageMetrics.averageSessionDuration} min</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-yellow-100 rounded-lg p-2">
                  <ChartBarIcon className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Peak Usage Hours</p>
                  <p className="text-sm font-bold text-gray-900">
                    {analyticsData.usageMetrics.peakUsageHours.join(', ')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Conversion Metrics */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Conversion Metrics</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-center">
                <div className="bg-blue-100 rounded-lg p-3 w-12 h-12 mx-auto mb-4">
                  <ArrowUpIcon className="h-6 w-6 text-blue-600 mx-auto" />
                </div>
                <p className="text-sm font-medium text-gray-600 mb-2">Trial to Subscription</p>
                <p className="text-3xl font-bold text-gray-900">{formatPercentage(analyticsData.conversionMetrics.trialToSubscription)}</p>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-center">
                <div className="bg-purple-100 rounded-lg p-3 w-12 h-12 mx-auto mb-4">
                  <ArrowUpIcon className="h-6 w-6 text-purple-600 mx-auto" />
                </div>
                <p className="text-sm font-medium text-gray-600 mb-2">Starter to Professional</p>
                <p className="text-3xl font-bold text-gray-900">{formatPercentage(analyticsData.conversionMetrics.starterToProfessional)}</p>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-center">
                <div className="bg-green-100 rounded-lg p-3 w-12 h-12 mx-auto mb-4">
                  <ArrowUpIcon className="h-6 w-6 text-green-600 mx-auto" />
                </div>
                <p className="text-sm font-medium text-gray-600 mb-2">Professional to Enterprise</p>
                <p className="text-3xl font-bold text-gray-900">{formatPercentage(analyticsData.conversionMetrics.professionalToEnterprise)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Growth Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">User Growth</h3>
            <div className="space-y-2">
              {analyticsData.chartData.userGrowth.map((point, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {new Date(point.date).toLocaleDateString()}
                  </span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatNumber(point.users)}
                  </span>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Growth</h3>
            <div className="space-y-2">
              {analyticsData.chartData.revenueGrowth.map((point, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {new Date(point.date).toLocaleDateString()}
                  </span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatCurrency(point.revenue)}
                  </span>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Usage Growth</h3>
            <div className="space-y-2">
              {analyticsData.chartData.usageGrowth.map((point, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {new Date(point.date).toLocaleDateString()}
                  </span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatNumber(point.minutes)} min
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
