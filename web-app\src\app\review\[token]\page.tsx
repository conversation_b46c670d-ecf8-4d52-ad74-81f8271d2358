'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { 
  StarIcon,
  CheckCircleIcon,
  UserIcon,
  BriefcaseIcon,
  CalendarIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import toast from 'react-hot-toast';

interface ProjectInfo {
  id: string;
  name: string;
  freelancer: {
    name: string;
    title: string;
    email: string;
  };
  client: {
    name: string;
    email: string;
  };
  description: string;
  completedDate: string;
  deliverables: string[];
}

export default function ClientReviewPage() {
  const params = useParams();
  const token = params.token as string;
  
  const [projectInfo, setProjectInfo] = useState<ProjectInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [reviewText, setReviewText] = useState('');
  const [clientName, setClientName] = useState('');
  const [clientEmail, setClientEmail] = useState('');
  const [clientPosition, setClientPosition] = useState('');
  const [wouldRecommend, setWouldRecommend] = useState(true);
  const [allowPortfolioDisplay, setAllowPortfolioDisplay] = useState(true);
  
  // Detailed ratings
  const [communicationRating, setCommunicationRating] = useState(0);
  const [qualityRating, setQualityRating] = useState(0);
  const [timelinessRating, setTimelinessRating] = useState(0);

  useEffect(() => {
    fetchProjectInfo();
  }, [token]);

  const fetchProjectInfo = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/client-reviews/review-token/${token}`);
      
      if (response.ok) {
        const data = await response.json();
        setProjectInfo(data.data.project);
        setClientName(data.data.project.client.name);
        setClientEmail(data.data.project.client.email);
      } else {
        setError('Invalid or expired review link');
      }
    } catch (error) {
      console.error('Error fetching project info:', error);
      setError('Failed to load project information');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitReview = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (rating === 0) {
      toast.error('Please provide an overall rating');
      return;
    }
    
    if (!reviewText.trim()) {
      toast.error('Please write a review');
      return;
    }

    setSubmitting(true);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/client-reviews/project/${projectInfo?.id}/submit-review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          clientName,
          clientEmail,
          clientPosition,
          rating,
          reviewText,
          wouldRecommend,
          communicationRating: communicationRating || rating,
          qualityRating: qualityRating || rating,
          timelinessRating: timelinessRating || rating,
          allowPortfolioDisplay
        })
      });

      if (response.ok) {
        setSubmitted(true);
        toast.success('Review submitted successfully!');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit review');
      }
    } catch (error: any) {
      console.error('Error submitting review:', error);
      toast.error(error.message || 'Failed to submit review');
    } finally {
      setSubmitting(false);
    }
  };

  const renderStars = (currentRating: number, onRate: (rating: number) => void, onHover?: (rating: number) => void) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onRate(star)}
            onMouseEnter={() => onHover?.(star)}
            onMouseLeave={() => onHover?.(0)}
            className="focus:outline-none"
          >
            {star <= (onHover ? hoverRating : currentRating) ? (
              <StarIconSolid className="h-8 w-8 text-yellow-400" />
            ) : (
              <StarIcon className="h-8 w-8 text-gray-300" />
            )}
          </button>
        ))}
      </div>
    );
  };

  const renderSmallStars = (currentRating: number, onRate: (rating: number) => void) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onRate(star)}
            className="focus:outline-none"
          >
            {star <= currentRating ? (
              <StarIconSolid className="h-5 w-5 text-yellow-400" />
            ) : (
              <StarIcon className="h-5 w-5 text-gray-300" />
            )}
          </button>
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Review Link Invalid</h1>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Review Submitted!</h1>
          <p className="text-gray-600 mb-4">
            Thank you for taking the time to review your project with {projectInfo?.freelancer.name}. 
            Your feedback is valuable and helps improve their services.
          </p>
          <p className="text-sm text-gray-500">
            {projectInfo?.freelancer.name} will be notified of your review.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-white">
            <h1 className="text-2xl font-bold mb-2">Project Review</h1>
            <p className="opacity-90">Share your experience working with {projectInfo?.freelancer.name}</p>
          </div>

          {/* Project Info */}
          <div className="px-8 py-6 border-b border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                  <BriefcaseIcon className="h-5 w-5 mr-2" />
                  {projectInfo?.name}
                </h3>
                <p className="text-gray-600 mb-4">{projectInfo?.description}</p>
                <div className="flex items-center text-sm text-gray-500">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Completed: {new Date(projectInfo?.completedDate || '').toLocaleDateString()}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                  <UserIcon className="h-4 w-4 mr-2" />
                  Freelancer
                </h4>
                <p className="font-semibold text-gray-900">{projectInfo?.freelancer.name}</p>
                <p className="text-gray-600">{projectInfo?.freelancer.title}</p>
              </div>
            </div>
          </div>

          {/* Review Form */}
          <form onSubmit={handleSubmitReview} className="px-8 py-6">
            <div className="space-y-6">
              {/* Client Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Your Name *</label>
                  <input
                    type="text"
                    value={clientName}
                    onChange={(e) => setClientName(e.target.value)}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Your Position</label>
                  <input
                    type="text"
                    value={clientPosition}
                    onChange={(e) => setClientPosition(e.target.value)}
                    placeholder="e.g., CEO, Marketing Director"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Overall Rating */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Overall Rating *</label>
                <div className="flex items-center space-x-4">
                  {renderStars(rating, setRating, setHoverRating)}
                  <span className="text-sm text-gray-600">
                    {rating > 0 && `${rating} out of 5 stars`}
                  </span>
                </div>
              </div>

              {/* Detailed Ratings */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Communication</label>
                  {renderSmallStars(communicationRating, setCommunicationRating)}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Quality of Work</label>
                  {renderSmallStars(qualityRating, setQualityRating)}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Timeliness</label>
                  {renderSmallStars(timelinessRating, setTimelinessRating)}
                </div>
              </div>

              {/* Review Text */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Your Review *</label>
                <textarea
                  value={reviewText}
                  onChange={(e) => setReviewText(e.target.value)}
                  required
                  rows={4}
                  placeholder="Please share your experience working with this freelancer..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Recommendation */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Would you recommend this freelancer?</label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      checked={wouldRecommend === true}
                      onChange={() => setWouldRecommend(true)}
                      className="mr-2"
                    />
                    Yes
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      checked={wouldRecommend === false}
                      onChange={() => setWouldRecommend(false)}
                      className="mr-2"
                    />
                    No
                  </label>
                </div>
              </div>

              {/* Portfolio Display Permission */}
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={allowPortfolioDisplay}
                    onChange={(e) => setAllowPortfolioDisplay(e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">
                    Allow this review to be displayed on the freelancer's portfolio
                  </span>
                </label>
              </div>

              {/* Submit Button */}
              <div className="pt-4">
                <button
                  type="submit"
                  disabled={submitting || rating === 0 || !reviewText.trim()}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-md font-semibold hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {submitting ? 'Submitting Review...' : 'Submit Review'}
                </button>
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            This review is powered by <span className="font-semibold text-blue-600">KaiNote</span>
          </p>
        </div>
      </div>
    </div>
  );
}
