import express, { Request, Response, NextFunction } from 'express';

const router = express.Router();

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    userId: string;
    email: string;
    name: string;
    title?: string;
    bio?: string;
  };
}

// Demo auth middleware
const demoAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  req.user = {
    id: 'demo-user-id',
    userId: 'demo-user-id',
    email: '<EMAIL>',
    name: '<PERSON>',
    title: 'Full-Stack Developer & UI/UX Designer',
    bio: 'Passionate freelancer with 5+ years of experience creating beautiful, functional web applications and user experiences.'
  };
  next();
};

// Get freelancer's portfolio settings
router.get('/settings', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;

    // Demo portfolio settings
    const portfolioSettings = {
      id: 'portfolio_demo_123',
      freelancerId: user.id,
      isPublic: true,
      portfolioUrl: `portfolio-${user.id}`,
      shareableLink: `http://localhost:3001/portfolio/portfolio-${user.id}`,
      displayName: user.name,
      title: user.title,
      bio: user.bio,
      selectedProjects: ['demo-project-1', 'demo-project-2'], // Project IDs to show in portfolio
      customization: {
        template: 'modern',
        primaryColor: '#3B82F6',
        secondaryColor: '#8B5CF6',
        showContactInfo: true,
        showProjectDetails: true,
        showClientTestimonials: true,
        showSkills: true,
        showStats: true,
        layout: 'grid' // 'grid' or 'list'
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      data: portfolioSettings
    });

  } catch (error: any) {
    console.error('Error getting portfolio settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get portfolio settings',
      details: error.message
    });
  }
});

// Update portfolio settings
router.put('/settings', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;
    const {
      isPublic,
      displayName,
      title,
      bio,
      selectedProjects,
      customization
    } = req.body;

    // Demo updated settings
    const updatedSettings = {
      id: 'portfolio_demo_123',
      freelancerId: user.id,
      isPublic: isPublic !== undefined ? isPublic : true,
      portfolioUrl: `portfolio-${user.id}`,
      shareableLink: `http://localhost:3001/portfolio/portfolio-${user.id}`,
      displayName: displayName || user.name,
      title: title || user.title,
      bio: bio || user.bio,
      selectedProjects: selectedProjects || ['demo-project-1', 'demo-project-2'],
      customization: customization || {
        template: 'modern',
        primaryColor: '#3B82F6',
        secondaryColor: '#8B5CF6',
        showContactInfo: true,
        showProjectDetails: true,
        showClientTestimonials: true,
        showSkills: true,
        showStats: true,
        layout: 'grid'
      },
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      data: updatedSettings
    });

  } catch (error: any) {
    console.error('Error updating portfolio settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update portfolio settings',
      details: error.message
    });
  }
});

// Get public portfolio data (no auth required)
router.get('/public/:portfolioUrl', async (req: Request, res: Response) => {
  try {
    const { portfolioUrl } = req.params;

    // Demo portfolio data
    const portfolioData = {
      freelancer: {
        id: 'demo-user-id',
        displayName: 'Alex Johnson',
        title: 'Full-Stack Developer & UI/UX Designer',
        bio: 'Passionate freelancer with 5+ years of experience creating beautiful, functional web applications and user experiences. I specialize in React, Node.js, and modern design principles.',
        email: '<EMAIL>',
        location: 'San Francisco, CA',
        website: 'https://alexjohnson.dev',
        skills: ['React', 'Node.js', 'TypeScript', 'UI/UX Design', 'PostgreSQL', 'AWS']
      },
      projects: [
        {
          id: 'demo-project-1',
          name: 'E-commerce Platform Redesign',
          client_name: 'TechCorp Solutions',
          description: 'Complete redesign and development of a modern e-commerce platform with improved user experience and performance.',
          technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe'],
          status: 'completed',
          start_date: '2024-01-15',
          end_date: '2024-03-20',
          budget: 15000,
          images: [
            'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800',
            'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800'
          ],
          highlights: [
            'Increased conversion rate by 35%',
            'Improved page load speed by 60%',
            'Mobile-first responsive design',
            'Integrated payment processing'
          ],
          testimonial: {
            text: "Alex delivered an outstanding e-commerce platform that exceeded our expectations. The new design increased our conversion rate significantly.",
            author: "Sarah Chen",
            position: "CTO, TechCorp Solutions"
          }
        },
        {
          id: 'demo-project-2',
          name: 'SaaS Dashboard Development',
          client_name: 'DataFlow Analytics',
          description: 'Built a comprehensive analytics dashboard for a SaaS platform with real-time data visualization and reporting.',
          technologies: ['React', 'TypeScript', 'D3.js', 'Express.js'],
          status: 'completed',
          start_date: '2024-04-01',
          end_date: '2024-05-30',
          budget: 12000,
          images: [
            'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800',
            'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800'
          ],
          highlights: [
            'Real-time data visualization',
            'Custom chart components',
            'Responsive design',
            'Advanced filtering and search'
          ],
          testimonial: {
            text: "The dashboard Alex created transformed how we present data to our clients. It's intuitive, fast, and visually stunning.",
            author: "Michael Rodriguez",
            position: "Product Manager, DataFlow Analytics"
          }
        }
      ],
      customization: {
        theme: 'modern',
        primaryColor: '#3B82F6',
        showContactInfo: true,
        showProjectDetails: true,
        showClientTestimonials: true
      },
      stats: {
        totalProjects: 12,
        completedProjects: 10,
        happyClients: 8,
        yearsExperience: 5
      }
    };

    res.json({
      success: true,
      data: portfolioData
    });

  } catch (error: any) {
    console.error('Error getting public portfolio:', error);
    res.status(500).json({
      success: false,
      error: 'Portfolio not found',
      details: error.message
    });
  }
});

// Get freelancer's available projects for portfolio selection
router.get('/available-projects', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;

    // Demo available projects
    const availableProjects = [
      {
        id: 'demo-project-1',
        name: 'E-commerce Platform Redesign',
        client_name: 'TechCorp Solutions',
        status: 'completed',
        end_date: '2024-03-20',
        budget: 15000,
        description: 'Complete redesign and development of a modern e-commerce platform',
        isSelected: true
      },
      {
        id: 'demo-project-2',
        name: 'SaaS Dashboard Development',
        client_name: 'DataFlow Analytics',
        status: 'completed',
        end_date: '2024-05-30',
        budget: 12000,
        description: 'Built a comprehensive analytics dashboard for a SaaS platform',
        isSelected: true
      },
      {
        id: 'demo-project-3',
        name: 'Mobile App UI/UX Design',
        client_name: 'StartupXYZ',
        status: 'completed',
        end_date: '2024-02-15',
        budget: 8000,
        description: 'Designed user interface and experience for a mobile application',
        isSelected: false
      },
      {
        id: 'demo-project-4',
        name: 'Website Performance Optimization',
        client_name: 'Local Business Inc',
        status: 'in_progress',
        end_date: null,
        budget: 5000,
        description: 'Optimizing website performance and SEO',
        isSelected: false
      }
    ];

    res.json({
      success: true,
      data: availableProjects
    });

  } catch (error: any) {
    console.error('Error getting available projects:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get available projects',
      details: error.message
    });
  }
});

// Get available portfolio templates
router.get('/templates', (req: Request, res: Response) => {
  try {
    const templates = [
      {
        id: 'modern',
        name: 'Modern',
        description: 'Clean, minimalist design with gradient accents',
        preview: '/templates/modern-preview.jpg',
        features: ['Gradient headers', 'Card-based layout', 'Modern typography', 'Mobile responsive'],
        primaryColor: '#3B82F6',
        secondaryColor: '#8B5CF6'
      },
      {
        id: 'professional',
        name: 'Professional',
        description: 'Corporate-style layout perfect for business clients',
        preview: '/templates/professional-preview.jpg',
        features: ['Corporate styling', 'Formal typography', 'Business colors', 'Executive layout'],
        primaryColor: '#1F2937',
        secondaryColor: '#374151'
      },
      {
        id: 'creative',
        name: 'Creative',
        description: 'Bold, artistic design for creative professionals',
        preview: '/templates/creative-preview.jpg',
        features: ['Artistic layouts', 'Bold colors', 'Creative typography', 'Visual emphasis'],
        primaryColor: '#EC4899',
        secondaryColor: '#F59E0B'
      },
      {
        id: 'minimal',
        name: 'Minimal',
        description: 'Ultra-clean design focusing on content',
        preview: '/templates/minimal-preview.jpg',
        features: ['Minimal styling', 'Content focus', 'Clean typography', 'Subtle accents'],
        primaryColor: '#6B7280',
        secondaryColor: '#9CA3AF'
      },
      {
        id: 'tech',
        name: 'Tech',
        description: 'Modern tech-focused design for developers',
        preview: '/templates/tech-preview.jpg',
        features: ['Tech aesthetics', 'Code-inspired', 'Dark mode ready', 'Developer focused'],
        primaryColor: '#10B981',
        secondaryColor: '#3B82F6'
      }
    ];

    res.json({
      success: true,
      data: templates
    });

  } catch (error: any) {
    console.error('Error getting templates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get templates',
      details: error.message
    });
  }
});

export default router;
