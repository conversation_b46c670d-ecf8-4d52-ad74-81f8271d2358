{"name": "kainote-backend", "version": "1.0.0", "description": "KaiNote Backend API - Freelancer Business Management Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "openai": "^4.20.1", "pg": "^8.11.3", "stripe": "^14.7.0", "ws": "^8.14.2", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/pg": "^8.10.9", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}