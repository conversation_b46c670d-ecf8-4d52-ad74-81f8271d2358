import express from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = express.Router();

// Subscription plans configuration
const SUBSCRIPTION_PLANS = {
  starter: {
    id: 'starter',
    name: 'Starter',
    price: 29,
    transcriptionMinutes: 1500,
    features: {
      liveTranscription: false,
      basicRecording: true,
      projectManagement: true,
      timeTracking: true,
      basicInvoicing: true,
      clientPortals: true,
      emailSupport: true,
    },
  },
  professional: {
    id: 'professional',
    name: 'Professional',
    price: 49,
    transcriptionMinutes: 3000,
    features: {
      liveTranscription: true,
      advancedRecording: true,
      advancedProjectManagement: true,
      smartTimeTracking: true,
      advancedInvoicing: true,
      portfolioGeneration: true,
      workflowAutomation: true,
      smartScheduling: true,
      aiDocumentGeneration: true,
      prioritySupport: true,
    },
  },
  enterprise: {
    id: 'enterprise',
    name: 'Enterprise',
    price: 99,
    transcriptionMinutes: -1, // Unlimited
    features: {
      liveTranscription: true,
      advancedRecording: true,
      teamProjectManagement: true,
      smartTimeTracking: true,
      advancedInvoicing: true,
      whitelabelPortals: true,
      advancedAutomation: true,
      aiOptimization: true,
      teamCollaboration: true,
      customIntegrations: true,
      dedicatedSupport: true,
    },
  },
};

// Get current subscription
router.get('/current', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;

  // For demo purposes, return a mock subscription
  // In production, this would query the database
  const subscription = {
    id: 'sub_demo_123',
    userId: user.userId,
    plan: 'professional', // Demo user has professional plan
    status: 'active',
    currentPeriodStart: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    currentPeriodEnd: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
    transcriptionMinutesUsed: 1250,
    transcriptionMinutesLimit: 3000,
    features: SUBSCRIPTION_PLANS.professional.features,
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString(),
  };

  res.json({
    success: true,
    data: subscription,
  });
}));

// Get available plans
router.get('/plans', asyncHandler(async (req: AuthenticatedRequest, res) => {
  res.json({
    success: true,
    data: Object.values(SUBSCRIPTION_PLANS),
  });
}));

// Check feature access
router.get('/features/:feature', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { feature } = req.params;
  const user = req.user!;

  // For demo purposes, professional plan features
  const userPlan = 'professional';
  const planFeatures = SUBSCRIPTION_PLANS[userPlan as keyof typeof SUBSCRIPTION_PLANS]?.features || {};
  
  const hasAccess = planFeatures[feature as keyof typeof planFeatures] || false;

  res.json({
    success: true,
    data: {
      feature,
      hasAccess,
      plan: userPlan,
      upgradeRequired: !hasAccess,
    },
  });
}));

// Check transcription minutes usage
router.get('/usage/transcription', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;

  // For demo purposes, return mock usage
  const usage = {
    used: 1250,
    limit: 3000,
    remaining: 1750,
    percentageUsed: 41.7,
    resetDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
  };

  res.json({
    success: true,
    data: usage,
  });
}));

// Create checkout session (Stripe integration)
router.post('/checkout', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { planId, billingPeriod = 'monthly' } = req.body;
  const user = req.user!;

  if (!SUBSCRIPTION_PLANS[planId as keyof typeof SUBSCRIPTION_PLANS]) {
    throw createError('Invalid plan ID', 400);
  }

  const plan = SUBSCRIPTION_PLANS[planId as keyof typeof SUBSCRIPTION_PLANS];

  // For demo purposes, return a mock checkout session
  // In production, this would create a Stripe checkout session
  const checkoutSession = {
    id: 'cs_demo_123',
    url: `https://checkout.stripe.com/demo?plan=${planId}&period=${billingPeriod}`,
    planId,
    planName: plan.name,
    amount: billingPeriod === 'yearly' ? plan.price * 12 * 0.8 : plan.price,
    billingPeriod,
    expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30 minutes
  };

  logger.info('Checkout session created', {
    userId: user.userId,
    planId,
    billingPeriod,
    sessionId: checkoutSession.id,
  });

  res.json({
    success: true,
    data: checkoutSession,
  });
}));

// Update subscription
router.put('/update', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { planId } = req.body;
  const user = req.user!;

  if (!SUBSCRIPTION_PLANS[planId as keyof typeof SUBSCRIPTION_PLANS]) {
    throw createError('Invalid plan ID', 400);
  }

  // For demo purposes, return success
  // In production, this would update the subscription in Stripe and database
  logger.info('Subscription updated', {
    userId: user.userId,
    newPlan: planId,
  });

  res.json({
    success: true,
    message: 'Subscription updated successfully',
    data: {
      planId,
      effectiveDate: new Date().toISOString(),
    },
  });
}));

// Cancel subscription
router.post('/cancel', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;

  // For demo purposes, return success
  // In production, this would cancel the subscription in Stripe
  logger.info('Subscription cancelled', {
    userId: user.userId,
  });

  res.json({
    success: true,
    message: 'Subscription cancelled successfully',
    data: {
      cancelledAt: new Date().toISOString(),
      accessUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    },
  });
}));

// Webhook handler for Stripe events
router.post('/webhook', express.raw({ type: 'application/json' }), asyncHandler(async (req, res) => {
  const sig = req.headers['stripe-signature'];
  
  // For demo purposes, just log the webhook
  logger.info('Subscription webhook received', {
    signature: sig,
    bodyLength: req.body.length,
  });

  // In production, verify the webhook signature and process the event
  res.json({ received: true });
}));

// Get billing history
router.get('/billing-history', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;

  // For demo purposes, return mock billing history
  const billingHistory = [
    {
      id: 'inv_demo_1',
      date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      amount: 49,
      status: 'paid',
      plan: 'Professional',
      period: 'Monthly',
      downloadUrl: '/api/subscription/invoice/inv_demo_1',
    },
    {
      id: 'inv_demo_2',
      date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
      amount: 49,
      status: 'paid',
      plan: 'Professional',
      period: 'Monthly',
      downloadUrl: '/api/subscription/invoice/inv_demo_2',
    },
  ];

  res.json({
    success: true,
    data: billingHistory,
  });
}));

// Download invoice
router.get('/invoice/:invoiceId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { invoiceId } = req.params;
  const user = req.user!;

  // For demo purposes, return a redirect to a mock PDF
  logger.info('Invoice download requested', {
    userId: user.userId,
    invoiceId,
  });

  res.json({
    success: true,
    data: {
      downloadUrl: `https://example.com/invoices/${invoiceId}.pdf`,
      expiresAt: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour
    },
  });
}));

export default router;
