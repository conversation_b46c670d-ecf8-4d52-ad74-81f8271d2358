import express from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHand<PERSON>, createError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = express.Router();

// Admin authentication middleware
const adminAuth = (req: AuthenticatedRequest, res: express.Response, next: express.NextFunction) => {
  const user = req.user;
  
  // For demo purposes, check if user email contains 'admin'
  // In production, this would check a proper admin role
  if (!user || !user.email.includes('admin')) {
    return res.status(403).json({
      success: false,
      message: 'Admin access required',
    });
  }
  
  next();
};

// Dashboard overview
router.get('/dashboard', adminAuth, asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Mock admin dashboard data
  const dashboardData = {
    overview: {
      totalUsers: 12847,
      activeUsers: 8932,
      totalRevenue: 487650,
      monthlyRevenue: 52340,
      transcriptionMinutes: 2847392,
      liveTranscriptionSessions: 15847,
    },
    growth: {
      userGrowth: 12.5, // percentage
      revenueGrowth: 18.3,
      usageGrowth: 24.7,
    },
    subscriptions: {
      starter: 5234,
      professional: 6891,
      enterprise: 722,
    },
    recentActivity: [
      {
        id: '1',
        type: 'user_signup',
        user: '<EMAIL>',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        details: 'New user registration - Professional plan trial',
      },
      {
        id: '2',
        type: 'subscription_upgrade',
        user: '<EMAIL>',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        details: 'Upgraded from Starter to Professional',
      },
      {
        id: '3',
        type: 'live_transcription',
        user: '<EMAIL>',
        timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
        details: 'Started live transcription session - 45 minutes',
      },
      {
        id: '4',
        type: 'payment_received',
        user: '<EMAIL>',
        timestamp: new Date(Date.now() - 35 * 60 * 1000).toISOString(),
        details: 'Payment received - Enterprise plan ($99)',
      },
    ],
    systemHealth: {
      apiResponseTime: 145, // ms
      uptime: 99.97, // percentage
      errorRate: 0.03, // percentage
      activeConnections: 1247,
      transcriptionQueue: 23,
    },
  };

  logger.info('Admin dashboard accessed', {
    adminUser: req.user?.email,
    timestamp: new Date().toISOString(),
  });

  res.json({
    success: true,
    data: dashboardData,
  });
}));

// User management
router.get('/users', adminAuth, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { page = 1, limit = 50, search, plan, status } = req.query;
  
  // Mock user data
  const users = [
    {
      id: 'user-1',
      email: '<EMAIL>',
      name: 'John Doe',
      plan: 'professional',
      status: 'active',
      transcriptionMinutesUsed: 1847,
      transcriptionMinutesLimit: 3000,
      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      totalRevenue: 147,
      liveTranscriptionSessions: 23,
    },
    {
      id: 'user-2',
      email: '<EMAIL>',
      name: 'Sarah Wilson',
      plan: 'enterprise',
      status: 'active',
      transcriptionMinutesUsed: 8934,
      transcriptionMinutesLimit: -1, // unlimited
      lastActive: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
      totalRevenue: 891,
      liveTranscriptionSessions: 156,
    },
    {
      id: 'user-3',
      email: '<EMAIL>',
      name: 'Mike Johnson',
      plan: 'starter',
      status: 'active',
      transcriptionMinutesUsed: 1234,
      transcriptionMinutesLimit: 1500,
      lastActive: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      totalRevenue: 58,
      liveTranscriptionSessions: 0,
    },
    {
      id: 'user-4',
      email: '<EMAIL>',
      name: 'Lisa Chen',
      plan: 'professional',
      status: 'trial',
      transcriptionMinutesUsed: 456,
      transcriptionMinutesLimit: 3000,
      lastActive: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      totalRevenue: 0,
      liveTranscriptionSessions: 8,
    },
    {
      id: 'user-5',
      email: '<EMAIL>',
      name: 'Alex Rodriguez',
      plan: 'professional',
      status: 'cancelled',
      transcriptionMinutesUsed: 2847,
      transcriptionMinutesLimit: 3000,
      lastActive: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString(),
      totalRevenue: 294,
      liveTranscriptionSessions: 47,
    },
  ];

  // Apply filters (mock implementation)
  let filteredUsers = users;
  if (search) {
    filteredUsers = users.filter(user => 
      user.email.toLowerCase().includes((search as string).toLowerCase()) ||
      user.name.toLowerCase().includes((search as string).toLowerCase())
    );
  }
  if (plan) {
    filteredUsers = filteredUsers.filter(user => user.plan === plan);
  }
  if (status) {
    filteredUsers = filteredUsers.filter(user => user.status === status);
  }

  const total = filteredUsers.length;
  const startIndex = (Number(page) - 1) * Number(limit);
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + Number(limit));

  res.json({
    success: true,
    data: {
      users: paginatedUsers,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        totalPages: Math.ceil(total / Number(limit)),
      },
    },
  });
}));

// User details
router.get('/users/:userId', adminAuth, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { userId } = req.params;

  // Mock detailed user data
  const userDetails = {
    id: userId,
    email: '<EMAIL>',
    name: 'John Doe',
    plan: 'professional',
    status: 'active',
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    subscription: {
      id: 'sub-123',
      plan: 'professional',
      status: 'active',
      currentPeriodStart: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      currentPeriodEnd: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
      totalRevenue: 147,
    },
    usage: {
      transcriptionMinutesUsed: 1847,
      transcriptionMinutesLimit: 3000,
      liveTranscriptionSessions: 23,
      totalMeetings: 89,
      totalProjects: 12,
      totalClients: 8,
    },
    recentActivity: [
      {
        type: 'live_transcription',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        details: 'Live transcription session - Client meeting (45 minutes)',
      },
      {
        type: 'meeting_recorded',
        timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        details: 'Meeting recorded and transcribed (32 minutes)',
      },
      {
        type: 'project_created',
        timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        details: 'New project created: Website Redesign',
      },
    ],
  };

  res.json({
    success: true,
    data: userDetails,
  });
}));

// Analytics
router.get('/analytics', adminAuth, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { period = '30d' } = req.query;

  // Mock analytics data
  const analytics = {
    userMetrics: {
      totalSignups: 1247,
      activeUsers: 8932,
      churnRate: 3.2,
      retentionRate: 96.8,
    },
    revenueMetrics: {
      totalRevenue: 487650,
      monthlyRecurringRevenue: 52340,
      averageRevenuePerUser: 54.60,
      lifetimeValue: 847.30,
    },
    usageMetrics: {
      totalTranscriptionMinutes: 2847392,
      liveTranscriptionSessions: 15847,
      averageSessionDuration: 34.5,
      peakUsageHours: ['9-10 AM', '2-3 PM', '7-8 PM'],
    },
    conversionMetrics: {
      trialToSubscription: 23.4,
      starterToProfessional: 18.7,
      professionalToEnterprise: 4.2,
    },
    chartData: {
      userGrowth: [
        { date: '2024-11-01', users: 8234 },
        { date: '2024-11-15', users: 8567 },
        { date: '2024-12-01', users: 8932 },
        { date: '2024-12-15', users: 9247 },
      ],
      revenueGrowth: [
        { date: '2024-11-01', revenue: 45230 },
        { date: '2024-11-15', revenue: 48560 },
        { date: '2024-12-01', revenue: 52340 },
        { date: '2024-12-15', revenue: 55670 },
      ],
      usageGrowth: [
        { date: '2024-11-01', minutes: 2456789 },
        { date: '2024-11-15', minutes: 2634567 },
        { date: '2024-12-01', minutes: 2847392 },
        { date: '2024-12-15', minutes: 3023456 },
      ],
    },
  };

  res.json({
    success: true,
    data: analytics,
  });
}));

// System monitoring
router.get('/system', adminAuth, asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Mock system metrics
  const systemMetrics = {
    server: {
      uptime: 99.97,
      cpuUsage: 23.4,
      memoryUsage: 67.8,
      diskUsage: 45.2,
    },
    api: {
      responseTime: 145,
      requestsPerMinute: 1247,
      errorRate: 0.03,
      activeConnections: 1247,
    },
    transcription: {
      queueLength: 23,
      processingTime: 2.3,
      successRate: 99.1,
      liveSessionsActive: 47,
    },
    database: {
      connectionPool: 85,
      queryTime: 12.5,
      activeQueries: 34,
      cacheHitRate: 94.2,
    },
  };

  res.json({
    success: true,
    data: systemMetrics,
  });
}));

// User actions
router.post('/users/:userId/suspend', adminAuth, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { userId } = req.params;
  const { reason } = req.body;

  logger.info('User suspended by admin', {
    adminUser: req.user?.email,
    targetUser: userId,
    reason,
  });

  res.json({
    success: true,
    message: 'User suspended successfully',
  });
}));

router.post('/users/:userId/unsuspend', adminAuth, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { userId } = req.params;

  logger.info('User unsuspended by admin', {
    adminUser: req.user?.email,
    targetUser: userId,
  });

  res.json({
    success: true,
    message: 'User unsuspended successfully',
  });
}));

router.post('/users/:userId/refund', adminAuth, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { userId } = req.params;
  const { amount, reason } = req.body;

  logger.info('Refund issued by admin', {
    adminUser: req.user?.email,
    targetUser: userId,
    amount,
    reason,
  });

  res.json({
    success: true,
    message: 'Refund processed successfully',
  });
}));

export default router;
