'use client';

import { useState, useEffect } from 'react';
import { CheckCircleIcon, SparklesIcon } from '@heroicons/react/24/outline';

interface SubscriptionPlan {
  name: string;
  priceId: string;
  price: number;
  currency: string;
  interval: string;
  features: string[];
}

export default function PricingPage() {
  const [plans, setPlans] = useState<Record<string, SubscriptionPlan>>({});
  const [loading, setLoading] = useState(true);
  const [checkoutLoading, setCheckoutLoading] = useState<string | null>(null);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/billing/plans`);
      if (response.ok) {
        const data = await response.json();
        setPlans(data.data);
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (planId: string) => {
    try {
      setCheckoutLoading(planId);
      const token = localStorage.getItem('token');

      if (!token) {
        // Redirect to sign in if not authenticated
        window.location.href = '/auth/signin?redirect=' + encodeURIComponent('/pricing');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/billing/create-checkout-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          planId: planId.toLowerCase(),
          successUrl: `${window.location.origin}/billing/success`,
          cancelUrl: `${window.location.origin}/pricing`
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.data.url) {
          window.location.href = data.data.url;
        }
      } else {
        const errorData = await response.json();
        alert('Error: ' + errorData.error);
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setCheckoutLoading(null);
    }
  };

  const formatPrice = (amount: number, currency: string) => {
    if (amount === 0) return 'Free';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const planOrder = ['starter', 'professional', 'business'];
  const orderedPlans = planOrder.map(key => ({ key, ...plans[key] })).filter(plan => plan.name);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
            Choose Your Plan
          </h1>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Start free and scale as you grow. All plans include our core AI features to make freelancing 90% more efficient.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {orderedPlans.map((plan) => {
            const isPopular = plan.key === 'professional';
            const isFree = plan.price === 0;
            
            return (
              <div
                key={plan.key}
                className={`relative bg-white rounded-xl shadow-lg border-2 ${
                  isPopular 
                    ? 'border-primary-500 ring-2 ring-primary-500 transform scale-105' 
                    : 'border-gray-200'
                } overflow-hidden`}
              >
                {isPopular && (
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <span className="inline-flex items-center px-4 py-1 rounded-full text-sm font-semibold tracking-wide uppercase bg-gradient-to-r from-primary-500 to-purple-600 text-white">
                      <SparklesIcon className="w-4 h-4 mr-1" />
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="p-8">
                  <div className="text-center">
                    <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
                    <div className="mt-4">
                      <span className="text-5xl font-extrabold text-gray-900">
                        {formatPrice(plan.price, plan.currency)}
                      </span>
                      {!isFree && (
                        <span className="text-xl font-medium text-gray-500">/{plan.interval}</span>
                      )}
                    </div>
                  </div>

                  <ul className="mt-8 space-y-4">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircleIcon className="flex-shrink-0 w-5 h-5 text-green-500 mt-0.5" />
                        <span className="ml-3 text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <div className="mt-8">
                    <button
                      onClick={() => handleSubscribe(plan.key)}
                      disabled={checkoutLoading === plan.key}
                      className={`w-full py-3 px-6 rounded-lg font-semibold text-center transition-colors ${
                        isPopular
                          ? 'bg-gradient-to-r from-primary-500 to-purple-600 text-white hover:from-primary-600 hover:to-purple-700'
                          : isFree
                          ? 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                          : 'bg-primary-50 text-primary-700 hover:bg-primary-100'
                      } disabled:opacity-50 disabled:cursor-not-allowed`}
                    >
                      {checkoutLoading === plan.key ? (
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current mr-2"></div>
                          Processing...
                        </div>
                      ) : isFree ? (
                        'Get Started Free'
                      ) : (
                        'Start Free Trial'
                      )}
                    </button>
                  </div>

                  {!isFree && (
                    <p className="mt-4 text-sm text-gray-500 text-center">
                      14-day free trial • No credit card required • Cancel anytime
                    </p>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* FAQ Section */}
        <div className="mt-16 max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">
            Frequently Asked Questions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Can I change plans anytime?
              </h3>
              <p className="text-gray-600">
                Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate the billing.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                What payment methods do you accept?
              </h3>
              <p className="text-gray-600">
                We accept all major credit cards (Visa, MasterCard, American Express) and PayPal through our secure Stripe integration.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Is there a free trial?
              </h3>
              <p className="text-gray-600">
                Yes! All paid plans come with a 14-day free trial. No credit card required to start, and you can cancel anytime.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                What happens if I cancel?
              </h3>
              <p className="text-gray-600">
                You'll retain access to premium features until the end of your billing period. After that, you'll be moved to the free plan.
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-primary-600 to-purple-600 rounded-xl p-8 text-white">
            <h2 className="text-3xl font-bold mb-4">
              Ready to make freelancing 90% more efficient?
            </h2>
            <p className="text-xl mb-6 opacity-90">
              Join thousands of freelancers who are already saving 15+ hours per week with KaiNote.
            </p>
            <button
              onClick={() => handleSubscribe('professional')}
              disabled={checkoutLoading === 'professional'}
              className="inline-flex items-center px-8 py-3 border border-transparent text-lg font-medium rounded-lg text-primary-600 bg-white hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              {checkoutLoading === 'professional' ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600 mr-2"></div>
                  Processing...
                </>
              ) : (
                'Start Your Free Trial'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
