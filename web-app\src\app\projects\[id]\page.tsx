'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon,
  ChatBubbleLeftRightIcon,
  DocumentDuplicateIcon,
  ShareIcon,
  PlusIcon,
  CheckIcon,
  XMarkIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  Cog6ToothIcon,
  ArrowDownTrayIcon,
  SparklesIcon,
  ReceiptPercentIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { apiHelpers } from '@/lib/api';
import toast from 'react-hot-toast';

interface Project {
  id: string;
  name: string;
  client_name: string;
  client_email?: string;
  description?: string;
  status: string;
  budget?: number;
  deadline?: string;
  created_at: string;
  updated_at: string;
}

interface Meeting {
  id: string;
  title: string;
  platform: string;
  duration_minutes: number;
  recorded_at: string;
  transcription_status: string;
}

interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'review' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  due_date?: string;
  assigned_to?: string;
  created_at: string;
  documents?: Document[];
}

interface Document {
  id: string;
  name: string;
  file_type: string;
  file_size: number;
  uploaded_at: string;
  uploaded_by: string;
}

interface Invoice {
  id: string;
  invoice_number: string;
  amount: number;
  currency: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  due_date: string;
  sent_at?: string;
  paid_at?: string;
  notes?: string;
  created_at: string;
  enableOnlinePayment?: boolean;
  paymentUrl?: string;
}

interface Receipt {
  id: string;
  receipt_number: string;
  invoice_id: string;
  invoice_number: string;
  amount: number;
  currency: string;
  payment_method: string;
  payment_date: string;
  transaction_id?: string;
  notes?: string;
  created_at: string;
}

export default function ProjectDetailPage() {
  const params = useParams();
  const projectId = params.id as string;
  
  const [project, setProject] = useState<Project | null>(null);
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [stripeStatus, setStripeStatus] = useState<any>(null);
  const [showNewTaskForm, setShowNewTaskForm] = useState(false);
  const [showNewInvoiceForm, setShowNewInvoiceForm] = useState(false);
  const [showNewReceiptForm, setShowNewReceiptForm] = useState(false);
  const [showEmailOptionsModal, setShowEmailOptionsModal] = useState(false);
  const [selectedReceiptForEmail, setSelectedReceiptForEmail] = useState<Receipt | null>(null);
  const [showCommunicationSettings, setShowCommunicationSettings] = useState(false);
  const [communicationEmail, setCommunicationEmail] = useState('');
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [showNewMeetingForm, setShowNewMeetingForm] = useState(false);
  const [meetingType, setMeetingType] = useState<'schedule' | 'previous'>('schedule');
  const [showDocumentUpload, setShowDocumentUpload] = useState(false);
  const [invoiceSearchTerm, setInvoiceSearchTerm] = useState('');
  const [invoiceStatusFilter, setInvoiceStatusFilter] = useState('all');
  const [taskDocuments, setTaskDocuments] = useState<Document[]>([]);
  const [editTaskDocuments, setEditTaskDocuments] = useState<Document[]>([]);

  // AI Task Generation State
  const [aiTaskText, setAiTaskText] = useState('');
  const [aiTaskFiles, setAiTaskFiles] = useState<File[]>([]);
  const [generatedTasks, setGeneratedTasks] = useState<Task[]>([]);
  const [isGeneratingTasks, setIsGeneratingTasks] = useState(false);
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);

  // AI Documents State
  const [selectedDocumentType, setSelectedDocumentType] = useState('');
  const [documentPrompt, setDocumentPrompt] = useState('');
  const [selectedDataSources, setSelectedDataSources] = useState<string[]>([]);
  const [generatedDocument, setGeneratedDocument] = useState('');
  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);
  const [documentTitle, setDocumentTitle] = useState('');

  // AI Chat State
  const [chatMessages, setChatMessages] = useState<Array<{id: string, role: 'user' | 'assistant', content: string, timestamp: Date}>>([]);
  const [chatInput, setChatInput] = useState('');
  const [isChatting, setIsChatting] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    if (projectId) {
      fetchProjectData();
      checkForNewTasksFromMeetings();
      fetchStripeStatus();
    }
  }, [projectId]);

  const fetchProjectData = async () => {
    try {
      setIsLoading(true);

      // Fetch project details
      const projectResponse = await apiHelpers.getProject(projectId);
      if (projectResponse.data.success) {
        setProject(projectResponse.data.data);
        // Set communication email from project data
        setCommunicationEmail(projectResponse.data.data.client_email || '');
      }

      // Fetch project meetings
      await fetchMeetings();

      // Fetch project tasks
      const tasksResponse = await apiHelpers.getProjectTasks(projectId);
      if (tasksResponse.data.success) {
        setTasks(tasksResponse.data.data || []);
      }

      // Fetch project documents
      const documentsResponse = await apiHelpers.getProjectDocuments(projectId);
      if (documentsResponse.data.success) {
        setDocuments(documentsResponse.data.data || []);
      }

      // Fetch project invoices
      const invoicesResponse = await apiHelpers.getProjectInvoices(projectId);
      if (invoicesResponse.data.success) {
        setInvoices(invoicesResponse.data.data || []);
      }

      // Fetch project receipts
      const receiptsResponse = await apiHelpers.getReceipts({ project_id: projectId });
      if (receiptsResponse.data.success) {
        setReceipts(receiptsResponse.data.data || []);
      }

    } catch (error) {
      console.error('Error fetching project data:', error);
      toast.error('Failed to load project data');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchMeetings = async () => {
    try {
      const meetingsResponse = await apiHelpers.getProjectMeetings(projectId);
      if (meetingsResponse.data.success) {
        setMeetings(meetingsResponse.data.data || []);
      }
    } catch (error) {
      console.error('Error fetching meetings:', error);
      toast.error('Failed to load meetings');
    }
  };

  const fetchStripeStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/freelancer-billing/stripe-status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStripeStatus(data.data);
      }
    } catch (error) {
      console.error('Error fetching Stripe status:', error);
    }
  };

  // Check for new tasks from meetings
  const checkForNewTasksFromMeetings = () => {
    try {
      const newTasksKey = `project-${projectId}-new-tasks`;
      const newTasksData = localStorage.getItem(newTasksKey);

      if (newTasksData) {
        const newTasks = JSON.parse(newTasksData);
        if (newTasks.length > 0) {
          // Add new tasks to existing tasks
          setTasks(prevTasks => [...prevTasks, ...newTasks]);

          // Clear the localStorage
          localStorage.removeItem(newTasksKey);

          // Show notification
          toast.success(`Added ${newTasks.length} task${newTasks.length > 1 ? 's' : ''} from meeting${newTasks.length > 1 ? 's' : ''}!`);

          // If we're not on the tasks tab, show a notification to switch
          if (activeTab !== 'tasks') {
            setTimeout(() => {
              toast.success('💡 Check the Tasks tab to see your new meeting tasks!', {
                duration: 4000,
              });
            }, 2000);
          }
        }
      }
    } catch (error) {
      console.error('Error checking for new tasks from meetings:', error);
    }
  };

  // Document helper functions
  const handleViewDocument = (doc: Document) => {
    // Create a blob URL for the document (in a real app, this would be the actual file URL)
    const demoUrl = `https://example.com/documents/${doc.id}/${encodeURIComponent(doc.name)}`;

    // For demo purposes, we'll show different behaviors based on file type
    if (doc.file_type.toLowerCase() === 'pdf') {
      // Open PDF in new tab
      window.open(demoUrl, '_blank');
      toast.success('Opening PDF in new tab');
    } else if (['jpg', 'jpeg', 'png', 'gif'].includes(doc.file_type.toLowerCase())) {
      // For images, we could show a modal or open in new tab
      window.open(demoUrl, '_blank');
      toast.success('Opening image in new tab');
    } else {
      // For other file types, trigger download
      handleDownloadDocument(doc);
    }
  };

  const handleDownloadDocument = (doc: Document) => {
    // Use the actual file URL for download
    if (doc.file_url && doc.file_url.startsWith('/uploads/')) {
      // Create a temporary link element to trigger download
      const link = document.createElement('a');
      link.href = doc.file_url;
      link.download = doc.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(`Downloading ${doc.name}...`);
    } else {
      toast.error('File not available for download');
    }
  };

  const getFileIcon = (fileType: string) => {
    const type = fileType.toLowerCase();
    if (['pdf'].includes(type)) return '📄';
    if (['doc', 'docx'].includes(type)) return '📝';
    if (['xls', 'xlsx'].includes(type)) return '📊';
    if (['ppt', 'pptx'].includes(type)) return '📋';
    if (['jpg', 'jpeg', 'png', 'gif'].includes(type)) return '🖼️';
    if (['zip', 'rar'].includes(type)) return '📦';
    return '📁';
  };

  // Handle file upload for tasks
  const handleTaskFileUpload = (files: FileList) => {
    const newDocuments: Document[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const newDoc: Document = {
        id: `task-upload-${Date.now()}-${i}`,
        name: file.name,
        file_type: file.name.split('.').pop() || 'unknown',
        file_size: file.size,
        uploaded_at: new Date().toISOString(),
        uploaded_by: 'Current User'
      };
      newDocuments.push(newDoc);
    }

    setTaskDocuments([...taskDocuments, ...newDocuments]);
    toast.success(`${newDocuments.length} document${newDocuments.length > 1 ? 's' : ''} added to task`);
  };

  // Remove document from task
  const removeTaskDocument = (docId: string) => {
    setTaskDocuments(taskDocuments.filter(doc => doc.id !== docId));
    toast.success('Document removed from task');
  };

  // Handle file upload for edit task
  const handleEditTaskFileUpload = (files: FileList) => {
    const newDocuments: Document[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const newDoc: Document = {
        id: `edit-task-upload-${Date.now()}-${i}`,
        name: file.name,
        file_type: file.name.split('.').pop() || 'unknown',
        file_size: file.size,
        uploaded_at: new Date().toISOString(),
        uploaded_by: 'Current User'
      };
      newDocuments.push(newDoc);
    }

    setEditTaskDocuments([...editTaskDocuments, ...newDocuments]);
    toast.success(`${newDocuments.length} document${newDocuments.length > 1 ? 's' : ''} added to task`);
  };

  // Remove document from edit task
  const removeEditTaskDocument = (docId: string) => {
    setEditTaskDocuments(editTaskDocuments.filter(doc => doc.id !== docId));
    toast.success('Document removed from task');
  };

  // AI Task Generation Functions
  const handleAiFileUpload = (files: FileList) => {
    const newFiles = Array.from(files);
    setAiTaskFiles([...aiTaskFiles, ...newFiles]);
    toast.success(`${newFiles.length} file${newFiles.length > 1 ? 's' : ''} uploaded for AI analysis`);
  };

  const removeAiFile = (index: number) => {
    setAiTaskFiles(aiTaskFiles.filter((_, i) => i !== index));
    toast.success('File removed');
  };

  const generateTasksWithAI = async () => {
    if (!aiTaskText.trim() && aiTaskFiles.length === 0) {
      toast.error('Please provide project documentation or text to analyze');
      return;
    }

    setIsGeneratingTasks(true);

    try {
      let projectDescription = aiTaskText;

      // Process uploaded files to extract content
      if (aiTaskFiles.length > 0) {
        toast.info('Processing uploaded documents...');

        for (const file of aiTaskFiles) {
          try {
            if (file.type === 'text/plain') {
              // Read text files directly
              const text = await file.text();
              projectDescription += `\n\n--- Content from ${file.name} ---\n${text}`;
            } else if (file.type === 'application/pdf') {
              // For PDF files, add file info and note that content extraction is needed
              projectDescription += `\n\n--- PDF Document: ${file.name} ---\n[PDF file uploaded - Size: ${Math.round(file.size / 1024)}KB]\nThis document contains project information that should be analyzed for task generation.`;
            } else {
              // For other file types
              projectDescription += `\n\n--- Document: ${file.name} ---\n[${file.type} file uploaded - Size: ${Math.round(file.size / 1024)}KB]\nThis document contains project information that should be analyzed.`;
            }
          } catch (fileError) {
            console.error('Error processing file:', file.name, fileError);
            projectDescription += `\n\n--- Document: ${file.name} ---\n[Error reading file content - but document was uploaded for analysis]`;
          }
        }
      }

      // Use OpenAI API route
      const response = await fetch('/api/ai/generate-tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectDescription,
          additionalContext: `Project: ${project?.name}, Client: ${project?.client_name}`,
          hasUploadedFiles: aiTaskFiles.length > 0,
          uploadedFileInfo: aiTaskFiles.map(f => ({ name: f.name, type: f.type, size: f.size }))
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate tasks');
      }

      // Check if any tasks were identified
      if (data.tasks.length === 0) {
        setGeneratedTasks([]);
        setSelectedTasks([]);
        if (data.message) {
          toast.error(data.message);
        } else {
          toast.error('No specific tasks could be identified from the provided project description. Please provide more detailed information about what needs to be accomplished.');
        }
        return;
      }

      // Convert AI response to our Task format
      const generatedTaskList: Task[] = data.tasks.map((aiTask: any, index: number) => ({
        id: `ai-task-${Date.now()}-${index + 1}`,
        title: aiTask.title,
        description: aiTask.description,
        status: 'todo' as const,
        priority: aiTask.priority as 'low' | 'medium' | 'high',
        due_date: new Date(Date.now() + (aiTask.estimatedDays || 7) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        assigned_to: aiTask.assigneeRole || 'Team Member',
        created_at: new Date().toISOString(),
        documents: []
      }));

      setGeneratedTasks(generatedTaskList);
      setSelectedTasks(generatedTaskList.map(task => task.id)); // Select all by default

      if (data.fallback) {
        toast.success(`Generated ${generatedTaskList.length} tasks (AI unavailable, using fallback)`);
      } else {
        toast.success(`Generated ${generatedTaskList.length} tasks based on your project description!`);
      }

    } catch (error) {
      console.error('AI task generation error:', error);

      // Show proper error message instead of fallback
      setGeneratedTasks([]);
      setSelectedTasks([]);

      // Check if it's a 503 error (service unavailable)
      if (error instanceof Error && error.message.includes('503')) {
        toast.error('AI service is currently unavailable. Please configure OpenAI API key or try again later.');
      } else {
        toast.error('AI task generation failed. Please try again or create tasks manually.');
      }
    } finally {
      setIsGeneratingTasks(false);
    }
  };

  const toggleTaskSelection = (taskId: string) => {
    setSelectedTasks(prev =>
      prev.includes(taskId)
        ? prev.filter(id => id !== taskId)
        : [...prev, taskId]
    );
  };

  const addSelectedTasksToProject = async () => {
    try {
      const tasksToAdd = generatedTasks.filter(task => selectedTasks.includes(task.id));

      // Create tasks in the backend
      const createdTasks = [];
      for (const task of tasksToAdd) {
        const taskData = {
          title: task.title,
          description: task.description,
          status: task.status,
          priority: task.priority,
          due_date: task.due_date,
          assigned_to: task.assigned_to
        };

        const response = await apiHelpers.createProjectTask(projectId, taskData);
        if (response.data.success) {
          createdTasks.push(response.data.data);
        }
      }

      // Update local state with created tasks
      setTasks([...tasks, ...createdTasks]);
      toast.success(`Added ${createdTasks.length} tasks to your project!`);

      // Clear AI generation state
      setGeneratedTasks([]);
      setSelectedTasks([]);
      setAiTaskText('');
      setAiTaskFiles([]);

      // Switch to tasks tab to show the new tasks
      setActiveTab('tasks');
    } catch (error) {
      console.error('Error adding tasks:', error);
      toast.error('Failed to add tasks to project');
    }
  };

  // AI Documents Functions
  const documentTypes = [
    { id: 'project-proposal', name: 'Project Proposal', description: 'Comprehensive project proposal with scope, timeline, and budget' },
    { id: 'status-report', name: 'Status Report', description: 'Current project status, progress, and next steps' },
    { id: 'meeting-summary', name: 'Meeting Summary', description: 'Consolidated summary of all project meetings' },
    { id: 'technical-spec', name: 'Technical Specification', description: 'Detailed technical requirements and specifications' },
    { id: 'client-presentation', name: 'Client Presentation', description: 'Professional presentation for client review' },
    { id: 'project-timeline', name: 'Project Timeline', description: 'Detailed project timeline with milestones' },
    { id: 'risk-assessment', name: 'Risk Assessment', description: 'Project risks analysis and mitigation strategies' },
    { id: 'final-report', name: 'Final Report', description: 'Comprehensive project completion report' }
  ];

  const dataSources = [
    { id: 'meetings', name: 'Meeting Transcripts & Summaries', count: meetings.length },
    { id: 'tasks', name: 'Project Tasks & Progress', count: tasks.length },
    { id: 'documents', name: 'Project Documents', count: documents.length },
    { id: 'project-info', name: 'Project Information', count: 1 },
    { id: 'invoices', name: 'Financial Information', count: invoices.length }
  ];

  const toggleDataSource = (sourceId: string) => {
    setSelectedDataSources(prev =>
      prev.includes(sourceId)
        ? prev.filter(id => id !== sourceId)
        : [...prev, sourceId]
    );
  };

  const generateDocument = async () => {
    if (!selectedDocumentType || selectedDataSources.length === 0) {
      toast.error('Please select a document type and at least one data source');
      return;
    }

    setIsGeneratingDocument(true);

    try {
      // Get selected document type info
      const docType = documentTypes.find(dt => dt.id === selectedDocumentType);

      // Use OpenAI API route for document generation
      const response = await fetch('/api/ai/generate-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentType: docType?.name || selectedDocumentType,
          projectData: {
            name: project?.name,
            client: project?.client_name,
            meetings: selectedDataSources.includes('meetings') ? meetings : [],
            tasks: selectedDataSources.includes('tasks') ? tasks : [],
            documents: selectedDataSources.includes('documents') ? documents : [],
            invoices: selectedDataSources.includes('invoices') ? invoices : [],
            budget: project?.budget,
            deadline: project?.deadline
          },
          dataSources: selectedDataSources,
          customPrompt: documentPrompt
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate document');
      }

      setGeneratedDocument(data.document);
      setDocumentTitle(docType?.name || 'Generated Document');

      // Initialize chat with welcome message
      setChatMessages([{
        id: Date.now().toString(),
        role: 'assistant',
        content: `I've generated your ${docType?.name} document! You can now chat with me to improve it. For example, you can ask me to:\n\n• Add more details to specific sections\n• Change the tone or style\n• Include additional information\n• Reorganize the content\n• Focus on particular aspects\n\nWhat would you like me to improve?`,
        timestamp: new Date()
      }]);

      toast.success('Document generated successfully!');

    } catch (error) {
      toast.error('Failed to generate document. Please try again.');
      console.error('AI document generation error:', error);
    } finally {
      setIsGeneratingDocument(false);
    }
  };

  // AI Chat Functions
  const sendChatMessage = async () => {
    if (!chatInput.trim() || !generatedDocument) return;

    const userMessage = {
      id: Date.now().toString(),
      role: 'user' as const,
      content: chatInput.trim(),
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsChatting(true);

    try {
      // Use OpenAI API route for chat
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          currentDocument: generatedDocument,
          context: `Project: ${project?.name}, Client: ${project?.client_name}`
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to process chat request');
      }

      // Check if the response contains an updated document
      if (data.response.includes('# ') || data.response.includes('## ')) {
        // If the response looks like a document, update the generated document
        setGeneratedDocument(data.response);

        const assistantMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant' as const,
          content: "I've updated your document based on your request. You can see the changes in the preview above.",
          timestamp: new Date()
        };
        setChatMessages(prev => [...prev, assistantMessage]);
      } else {
        // Regular chat response
        const assistantMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant' as const,
          content: data.response,
          timestamp: new Date()
        };
        setChatMessages(prev => [...prev, assistantMessage]);
      }

    } catch (error) {
      toast.error('Failed to process your request. Please try again.');
      console.error('Chat error:', error);
    } finally {
      setIsChatting(false);
    }
  };

  const generateChatResponse = (userInput: string) => {
    const input = userInput.toLowerCase();

    // Analyze user request and modify document accordingly
    if (input.includes('add') || input.includes('include') || input.includes('more detail')) {
      // Add more content
      const additionalContent = generateAdditionalContent(input);
      setGeneratedDocument(prev => prev + '\n\n' + additionalContent);
      return "I've added more detailed information to your document. The new content has been integrated seamlessly. You can see the updates in the preview above.";
    }

    if (input.includes('shorten') || input.includes('brief') || input.includes('concise')) {
      // Make document more concise
      const shortenedDoc = generateShortenedVersion();
      setGeneratedDocument(shortenedDoc);
      return "I've made the document more concise while keeping all the essential information. The updated version is now displayed above.";
    }

    if (input.includes('professional') || input.includes('formal')) {
      // Make more professional
      const professionalDoc = makeProfessional();
      setGeneratedDocument(professionalDoc);
      return "I've enhanced the document with a more professional tone and formal language. The updated version maintains all key information while improving the overall presentation.";
    }

    if (input.includes('executive summary') || input.includes('summary')) {
      // Add executive summary
      const summaryDoc = addExecutiveSummary();
      setGeneratedDocument(summaryDoc);
      return "I've added a comprehensive executive summary at the beginning of your document. This provides a high-level overview for busy stakeholders.";
    }

    if (input.includes('timeline') || input.includes('schedule') || input.includes('deadline')) {
      // Add timeline information
      const timelineDoc = addTimelineDetails();
      setGeneratedDocument(timelineDoc);
      return "I've enhanced the document with detailed timeline information, including key milestones and deadlines based on your project data.";
    }

    if (input.includes('budget') || input.includes('cost') || input.includes('financial')) {
      // Add financial details
      const financialDoc = addFinancialDetails();
      setGeneratedDocument(financialDoc);
      return "I've added comprehensive financial information to your document, including budget breakdowns and cost analysis based on your project data.";
    }

    // Default response for general improvements
    const improvedDoc = generateGeneralImprovement(input);
    setGeneratedDocument(improvedDoc);
    return `I've improved your document based on your request: "${userInput}". The enhancements include better structure, clearer language, and additional relevant details.`;
  };

  // Document improvement helper functions
  const generateAdditionalContent = (userInput: string) => {
    return `## Additional Information

Based on your request, here are additional details:

### Enhanced Project Scope
- Detailed technical requirements and specifications
- Comprehensive quality assurance procedures
- Extended testing and validation protocols
- Advanced security and compliance measures

### Stakeholder Engagement
- Regular communication schedules with all stakeholders
- Feedback collection and integration processes
- Change management and approval workflows
- Risk mitigation and contingency planning

### Success Metrics
- Key performance indicators (KPIs) for project success
- Quality benchmarks and acceptance criteria
- Timeline adherence and milestone tracking
- Budget compliance and cost optimization

*This additional content was generated based on your request and project data.*`;
  };

  const generateShortenedVersion = () => {
    const lines = generatedDocument.split('\n');
    const essentialLines = lines.filter(line => {
      return line.includes('#') || line.includes('**') || line.includes('•') || line.includes('-') || line.trim().length > 50;
    });
    return essentialLines.join('\n');
  };

  const makeProfessional = () => {
    return generatedDocument
      .replace(/we'll/g, 'we will')
      .replace(/can't/g, 'cannot')
      .replace(/won't/g, 'will not')
      .replace(/I've/g, 'I have')
      .replace(/you'll/g, 'you will')
      + '\n\n## Professional Standards\n\nThis document adheres to industry best practices and professional standards. All recommendations are based on proven methodologies and extensive project experience.';
  };

  const addExecutiveSummary = () => {
    const summary = `## Executive Summary

This document presents a comprehensive overview of ${project?.name} for ${project?.client_name}. Key highlights include:

**Project Scope:** Comprehensive solution delivery with focus on quality and timely execution
**Timeline:** ${project?.deadline ? `Target completion by ${new Date(project.deadline).toLocaleDateString()}` : 'Flexible timeline based on requirements'}
**Investment:** ${project?.budget ? `$${project.budget.toLocaleString()} total project value` : 'Investment details to be finalized'}
**Team:** Experienced professionals with proven track record
**Expected Outcomes:** High-quality deliverables that exceed client expectations

---

`;
    return summary + generatedDocument;
  };

  const addTimelineDetails = () => {
    const timelineSection = `

## Detailed Project Timeline

### Phase Breakdown
${tasks.slice(0, 6).map((task, index) =>
`**Phase ${index + 1}: ${task.title}**
- Duration: ${task.due_date ? `Target completion: ${new Date(task.due_date).toLocaleDateString()}` : 'Timeline to be determined'}
- Priority: ${task.priority}
- Assigned to: ${task.assigned_to || 'Team member to be assigned'}
- Description: ${task.description}`
).join('\n\n')}

### Critical Milestones
- Project kickoff and team alignment
- Requirements finalization and approval
- Development phase completion
- Testing and quality assurance
- Client review and feedback integration
- Final delivery and project closure

*Timeline details are based on current project data and may be adjusted based on requirements.*`;

    return generatedDocument + timelineSection;
  };

  const addFinancialDetails = () => {
    const totalInvoiced = invoices.reduce((sum, inv) => sum + inv.amount, 0);
    const paidAmount = invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.amount, 0);

    const financialSection = `

## Financial Analysis

### Budget Overview
${project?.budget ? `**Total Project Budget:** $${project.budget.toLocaleString()}` : '**Budget:** To be determined based on final scope'}
${totalInvoiced > 0 ? `**Total Invoiced:** $${totalInvoiced.toFixed(2)}` : ''}
${paidAmount > 0 ? `**Amount Paid:** $${paidAmount.toFixed(2)}` : ''}
${totalInvoiced > paidAmount ? `**Outstanding:** $${(totalInvoiced - paidAmount).toFixed(2)}` : ''}

### Cost Breakdown
- **Development Costs:** Primary development and implementation
- **Quality Assurance:** Testing and validation procedures
- **Project Management:** Coordination and oversight
- **Documentation:** Technical and user documentation
- **Support & Maintenance:** Post-delivery support services

### Payment Schedule
${invoices.length > 0 ?
`Current invoicing schedule includes ${invoices.length} invoice${invoices.length > 1 ? 's' : ''} with structured payment milestones.` :
'Payment schedule will be established based on project milestones and deliverables.'}

### Return on Investment
This project is designed to deliver significant value through improved efficiency, enhanced capabilities, and long-term benefits that justify the investment.

*Financial details are based on current project data and contractual agreements.*`;

    return generatedDocument + financialSection;
  };

  const generateGeneralImprovement = (userInput: string) => {
    const improvements = `

## Enhanced Content

Based on your feedback, this document has been improved with:

### Content Enhancements
- More detailed explanations and context
- Better organization and structure
- Enhanced readability and flow
- Additional supporting information

### Quality Improvements
- Professional language and tone
- Consistent formatting and style
- Clear headings and sections
- Comprehensive coverage of topics

### Value Additions
- Actionable recommendations
- Practical implementation guidance
- Risk considerations and mitigation
- Success metrics and evaluation criteria

*These improvements were made based on your specific request: "${userInput}"*`;

    return generatedDocument + improvements;
  };

  // Markdown to HTML converter for preview
  const convertMarkdownToHtml = (markdown: string) => {
    let html = markdown;

    // Handle headings first
    html = html.replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold text-gray-900 mb-4 mt-6">$1</h1>');
    html = html.replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold text-gray-800 mb-3 mt-6">$1</h2>');
    html = html.replace(/^### (.*$)/gim, '<h3 class="text-xl font-medium text-gray-700 mb-2 mt-4">$1</h3>');
    html = html.replace(/^#### (.*$)/gim, '<h4 class="text-lg font-medium text-gray-700 mb-2 mt-3">$1</h4>');

    // Handle bold and italic text (anywhere in the text, not just at line start)
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em class="italic text-gray-700">$1</em>');

    // Handle horizontal rules
    html = html.replace(/^---$/gm, '<hr class="border-gray-300 my-6">');

    // Handle lists - need to group consecutive list items
    const lines = html.split('\n');
    const processedLines = [];
    let inUnorderedList = false;
    let inOrderedList = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // Check for unordered list items
      if (trimmedLine.match(/^[-•] /)) {
        if (!inUnorderedList) {
          processedLines.push('<ul class="list-disc list-inside mb-4 space-y-1">');
          inUnorderedList = true;
        }
        if (inOrderedList) {
          processedLines.push('</ol>');
          inOrderedList = false;
          processedLines.push('<ul class="list-disc list-inside mb-4 space-y-1">');
        }
        const content = trimmedLine.replace(/^[-•] /, '');
        processedLines.push(`<li class="text-gray-700 ml-4">${content}</li>`);
      }
      // Check for ordered list items
      else if (trimmedLine.match(/^\d+\. /)) {
        if (!inOrderedList) {
          processedLines.push('<ol class="list-decimal list-inside mb-4 space-y-1">');
          inOrderedList = true;
        }
        if (inUnorderedList) {
          processedLines.push('</ul>');
          inUnorderedList = false;
          processedLines.push('<ol class="list-decimal list-inside mb-4 space-y-1">');
        }
        const content = trimmedLine.replace(/^\d+\. /, '');
        processedLines.push(`<li class="text-gray-700 ml-4">${content}</li>`);
      }
      // Regular content
      else {
        // Close any open lists
        if (inUnorderedList) {
          processedLines.push('</ul>');
          inUnorderedList = false;
        }
        if (inOrderedList) {
          processedLines.push('</ol>');
          inOrderedList = false;
        }

        // Handle regular paragraphs
        if (trimmedLine === '') {
          processedLines.push('');
        } else if (!trimmedLine.startsWith('<h') && !trimmedLine.startsWith('<hr')) {
          processedLines.push(`<p class="text-gray-700 mb-4 leading-relaxed">${line}</p>`);
        } else {
          processedLines.push(line);
        }
      }
    }

    // Close any remaining open lists
    if (inUnorderedList) {
      processedLines.push('</ul>');
    }
    if (inOrderedList) {
      processedLines.push('</ol>');
    }

    html = processedLines.join('\n');

    // Handle code blocks (basic support)
    html = html.replace(/`([^`]+)`/g, '<code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono">$1</code>');

    // Handle links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-800 underline">$1</a>');

    return html;
  };

  // Document generation helper functions
  const generateProjectProposal = () => {
    return `# Project Proposal: ${project?.name}

## Executive Summary
This proposal outlines the comprehensive plan for ${project?.name}, a project for ${project?.client_name}. Based on our analysis of project requirements and stakeholder meetings, we present a detailed approach to deliver exceptional results.

## Project Overview
**Client:** ${project?.client_name}
**Project Duration:** ${project?.deadline ? `Target completion: ${new Date(project.deadline).toLocaleDateString()}` : 'To be determined'}
**Budget:** ${project?.budget ? `$${project.budget.toLocaleString()}` : 'To be discussed'}

## Scope of Work
Based on our ${meetings.length} stakeholder meeting${meetings.length !== 1 ? 's' : ''} and project analysis, the scope includes:

${tasks.filter(t => selectedDataSources.includes('tasks')).map(task => `• ${task.title}: ${task.description}`).join('\n')}

## Project Timeline
We have identified ${tasks.length} key tasks that will be executed in phases:
- **Phase 1:** Project initiation and planning
- **Phase 2:** Development and implementation
- **Phase 3:** Testing and quality assurance
- **Phase 4:** Deployment and handover

## Deliverables
${documents.length > 0 && selectedDataSources.includes('documents') ?
`Based on project documentation, key deliverables include:
${documents.slice(0, 5).map(doc => `• ${doc.name}`).join('\n')}` :
'Detailed deliverables will be defined based on project requirements.'}

## Investment
${invoices.length > 0 && selectedDataSources.includes('invoices') ?
`Total project investment: $${invoices.reduce((sum, inv) => sum + inv.amount, 0).toFixed(2)}
Payment structure: Milestone-based payments as outlined in our invoicing schedule.` :
`Investment details will be provided based on final scope confirmation.`}

## Next Steps
1. Review and approve this proposal
2. Sign project agreement
3. Initiate project kickoff meeting
4. Begin Phase 1 execution

We look forward to partnering with ${project?.client_name} on this exciting project.

---
*Prepared using KaiNote AI on ${new Date().toLocaleDateString()}*`;
  };

  const generateStatusReport = () => {
    const completedTasks = tasks.filter(t => t.status === 'completed').length;
    const inProgressTasks = tasks.filter(t => t.status === 'in_progress').length;
    const pendingTasks = tasks.filter(t => t.status === 'pending').length;
    const progressPercentage = tasks.length > 0 ? Math.round((completedTasks / tasks.length) * 100) : 0;

    return `# Project Status Report: ${project?.name}
**Report Date:** ${new Date().toLocaleDateString()}
**Client:** ${project?.client_name}

## Executive Summary
Project ${project?.name} is currently ${progressPercentage}% complete with ${completedTasks} tasks completed out of ${tasks.length} total tasks.

## Progress Overview
- ✅ **Completed Tasks:** ${completedTasks}
- 🔄 **In Progress:** ${inProgressTasks}
- ⏳ **Pending:** ${pendingTasks}
- 📊 **Overall Progress:** ${progressPercentage}%

## Recent Activities
${meetings.length > 0 && selectedDataSources.includes('meetings') ?
`### Recent Meetings (${meetings.length} total)
${meetings.slice(0, 3).map(meeting =>
`**${meeting.title}** - ${new Date(meeting.recorded_at).toLocaleDateString()}
Duration: ${meeting.duration_minutes} minutes | Status: ${meeting.transcription_status}`
).join('\n\n')}` : ''}

## Task Status Details
${selectedDataSources.includes('tasks') ?
`### Completed Tasks
${tasks.filter(t => t.status === 'completed').slice(0, 5).map(task =>
`• ${task.title} - Completed ${task.due_date ? `by ${new Date(task.due_date).toLocaleDateString()}` : ''}`
).join('\n')}

### In Progress
${tasks.filter(t => t.status === 'in_progress').slice(0, 5).map(task =>
`• ${task.title} - ${task.assigned_to ? `Assigned to: ${task.assigned_to}` : ''}`
).join('\n')}

### Upcoming Tasks
${tasks.filter(t => t.status === 'pending').slice(0, 5).map(task =>
`• ${task.title} - ${task.due_date ? `Due: ${new Date(task.due_date).toLocaleDateString()}` : ''}`
).join('\n')}` : ''}

## Financial Status
${invoices.length > 0 && selectedDataSources.includes('invoices') ?
`- **Total Invoiced:** $${invoices.reduce((sum, inv) => sum + inv.amount, 0).toFixed(2)}
- **Paid:** $${invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.amount, 0).toFixed(2)}
- **Outstanding:** $${invoices.filter(inv => inv.status !== 'paid').reduce((sum, inv) => sum + inv.amount, 0).toFixed(2)}` :
'Financial details will be updated as invoicing progresses.'}

## Next Steps
1. Continue execution of in-progress tasks
2. Address any blockers or dependencies
3. Prepare for upcoming milestones
4. Schedule next client review meeting

---
*Prepared using KaiNote AI on ${new Date().toLocaleDateString()}*`;
  };

  const generateMeetingSummary = () => {
    return `# Meeting Summary Report: ${project?.name}
**Client:** ${project?.client_name}
**Report Period:** Project inception to ${new Date().toLocaleDateString()}

## Overview
This document consolidates insights from ${meetings.length} project meeting${meetings.length !== 1 ? 's' : ''} conducted for ${project?.name}.

${selectedDataSources.includes('meetings') && meetings.length > 0 ?
`## Meeting Details
${meetings.map((meeting, index) =>
`### Meeting ${index + 1}: ${meeting.title}
**Date:** ${new Date(meeting.recorded_at).toLocaleDateString()}
**Duration:** ${meeting.duration_minutes} minutes
**Status:** ${meeting.transcription_status}

**Key Discussion Points:**
- Project requirements and scope clarification
- Technical approach and implementation strategy
- Timeline and milestone discussions
- Resource allocation and team assignments

**Action Items Generated:**
- Follow-up tasks assigned to team members
- Client feedback incorporation
- Technical documentation updates
- Next meeting scheduling

---`).join('\n\n')}` : 'Meeting details will be included as meetings are conducted.'}

## Key Themes Across Meetings
1. **Project Scope:** Consistent focus on delivering high-quality solutions
2. **Timeline Management:** Regular discussions on milestone achievement
3. **Client Collaboration:** Strong emphasis on client feedback integration
4. **Technical Excellence:** Commitment to best practices and quality standards

## Decisions Made
- Technology stack and architecture decisions
- Project timeline and milestone definitions
- Resource allocation and team structure
- Communication protocols and review processes

## Next Steps
- Continue regular meeting cadence
- Implement decisions made in previous meetings
- Prepare for upcoming milestone reviews
- Maintain open communication channels

---
*Prepared using KaiNote AI on ${new Date().toLocaleDateString()}*`;
  };

  const generateTechnicalSpec = () => {
    return `# Technical Specification: ${project?.name}
**Client:** ${project?.client_name}
**Version:** 1.0
**Date:** ${new Date().toLocaleDateString()}

## Project Overview
${project?.description || 'Comprehensive technical specification for project implementation.'}

## Technical Requirements
Based on project analysis and stakeholder meetings, the following technical requirements have been identified:

### System Architecture
- **Frontend:** Modern web application with responsive design
- **Backend:** Scalable API architecture
- **Database:** Robust data storage and management
- **Security:** Enterprise-grade security implementation

### Functional Requirements
${selectedDataSources.includes('tasks') ?
`Based on project tasks analysis:
${tasks.slice(0, 8).map(task =>
`**${task.title}**
- Description: ${task.description}
- Priority: ${task.priority}
- Implementation Notes: Technical implementation details to be defined during development phase`
).join('\n\n')}` : 'Functional requirements will be detailed based on project scope.'}

### Non-Functional Requirements
- **Performance:** System should handle expected load with optimal response times
- **Scalability:** Architecture should support future growth and expansion
- **Security:** Implementation of industry-standard security practices
- **Maintainability:** Code should be well-documented and maintainable

### Technology Stack
- **Frontend Framework:** To be determined based on project requirements
- **Backend Technology:** Selected based on scalability and performance needs
- **Database Solution:** Chosen for data requirements and performance
- **Deployment Platform:** Cloud-based solution for reliability and scalability

### Integration Requirements
${documents.length > 0 && selectedDataSources.includes('documents') ?
`Based on project documentation:
${documents.slice(0, 5).map(doc => `- Integration with ${doc.name} system`).join('\n')}` :
'Integration requirements will be defined based on system analysis.'}

### Testing Strategy
- **Unit Testing:** Comprehensive unit test coverage
- **Integration Testing:** End-to-end integration testing
- **Performance Testing:** Load and stress testing
- **Security Testing:** Vulnerability assessment and penetration testing

### Deployment Strategy
- **Environment Setup:** Development, staging, and production environments
- **CI/CD Pipeline:** Automated build, test, and deployment processes
- **Monitoring:** Comprehensive system monitoring and alerting
- **Backup Strategy:** Regular data backup and disaster recovery procedures

---
*Prepared using KaiNote AI on ${new Date().toLocaleDateString()}*`;
  };

  const generateGenericDocument = () => {
    return `# ${documentTypes.find(dt => dt.id === selectedDocumentType)?.name || 'Project Document'}: ${project?.name}
**Client:** ${project?.client_name}
**Generated:** ${new Date().toLocaleDateString()}

## Document Overview
${documentPrompt || 'This document has been generated based on the selected project data sources and requirements.'}

## Project Information
${selectedDataSources.includes('project-info') ?
`**Project Name:** ${project?.name}
**Client:** ${project?.client_name}
**Description:** ${project?.description || 'Project description to be provided'}
${project?.deadline ? `**Deadline:** ${new Date(project.deadline).toLocaleDateString()}` : ''}
${project?.budget ? `**Budget:** $${project.budget.toLocaleString()}` : ''}` : ''}

## Data Analysis
${selectedDataSources.includes('meetings') && meetings.length > 0 ?
`### Meeting Insights (${meetings.length} meetings analyzed)
${meetings.slice(0, 3).map(meeting => `- ${meeting.title}: ${meeting.duration_minutes} minutes`).join('\n')}` : ''}

${selectedDataSources.includes('tasks') && tasks.length > 0 ?
`### Task Analysis (${tasks.length} tasks reviewed)
- Completed: ${tasks.filter(t => t.status === 'completed').length}
- In Progress: ${tasks.filter(t => t.status === 'in_progress').length}
- Pending: ${tasks.filter(t => t.status === 'pending').length}` : ''}

${selectedDataSources.includes('documents') && documents.length > 0 ?
`### Document Review (${documents.length} documents analyzed)
${documents.slice(0, 5).map(doc => `- ${doc.name} (${doc.file_type.toUpperCase()})`).join('\n')}` : ''}

${selectedDataSources.includes('invoices') && invoices.length > 0 ?
`### Financial Overview (${invoices.length} invoices reviewed)
- Total Amount: $${invoices.reduce((sum, inv) => sum + inv.amount, 0).toFixed(2)}
- Paid: $${invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.amount, 0).toFixed(2)}` : ''}

## Recommendations
Based on the analysis of selected data sources, we recommend:
1. Continue monitoring project progress and milestones
2. Maintain regular communication with stakeholders
3. Ensure quality standards are met throughout execution
4. Document lessons learned for future projects

## Conclusion
This document provides a comprehensive overview based on the available project data. For more detailed information, please refer to the specific project components and documentation.

---
*Prepared using KaiNote AI on ${new Date().toLocaleDateString()}*`;
  };

  const generateClientPresentation = () => {
    return `# Client Presentation: ${project?.name}
**Prepared for:** ${project?.client_name}
**Date:** ${new Date().toLocaleDateString()}

---

## Slide 1: Project Overview
### ${project?.name}
**Your Partner in Success**

---

## Slide 2: Project Highlights
- **Client:** ${project?.client_name}
- **Timeline:** ${project?.deadline ? `Target: ${new Date(project.deadline).toLocaleDateString()}` : 'Flexible timeline'}
- **Scope:** Comprehensive solution delivery
- **Team:** Experienced professionals

---

## Slide 3: Progress Summary
### Current Status
- **Total Tasks:** ${tasks.length}
- **Completed:** ${tasks.filter(t => t.status === 'completed').length}
- **In Progress:** ${tasks.filter(t => t.status === 'in_progress').length}
- **Progress:** ${tasks.length > 0 ? Math.round((tasks.filter(t => t.status === 'completed').length / tasks.length) * 100) : 0}%

---

## Slide 4: Key Achievements
${tasks.filter(t => t.status === 'completed').slice(0, 5).map(task => `✅ ${task.title}`).join('\n')}

---

## Slide 5: Upcoming Milestones
${tasks.filter(t => t.status === 'pending').slice(0, 5).map(task => `🎯 ${task.title}${task.due_date ? ` - ${new Date(task.due_date).toLocaleDateString()}` : ''}`).join('\n')}

---

## Slide 6: Collaboration Success
### Meeting Engagement
- **Total Meetings:** ${meetings.length}
- **Active Collaboration:** Regular touchpoints
- **Feedback Integration:** Continuous improvement

---

## Slide 7: Next Steps
1. **Continue Current Phase**
2. **Stakeholder Review**
3. **Milestone Achievement**
4. **Quality Assurance**

---

## Slide 8: Thank You
### Questions & Discussion
**Contact:** Your Project Team
**Next Meeting:** To be scheduled

---
*Presentation prepared using KaiNote AI*`;
  };

  const generateProjectTimeline = () => {
    return `# Project Timeline: ${project?.name}
**Client:** ${project?.client_name}
**Timeline Overview:** ${new Date().toLocaleDateString()} onwards

## Project Phases

### Phase 1: Project Initiation
**Duration:** Week 1-2
- Project kickoff and team alignment
- Requirements gathering and analysis
- Initial planning and resource allocation

### Phase 2: Design & Planning
**Duration:** Week 3-4
- System design and architecture
- Detailed project planning
- Resource confirmation and scheduling

### Phase 3: Development & Implementation
**Duration:** Week 5-8
${selectedDataSources.includes('tasks') ?
`**Key Tasks:**
${tasks.filter(t => t.status !== 'completed').slice(0, 6).map(task =>
`- ${task.title}${task.due_date ? ` (Due: ${new Date(task.due_date).toLocaleDateString()})` : ''}`
).join('\n')}` : '- Core development activities\n- Feature implementation\n- Integration work'}

### Phase 4: Testing & Quality Assurance
**Duration:** Week 9-10
- Comprehensive testing procedures
- Quality assurance and validation
- Performance optimization

### Phase 5: Deployment & Handover
**Duration:** Week 11-12
- Production deployment
- User training and documentation
- Project handover and closure

## Milestones
${tasks.filter(t => t.priority === 'high').slice(0, 5).map((task, index) =>
`**Milestone ${index + 1}:** ${task.title}
Target Date: ${task.due_date ? new Date(task.due_date).toLocaleDateString() : 'TBD'}
Status: ${task.status.replace('_', ' ').toUpperCase()}`
).join('\n\n')}

## Risk Mitigation Timeline
- **Week 2:** Risk assessment completion
- **Week 4:** Mitigation strategies implementation
- **Week 6:** Mid-project risk review
- **Week 10:** Final risk evaluation

${project?.deadline ? `## Project Deadline
**Target Completion:** ${new Date(project.deadline).toLocaleDateString()}
**Buffer Time:** Built into timeline for quality assurance` : ''}

---
*Prepared using KaiNote AI on ${new Date().toLocaleDateString()}*`;
  };

  const generateRiskAssessment = () => {
    return `# Risk Assessment: ${project?.name}
**Client:** ${project?.client_name}
**Assessment Date:** ${new Date().toLocaleDateString()}

## Executive Summary
This risk assessment identifies potential project risks and provides mitigation strategies for ${project?.name}.

## Risk Categories

### 1. Technical Risks
**Risk Level:** Medium
- **Complexity Risk:** Technical implementation challenges
- **Integration Risk:** System integration complexities
- **Performance Risk:** Meeting performance requirements

**Mitigation Strategies:**
- Regular technical reviews and validation
- Prototype development and testing
- Performance monitoring and optimization

### 2. Timeline Risks
**Risk Level:** ${tasks.filter(t => t.status === 'pending').length > tasks.filter(t => t.status === 'completed').length ? 'High' : 'Medium'}
- **Scope Creep:** Uncontrolled expansion of project scope
- **Resource Availability:** Team member availability issues
- **Dependency Delays:** External dependency impacts

**Mitigation Strategies:**
- Clear scope definition and change control
- Resource planning and backup allocation
- Dependency tracking and early identification

### 3. Quality Risks
**Risk Level:** Low
- **Requirements Clarity:** Unclear or changing requirements
- **Testing Coverage:** Insufficient testing procedures
- **Documentation Quality:** Inadequate project documentation

**Mitigation Strategies:**
- Regular stakeholder reviews and validation
- Comprehensive testing strategy implementation
- Continuous documentation updates

### 4. Communication Risks
**Risk Level:** Low
**Current Status:** ${meetings.length} meetings conducted
- **Stakeholder Alignment:** Misaligned expectations
- **Information Flow:** Communication gaps
- **Decision Making:** Delayed decision processes

**Mitigation Strategies:**
- Regular meeting schedule maintenance
- Clear communication protocols
- Escalation procedures for decisions

### 5. Financial Risks
**Risk Level:** ${invoices.length > 0 ? 'Low' : 'Medium'}
${invoices.length > 0 && selectedDataSources.includes('invoices') ?
`**Current Status:** $${invoices.reduce((sum, inv) => sum + inv.amount, 0).toFixed(2)} total invoiced
- **Budget Overrun:** Exceeding allocated budget
- **Payment Delays:** Client payment timing issues` :
`- **Budget Definition:** Budget parameters to be established
- **Cost Control:** Expense monitoring and control`}

**Mitigation Strategies:**
- Regular budget monitoring and reporting
- Clear payment terms and schedules
- Cost control measures implementation

## Risk Monitoring Plan
- **Weekly:** Task progress and timeline review
- **Bi-weekly:** Technical risk assessment
- **Monthly:** Comprehensive risk review meeting
- **Milestone-based:** Quality and delivery risk evaluation

## Contingency Plans
1. **Technical Issues:** Expert consultation and alternative approaches
2. **Timeline Delays:** Resource reallocation and scope prioritization
3. **Quality Concerns:** Additional testing and review cycles
4. **Communication Gaps:** Enhanced meeting frequency and documentation

## Conclusion
Overall project risk level is assessed as **MEDIUM** with appropriate mitigation strategies in place. Regular monitoring and proactive management will ensure successful project delivery.

---
*Prepared using KaiNote AI on ${new Date().toLocaleDateString()}*`;
  };

  const generateFinalReport = () => {
    const completedTasks = tasks.filter(t => t.status === 'completed').length;
    const totalTasks = tasks.length;
    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    return `# Final Project Report: ${project?.name}
**Client:** ${project?.client_name}
**Project Completion Date:** ${new Date().toLocaleDateString()}
**Report Prepared By:** Project Team

## Executive Summary
Project ${project?.name} has been successfully completed for ${project?.client_name}. This comprehensive report summarizes the project execution, achievements, and outcomes.

## Project Overview
- **Project Name:** ${project?.name}
- **Client:** ${project?.client_name}
- **Project Duration:** ${meetings.length > 0 ? `${meetings.length} meetings conducted` : 'Timeline as planned'}
- **Completion Rate:** ${completionRate}%
${project?.budget ? `- **Budget:** $${project.budget.toLocaleString()}` : ''}

## Project Achievements
### Task Completion Summary
- **Total Tasks:** ${totalTasks}
- **Completed Tasks:** ${completedTasks}
- **Success Rate:** ${completionRate}%

### Key Deliverables Completed
${tasks.filter(t => t.status === 'completed').slice(0, 8).map(task =>
`✅ **${task.title}**
   ${task.description}
   Completed: ${task.due_date ? new Date(task.due_date).toLocaleDateString() : 'On schedule'}`
).join('\n\n')}

## Project Execution
### Meeting Summary
${selectedDataSources.includes('meetings') && meetings.length > 0 ?
`Total meetings conducted: ${meetings.length}
${meetings.map(meeting =>
`- **${meeting.title}:** ${new Date(meeting.recorded_at).toLocaleDateString()} (${meeting.duration_minutes} min)`
).join('\n')}

**Key Outcomes:**
- Effective stakeholder communication
- Regular progress reviews
- Timely decision making
- Strong client collaboration` : 'Regular communication maintained throughout project lifecycle.'}

### Documentation Delivered
${selectedDataSources.includes('documents') && documents.length > 0 ?
`${documents.map(doc => `📄 ${doc.name} (${doc.file_type.toUpperCase()})`).join('\n')}` :
'All required project documentation has been delivered as specified.'}

## Financial Summary
${selectedDataSources.includes('invoices') && invoices.length > 0 ?
`- **Total Project Value:** $${invoices.reduce((sum, inv) => sum + inv.amount, 0).toFixed(2)}
- **Invoices Issued:** ${invoices.length}
- **Payment Status:** ${invoices.filter(inv => inv.status === 'paid').length} paid, ${invoices.filter(inv => inv.status !== 'paid').length} pending
- **Financial Performance:** On budget and schedule` :
'Financial performance met project expectations and budget requirements.'}

## Quality Metrics
- **Deliverable Quality:** All deliverables met or exceeded quality standards
- **Timeline Performance:** ${completionRate >= 90 ? 'Excellent' : completionRate >= 75 ? 'Good' : 'Satisfactory'} adherence to schedule
- **Client Satisfaction:** High level of client engagement and feedback
- **Team Performance:** Effective collaboration and execution

## Lessons Learned
### What Went Well
- Strong client communication and collaboration
- Effective project planning and execution
- Quality deliverable production
- Timely milestone achievement

### Areas for Improvement
- Continuous process optimization opportunities
- Enhanced documentation procedures
- Improved risk mitigation strategies
- Streamlined communication protocols

## Recommendations for Future Projects
1. **Process Enhancement:** Implement lessons learned in future engagements
2. **Client Relationship:** Maintain ongoing relationship for future opportunities
3. **Team Development:** Continue team skill development and training
4. **Technology Advancement:** Stay current with industry best practices

## Project Closure
### Handover Completed
- All deliverables transferred to client
- Documentation provided and reviewed
- Training sessions conducted as required
- Support transition plan implemented

### Post-Project Support
- Warranty period: As per contract terms
- Support contact: Project team available
- Maintenance plan: Ongoing support available
- Future enhancements: Ready for discussion

## Conclusion
Project ${project?.name} has been successfully completed, meeting all specified requirements and delivering exceptional value to ${project?.client_name}. The project demonstrates our commitment to quality, timeliness, and client satisfaction.

We thank ${project?.client_name} for their trust and collaboration throughout this project and look forward to future opportunities to work together.

---
**Project Team Signatures**
*Final report prepared using KaiNote AI on ${new Date().toLocaleDateString()}*`;
  };

  // Filter invoices based on search and status
  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.invoice_number.toLowerCase().includes(invoiceSearchTerm.toLowerCase()) ||
                         project?.name.toLowerCase().includes(invoiceSearchTerm.toLowerCase()) ||
                         project?.client_name.toLowerCase().includes(invoiceSearchTerm.toLowerCase());

    const matchesStatus = invoiceStatusFilter === 'all' || invoice.status === invoiceStatusFilter;

    return matchesSearch && matchesStatus;
  });

  // Invoice status color helper
  const getInvoiceStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  // Email integration functions
  const sendEmailViaGmail = (receipt: Receipt) => {
    const subject = `Payment Receipt ${receipt.receipt_number} - ${project?.name}`;
    const body = generateEmailBody(receipt);

    const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=${encodeURIComponent(project?.client_email || '')}&su=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(gmailUrl, '_blank');
    toast.success('Gmail opened with receipt details');
  };

  const sendEmailViaOutlook = (receipt: Receipt) => {
    const subject = `Payment Receipt ${receipt.receipt_number} - ${project?.name}`;
    const body = generateEmailBody(receipt);

    const outlookUrl = `https://outlook.live.com/mail/0/deeplink/compose?to=${encodeURIComponent(project?.client_email || '')}&subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(outlookUrl, '_blank');
    toast.success('Outlook opened with receipt details');
  };

  const generateEmailBody = (receipt: Receipt) => {
    return `Dear ${project?.client_name},

Thank you for your payment! Please find your receipt details below:

Receipt Number: ${receipt.receipt_number}
Invoice Number: ${receipt.invoice_number}
Amount: $${receipt.amount.toFixed(2)} ${receipt.currency}
Payment Date: ${new Date(receipt.payment_date).toLocaleDateString()}
Payment Method: ${receipt.payment_method}
${receipt.transaction_id ? `Transaction ID: ${receipt.transaction_id}` : ''}

${receipt.notes ? `Notes: ${receipt.notes}` : ''}

You can also download a copy of this receipt from our client portal.

Best regards,
Your Project Team

---
This receipt was prepared using KaiNote`;
  };

  // Receipt helper functions
  const downloadReceipt = (receipt: Receipt) => {
    const receiptContent = generateReceiptContent(receipt);
    const blob = new Blob([receiptContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${receipt.receipt_number}_${project?.client_name?.replace(/\s+/g, '_')}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Receipt downloaded successfully!');
  };

  const emailReceipt = (receipt: Receipt) => {
    // Show email options modal
    setSelectedReceiptForEmail(receipt);
    setShowEmailOptionsModal(true);
  };

  const generateReceiptContent = (receipt: Receipt) => {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Receipt ${receipt.receipt_number}</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .receipt-info { display: flex; justify-content: space-between; margin-bottom: 30px; }
        .amount { font-size: 24px; font-weight: bold; color: #059669; }
        .details { margin-bottom: 20px; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #ccc; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>PAYMENT RECEIPT</h1>
        <h2>${receipt.receipt_number}</h2>
    </div>

    <div class="receipt-info">
        <div>
            <h3>From:</h3>
            <p><strong>Your Business Name</strong><br>
            Your Address<br>
            Your Email</p>
        </div>
        <div>
            <h3>To:</h3>
            <p><strong>${project?.client_name}</strong><br>
            ${project?.client_email || ''}</p>
        </div>
    </div>

    <div class="details">
        <h3>Payment Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
            <tr style="border-bottom: 1px solid #ddd;">
                <td style="padding: 10px;"><strong>Receipt Number:</strong></td>
                <td style="padding: 10px;">${receipt.receipt_number}</td>
            </tr>
            <tr style="border-bottom: 1px solid #ddd;">
                <td style="padding: 10px;"><strong>Invoice Number:</strong></td>
                <td style="padding: 10px;">${receipt.invoice_number}</td>
            </tr>
            <tr style="border-bottom: 1px solid #ddd;">
                <td style="padding: 10px;"><strong>Project:</strong></td>
                <td style="padding: 10px;">${project?.name}</td>
            </tr>
            <tr style="border-bottom: 1px solid #ddd;">
                <td style="padding: 10px;"><strong>Payment Date:</strong></td>
                <td style="padding: 10px;">${new Date(receipt.payment_date).toLocaleDateString()}</td>
            </tr>
            <tr style="border-bottom: 1px solid #ddd;">
                <td style="padding: 10px;"><strong>Payment Method:</strong></td>
                <td style="padding: 10px;">${receipt.payment_method}</td>
            </tr>
            ${receipt.transaction_id ? `
            <tr style="border-bottom: 1px solid #ddd;">
                <td style="padding: 10px;"><strong>Transaction ID:</strong></td>
                <td style="padding: 10px;">${receipt.transaction_id}</td>
            </tr>` : ''}
            <tr style="border-bottom: 2px solid #333;">
                <td style="padding: 15px;"><strong>Amount Received:</strong></td>
                <td style="padding: 15px;" class="amount">$${receipt.amount.toFixed(2)} ${receipt.currency}</td>
            </tr>
        </table>
    </div>

    ${receipt.notes ? `
    <div class="details">
        <h3>Notes</h3>
        <p>${receipt.notes}</p>
    </div>` : ''}

    <div class="footer">
        <p>Thank you for your payment!</p>
        <p><em>This receipt was prepared using KaiNote on ${new Date().toLocaleDateString()}</em></p>
    </div>
</body>
</html>`;
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: DocumentTextIcon },
    { id: 'meetings', name: 'Meetings', icon: ChatBubbleLeftRightIcon },
    { id: 'tasks', name: 'Tasks', icon: ClipboardDocumentListIcon },
    { id: 'ai-tasks', name: 'AI Tasks', icon: SparklesIcon },
    { id: 'ai-docs', name: 'AI Documents', icon: SparklesIcon },
    { id: 'documents', name: 'Documents', icon: DocumentDuplicateIcon },
    { id: 'invoices', name: 'Invoices', icon: CurrencyDollarIcon },
    { id: 'receipts', name: 'Receipts', icon: ReceiptPercentIcon },
    { id: 'client', name: 'Client Portal', icon: UserGroupIcon },
  ];

  if (isLoading) {
    return (
      <DashboardLayout>
        <div>
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              <div className="lg:col-span-1">
                <div className="h-64 bg-gray-200 rounded"></div>
              </div>
              <div className="lg:col-span-3">
                <div className="h-96 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!project) {
    return (
      <DashboardLayout>
        <div>
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Project not found</h1>
            <Link href="/projects" className="text-primary-600 hover:text-primary-500 mt-4 inline-block">
              ← Back to Projects
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link
                  href="/projects"
                  className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-1" />
                  Back to Projects
                </Link>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setActiveTab('client')}
                  className="btn btn-secondary"
                >
                  <ShareIcon className="h-4 w-4 mr-2" />
                  Share with Client
                </button>
                <button
                  onClick={() => setShowNewMeetingForm(true)}
                  className="btn btn-primary"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  New Meeting
                </button>
              </div>
            </div>
            
            <div className="mt-4">
              <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <UserGroupIcon className="h-4 w-4 mr-1" />
                  {project.client_name}
                </span>
                {project.deadline && (
                  <span className="flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    Due {new Date(project.deadline).toLocaleDateString()}
                  </span>
                )}
                {project.budget && (
                  <span className="flex items-center">
                    <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                    ${project.budget.toLocaleString()}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                      activeTab === tab.id
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white shadow rounded-lg">
              {activeTab === 'overview' && (
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Project Overview</h2>
                  
                  {/* Project Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{meetings.length}</div>
                      <div className="text-sm text-blue-600">Total Meetings</div>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{tasks.length}</div>
                      <div className="text-sm text-purple-600">Total Tasks</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {tasks.filter(t => t.status === 'completed').length}
                      </div>
                      <div className="text-sm text-green-600">Completed Tasks</div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">
                        {tasks.filter(t => t.status === 'todo').length}
                      </div>
                      <div className="text-sm text-yellow-600">Todo Tasks</div>
                    </div>
                  </div>

                  {/* Project Progress */}
                  <div className="mb-8">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Project Progress</h3>
                    <div className="bg-gray-200 rounded-full h-3 mb-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500"
                        style={{
                          width: `${tasks.length > 0 ? (tasks.filter(t => t.status === 'completed').length / tasks.length) * 100 : 0}%`
                        }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>
                        {tasks.filter(t => t.status === 'completed').length} of {tasks.length} tasks completed
                      </span>
                      <span>
                        {tasks.length > 0 ? Math.round((tasks.filter(t => t.status === 'completed').length / tasks.length) * 100) : 0}%
                      </span>
                    </div>
                  </div>

                  {/* Project Description */}
                  {project.description && (
                    <div className="mb-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Description</h3>
                      <p className="text-gray-600">{project.description}</p>
                    </div>
                  )}

                  {/* Recent Activity */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Recent Meetings */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Meetings</h3>
                      <div className="space-y-3">
                        {meetings.length === 0 ? (
                          <div className="text-center py-6 text-gray-500">
                            <ChatBubbleLeftRightIcon className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                            <p className="text-sm">No meetings yet</p>
                          </div>
                        ) : (
                          meetings.slice(0, 3).map((meeting) => (
                            <Link
                              key={meeting.id}
                              href={`/meetings/${meeting.id}`}
                              className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                            >
                              <ChatBubbleLeftRightIcon className="h-5 w-5 text-gray-400" />
                              <div className="flex-1">
                                <p className="text-sm font-medium text-gray-900">{meeting.title}</p>
                                <p className="text-xs text-gray-500">
                                  {new Date(meeting.recorded_at).toLocaleDateString()} • {meeting.duration_minutes}m
                                </p>
                              </div>
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                meeting.transcription_status === 'completed'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {meeting.transcription_status}
                              </span>
                            </Link>
                          ))
                        )}
                      </div>
                    </div>

                    {/* Recent Tasks */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Tasks</h3>
                      <div className="space-y-3">
                        {tasks.length === 0 ? (
                          <div className="text-center py-6 text-gray-500">
                            <ClipboardDocumentListIcon className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                            <p className="text-sm">No tasks yet</p>
                          </div>
                        ) : (
                          tasks.slice(0, 3).map((task) => (
                            <div
                              key={task.id}
                              className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
                            >
                              <div className={`w-3 h-3 rounded-full ${
                                task.status === 'completed' ? 'bg-green-500' :
                                task.status === 'in_progress' ? 'bg-blue-500' :
                                'bg-gray-400'
                              }`}></div>
                              <div className="flex-1">
                                <p className={`text-sm font-medium ${
                                  task.status === 'completed' ? 'text-gray-500 line-through' : 'text-gray-900'
                                }`}>
                                  {task.title}
                                </p>
                                <div className="flex items-center space-x-2 text-xs text-gray-500">
                                  <span className={`px-2 py-1 rounded-full ${
                                    task.priority === 'high' ? 'bg-red-100 text-red-800' :
                                    task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-green-100 text-green-800'
                                  }`}>
                                    {task.priority}
                                  </span>
                                  {task.due_date && (
                                    <span>Due: {new Date(task.due_date).toLocaleDateString()}</span>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'meetings' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Project Meetings</h2>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setMeetingType('previous');
                          setShowNewMeetingForm(true);
                        }}
                        className="inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                      >
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Add Previous Meeting
                      </button>
                      <button
                        onClick={() => {
                          setMeetingType('schedule');
                          setShowNewMeetingForm(true);
                        }}
                        className="btn btn-primary"
                      >
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Schedule Meeting
                      </button>
                    </div>
                  </div>
                  
                  {meetings.length === 0 ? (
                    <div className="text-center py-12">
                      <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No meetings yet</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Schedule your first meeting to get started.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {meetings.map((meeting) => (
                        <div key={meeting.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="text-lg font-medium text-gray-900">{meeting.title}</h3>
                              <p className="text-sm text-gray-500">
                                {new Date(meeting.recorded_at).toLocaleDateString()} • {meeting.duration_minutes} minutes
                              </p>
                            </div>
                            <div className="flex items-center space-x-3">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                meeting.transcription_status === 'completed' 
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {meeting.transcription_status}
                              </span>
                              <Link 
                                href={`/meetings/${meeting.id}`}
                                className="text-primary-600 hover:text-primary-500 text-sm font-medium"
                              >
                                View Details →
                              </Link>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Tasks Tab */}
              {activeTab === 'tasks' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Project Tasks</h2>
                    <button
                      onClick={() => setShowNewTaskForm(true)}
                      className="btn btn-primary"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Add Task
                    </button>
                  </div>

                  {/* Task Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{tasks.length}</div>
                      <div className="text-sm text-blue-600">Total Tasks</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {tasks.filter(t => t.status === 'completed').length}
                      </div>
                      <div className="text-sm text-green-600">Completed</div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">
                        {tasks.filter(t => t.status === 'in_progress').length}
                      </div>
                      <div className="text-sm text-yellow-600">In Progress</div>
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">
                        {tasks.filter(t => t.status === 'todo').length}
                      </div>
                      <div className="text-sm text-red-600">Todo</div>
                    </div>
                  </div>

                  {/* Tasks List */}
                  <div className="space-y-4">
                    {tasks.map((task) => (
                      <div key={task.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {/* Status Control Circles */}
                            <div className="flex items-center space-x-2">
                              {/* In Progress Circle */}
                              <button
                                onClick={async () => {
                                  try {
                                    const newStatus = task.status === 'in_progress' ? 'todo' : 'in_progress';
                                    const response = await apiHelpers.updateProjectTask(task.id, { status: newStatus });
                                    if (response.data.success) {
                                      setTasks(tasks.map(t => t.id === task.id ? {...t, status: newStatus} : t));
                                      toast.success(`Task marked as ${newStatus === 'in_progress' ? 'in progress' : 'todo'}`);
                                    }
                                  } catch (error) {
                                    toast.error('Failed to update task status');
                                  }
                                }}
                                className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                                  task.status === 'in_progress'
                                    ? 'bg-blue-500 border-blue-500 text-white hover:bg-blue-600 hover:border-blue-600'
                                    : 'bg-white border-gray-300 text-transparent hover:border-blue-400 hover:bg-blue-50'
                                }`}
                                title={task.status === 'in_progress' ? 'Mark as pending' : 'Mark as in progress'}
                              >
                                <div className="w-2 h-2 bg-current rounded-full"></div>
                              </button>

                              {/* Completed Circle */}
                              <button
                                onClick={async () => {
                                  try {
                                    const newStatus = task.status === 'completed' ? 'todo' : 'completed';
                                    const response = await apiHelpers.updateProjectTask(task.id, { status: newStatus });
                                    if (response.data.success) {
                                      setTasks(tasks.map(t => t.id === task.id ? {...t, status: newStatus} : t));
                                      toast.success(`Task marked as ${newStatus === 'completed' ? 'complete' : 'todo'}`);
                                    }
                                  } catch (error) {
                                    toast.error('Failed to update task status');
                                  }
                                }}
                                className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                                  task.status === 'completed'
                                    ? 'bg-green-500 border-green-500 text-white hover:bg-green-600 hover:border-green-600'
                                    : 'bg-white border-gray-300 text-transparent hover:border-green-400 hover:bg-green-50'
                                }`}
                                title={task.status === 'completed' ? 'Mark as pending' : 'Mark as complete'}
                              >
                                <CheckIcon className="h-3 w-3" />
                              </button>
                            </div>
                            <div className="flex-1">
                              <h3 className={`text-lg font-medium ${
                                task.status === 'completed' ? 'text-gray-500 line-through' : 'text-gray-900'
                              }`}>
                                {task.title}
                              </h3>
                              {task.description && (
                                <p className="text-sm text-gray-500">{task.description}</p>
                              )}
                              <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                <span className={`px-2 py-1 rounded-full ${
                                  task.priority === 'high' ? 'bg-red-100 text-red-800' :
                                  task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-green-100 text-green-800'
                                }`}>
                                  {task.priority} priority
                                </span>
                                <span className={`px-2 py-1 rounded-full ${
                                  task.status === 'completed' ? 'bg-green-100 text-green-800' :
                                  task.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {task.status.replace('_', ' ')}
                                </span>
                                {task.due_date && (
                                  <span>Due: {new Date(task.due_date).toLocaleDateString()}</span>
                                )}
                                {task.assigned_to && (
                                  <span>Assigned to: {task.assigned_to}</span>
                                )}
                                {(task as any).source === 'meeting' && (
                                  <span
                                    className="flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-full cursor-help"
                                    title={`Generated from meeting: ${(task as any).meeting_title || 'Unknown Meeting'}`}
                                  >
                                    <ChatBubbleLeftRightIcon className="h-3 w-3 mr-1" />
                                    From Meeting
                                  </span>
                                )}
                                {task.documents && task.documents.length > 0 && (
                                  <span className="flex items-center">
                                    <DocumentDuplicateIcon className="h-3 w-3 mr-1" />
                                    {task.documents.length} document{task.documents.length > 1 ? 's' : ''}
                                  </span>
                                )}
                              </div>

                              {/* Task Documents */}
                              {task.documents && task.documents.length > 0 && (
                                <div className="mt-3 pt-3 border-t border-gray-100">
                                  <div className="text-xs text-gray-500 mb-2">Attached Documents:</div>
                                  <div className="flex flex-wrap gap-2">
                                    {task.documents.map((doc) => (
                                      <div key={doc.id} className="flex items-center bg-gray-50 rounded-md px-2 py-1 text-xs">
                                        <span className="mr-1">{getFileIcon(doc.file_type)}</span>
                                        <span className="text-gray-700 mr-2 max-w-24 truncate" title={doc.name}>
                                          {doc.name}
                                        </span>
                                        <div className="flex items-center space-x-1">
                                          <button
                                            onClick={() => handleViewDocument(doc)}
                                            className="text-blue-600 hover:text-blue-800 p-1"
                                            title="View document"
                                          >
                                            <EyeIcon className="h-3 w-3" />
                                          </button>
                                          <button
                                            onClick={() => handleDownloadDocument(doc)}
                                            className="text-green-600 hover:text-green-800 p-1"
                                            title="Download document"
                                          >
                                            <ArrowDownTrayIcon className="h-3 w-3" />
                                          </button>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => {
                                setEditingTask(task);
                                setEditTaskDocuments(task.documents || []); // Initialize with existing documents
                              }}
                              className="p-2 text-gray-400 hover:text-blue-600"
                              title="Edit task"
                            >
                              <PencilIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => {
                                if (confirm('Are you sure you want to delete this task?')) {
                                  setTasks(tasks.filter(t => t.id !== task.id));
                                  toast.success('Task deleted');
                                }
                              }}
                              className="p-2 text-gray-400 hover:text-red-600"
                              title="Delete task"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* New Task Form */}
                  {showNewTaskForm && (
                    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
                      <div className="bg-white rounded-lg p-6 w-full max-w-md">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Task</h3>
                        <form onSubmit={(e) => {
                          e.preventDefault();
                          const formData = new FormData(e.target as HTMLFormElement);
                          const newTask: Task = {
                            id: Date.now().toString(),
                            title: formData.get('title') as string,
                            description: formData.get('description') as string,
                            status: 'pending',
                            priority: formData.get('priority') as 'low' | 'medium' | 'high',
                            due_date: formData.get('due_date') as string,
                            assigned_to: formData.get('assigned_to') as string,
                            created_at: new Date().toISOString(),
                            documents: [...taskDocuments]
                          };
                          setTasks([...tasks, newTask]);
                          setShowNewTaskForm(false);
                          setTaskDocuments([]); // Clear uploaded documents
                          toast.success('Task added successfully');
                        }}>
                          <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Title</label>
                              <input
                                type="text"
                                name="title"
                                required
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Description</label>
                              <textarea
                                name="description"
                                rows={3}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Priority</label>
                              <select
                                name="priority"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              >
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                              </select>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Due Date</label>
                              <input
                                type="date"
                                name="due_date"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Assigned To</label>
                              <input
                                type="text"
                                name="assigned_to"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>

                            {/* Document Upload Section - Extra Compact */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Documents</label>
                              <div className="border border-dashed border-gray-300 rounded-md p-1">
                                <input
                                  type="file"
                                  multiple
                                  accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif"
                                  onChange={(e) => {
                                    if (e.target.files && e.target.files.length > 0) {
                                      handleTaskFileUpload(e.target.files);
                                      e.target.value = ''; // Reset input
                                    }
                                  }}
                                  className="hidden"
                                  id="task-file-upload"
                                />
                                <label
                                  htmlFor="task-file-upload"
                                  className="cursor-pointer flex items-center justify-center text-center py-1"
                                >
                                  <DocumentDuplicateIcon className="h-3 w-3 text-gray-400 mr-1" />
                                  <span className="text-xs text-gray-600">Upload</span>
                                </label>
                              </div>

                              {/* Uploaded Documents Preview - Extra Compact */}
                              {taskDocuments.length > 0 && (
                                <div className="mt-1">
                                  <div className="flex flex-wrap gap-1">
                                    {taskDocuments.map((doc) => (
                                      <div key={doc.id} className="flex items-center bg-gray-100 rounded px-1 py-0.5 text-xs">
                                        <span className="mr-1">{getFileIcon(doc.file_type)}</span>
                                        <span className="text-gray-700 max-w-16 truncate" title={doc.name}>
                                          {doc.name}
                                        </span>
                                        <button
                                          type="button"
                                          onClick={() => removeTaskDocument(doc.id)}
                                          className="ml-1 text-red-500 hover:text-red-700"
                                          title="Remove"
                                        >
                                          ×
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex justify-end space-x-3 mt-6">
                            <button
                              type="button"
                              onClick={() => {
                                setShowNewTaskForm(false);
                                setTaskDocuments([]); // Clear uploaded documents
                              }}
                              className="btn btn-secondary"
                            >
                              Cancel
                            </button>
                            <button type="submit" className="btn btn-primary">
                              Add Task
                            </button>
                          </div>
                        </form>
                      </div>
                    </div>
                  )}

                  {/* Edit Task Form */}
                  {editingTask && (
                    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
                      <div className="bg-white rounded-lg p-6 w-full max-w-md">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Edit Task</h3>
                        <form onSubmit={(e) => {
                          e.preventDefault();
                          const formData = new FormData(e.target as HTMLFormElement);
                          const updatedTask: Task = {
                            ...editingTask,
                            title: formData.get('title') as string,
                            description: formData.get('description') as string,
                            priority: formData.get('priority') as 'low' | 'medium' | 'high',
                            due_date: formData.get('due_date') as string,
                            assigned_to: formData.get('assigned_to') as string,
                            documents: [...editTaskDocuments]
                          };
                          setTasks(tasks.map(t => t.id === editingTask.id ? updatedTask : t));
                          setEditingTask(null);
                          setEditTaskDocuments([]); // Clear edit documents
                          toast.success('Task updated successfully');
                        }}>
                          <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Title</label>
                              <input
                                type="text"
                                name="title"
                                defaultValue={editingTask.title}
                                required
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Description</label>
                              <textarea
                                name="description"
                                defaultValue={editingTask.description}
                                rows={3}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Priority</label>
                              <select
                                name="priority"
                                defaultValue={editingTask.priority}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              >
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                              </select>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Due Date</label>
                              <input
                                type="date"
                                name="due_date"
                                defaultValue={editingTask.due_date}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Assigned To</label>
                              <input
                                type="text"
                                name="assigned_to"
                                defaultValue={editingTask.assigned_to}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>

                            {/* Document Upload Section - Extra Compact */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Documents</label>
                              <div className="border border-dashed border-gray-300 rounded-md p-1">
                                <input
                                  type="file"
                                  multiple
                                  accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif"
                                  onChange={(e) => {
                                    if (e.target.files && e.target.files.length > 0) {
                                      handleEditTaskFileUpload(e.target.files);
                                      e.target.value = ''; // Reset input
                                    }
                                  }}
                                  className="hidden"
                                  id="edit-task-file-upload"
                                />
                                <label
                                  htmlFor="edit-task-file-upload"
                                  className="cursor-pointer flex items-center justify-center text-center py-1"
                                >
                                  <DocumentDuplicateIcon className="h-3 w-3 text-gray-400 mr-1" />
                                  <span className="text-xs text-gray-600">Upload</span>
                                </label>
                              </div>

                              {/* Uploaded Documents Preview - Extra Compact */}
                              {editTaskDocuments.length > 0 && (
                                <div className="mt-1">
                                  <div className="flex flex-wrap gap-1">
                                    {editTaskDocuments.map((doc) => (
                                      <div key={doc.id} className="flex items-center bg-gray-100 rounded px-1 py-0.5 text-xs">
                                        <span className="mr-1">{getFileIcon(doc.file_type)}</span>
                                        <span className="text-gray-700 max-w-16 truncate" title={doc.name}>
                                          {doc.name}
                                        </span>
                                        <button
                                          type="button"
                                          onClick={() => removeEditTaskDocument(doc.id)}
                                          className="ml-1 text-red-500 hover:text-red-700"
                                          title="Remove"
                                        >
                                          ×
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex justify-end space-x-3 mt-6">
                            <button
                              type="button"
                              onClick={() => {
                                setEditingTask(null);
                                setEditTaskDocuments([]); // Clear edit documents
                              }}
                              className="btn btn-secondary"
                            >
                              Cancel
                            </button>
                            <button type="submit" className="btn btn-primary">
                              Update Task
                            </button>
                          </div>
                        </form>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* AI Tasks Tab */}
              {activeTab === 'ai-tasks' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900">AI Task Generator</h2>
                      <p className="text-sm text-gray-600 mt-1">Upload project documentation or paste text to automatically generate tasks</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <SparklesIcon className="h-5 w-5 text-purple-500" />
                      <span className="text-sm font-medium text-purple-600">Powered by AI</span>
                    </div>
                  </div>

                  {/* Input Section */}
                  <div className="bg-white rounded-lg shadow p-6 mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Project Documentation</h3>

                    {/* Text Input */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Paste Project Requirements or Description
                      </label>
                      <textarea
                        value={aiTaskText}
                        onChange={(e) => setAiTaskText(e.target.value)}
                        placeholder="Paste your project requirements, specifications, or any text that describes what needs to be done. The AI will analyze this content and generate relevant tasks..."
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      />
                    </div>

                    {/* File Upload */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Upload Project Documents
                      </label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <input
                          type="file"
                          multiple
                          accept=".pdf,.doc,.docx,.txt,.md"
                          onChange={(e) => {
                            if (e.target.files && e.target.files.length > 0) {
                              handleAiFileUpload(e.target.files);
                              e.target.value = '';
                            }
                          }}
                          className="hidden"
                          id="ai-file-upload"
                        />
                        <label
                          htmlFor="ai-file-upload"
                          className="cursor-pointer flex flex-col items-center"
                        >
                          <DocumentDuplicateIcon className="h-12 w-12 text-gray-400 mb-3" />
                          <span className="text-sm font-medium text-gray-900">Click to upload files</span>
                          <span className="text-xs text-gray-500 mt-1">PDF, Word, Text, Markdown files</span>
                        </label>
                      </div>

                      {/* Uploaded Files */}
                      {aiTaskFiles.length > 0 && (
                        <div className="mt-4">
                          <div className="text-sm font-medium text-gray-700 mb-2">Uploaded Files:</div>
                          <div className="space-y-2">
                            {aiTaskFiles.map((file, index) => (
                              <div key={index} className="flex items-center justify-between bg-gray-50 rounded-md px-3 py-2">
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm">{getFileIcon(file.name.split('.').pop() || '')}</span>
                                  <span className="text-sm text-gray-700">{file.name}</span>
                                  <span className="text-xs text-gray-500">
                                    ({(file.size / 1024 / 1024).toFixed(1)} MB)
                                  </span>
                                </div>
                                <button
                                  onClick={() => removeAiFile(index)}
                                  className="text-red-500 hover:text-red-700 p-1"
                                  title="Remove file"
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Generate Button */}
                    <div className="flex justify-center">
                      <button
                        onClick={generateTasksWithAI}
                        disabled={isGeneratingTasks || (!aiTaskText.trim() && aiTaskFiles.length === 0)}
                        className={`px-6 py-3 rounded-lg font-medium flex items-center space-x-2 ${
                          isGeneratingTasks || (!aiTaskText.trim() && aiTaskFiles.length === 0)
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-purple-600 text-white hover:bg-purple-700'
                        }`}
                      >
                        {isGeneratingTasks ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            <span>AI is analyzing your project...</span>
                          </>
                        ) : (
                          <>
                            <SparklesIcon className="h-4 w-4" />
                            <span>Generate Tasks with AI</span>
                          </>
                        )}
                      </button>
                    </div>

                    {/* AI Processing Indicator */}
                    {isGeneratingTasks && (
                      <div className="mt-4 p-4 bg-purple-50 rounded-lg border border-purple-200">
                        <div className="flex items-start space-x-3">
                          <div className="animate-pulse">
                            <SparklesIcon className="h-6 w-6 text-purple-500" />
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-purple-900">AI is analyzing your project</h4>
                            <p className="text-sm text-purple-700 mt-1">
                              Creating intelligent, actionable tasks based on your project description.
                              This usually takes a few moments for the best results.
                            </p>
                            <div className="mt-3 flex items-center space-x-2">
                              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                              <span className="text-xs text-purple-600 ml-2">Processing...</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Generated Tasks Section */}
                  {generatedTasks.length > 0 && (
                    <div className="bg-white rounded-lg shadow p-6">
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-medium text-gray-900">Generated Tasks</h3>
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() => {
                              const allSelected = selectedTasks.length === generatedTasks.length;
                              setSelectedTasks(allSelected ? [] : generatedTasks.map(t => t.id));
                            }}
                            className="text-sm text-purple-600 hover:text-purple-800"
                          >
                            {selectedTasks.length === generatedTasks.length ? 'Deselect All' : 'Select All'}
                          </button>
                          <button
                            onClick={addSelectedTasksToProject}
                            disabled={selectedTasks.length === 0}
                            className={`px-4 py-2 rounded-lg font-medium ${
                              selectedTasks.length === 0
                                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                : 'bg-green-600 text-white hover:bg-green-700'
                            }`}
                          >
                            Add {selectedTasks.length} Task{selectedTasks.length !== 1 ? 's' : ''} to Project
                          </button>
                        </div>
                      </div>

                      <div className="space-y-3">
                        {generatedTasks.map((task) => (
                          <div
                            key={task.id}
                            className={`border rounded-lg p-4 transition-colors ${
                              selectedTasks.includes(task.id)
                                ? 'border-purple-300 bg-purple-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <div className="flex items-start space-x-3">
                              <input
                                type="checkbox"
                                checked={selectedTasks.includes(task.id)}
                                onChange={() => toggleTaskSelection(task.id)}
                                className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                              />
                              <div className="flex-1">
                                <h4 className="font-medium text-gray-900">{task.title}</h4>
                                <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                                <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                  <span className={`px-2 py-1 rounded-full ${
                                    task.priority === 'high' ? 'bg-red-100 text-red-800' :
                                    task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-green-100 text-green-800'
                                  }`}>
                                    {task.priority} priority
                                  </span>
                                  {task.due_date && (
                                    <span>Due: {new Date(task.due_date).toLocaleDateString()}</span>
                                  )}
                                  {task.assigned_to && (
                                    <span>Assigned to: {task.assigned_to}</span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                        <div className="flex items-start space-x-2">
                          <SparklesIcon className="h-5 w-5 text-blue-500 mt-0.5" />
                          <div>
                            <h4 className="text-sm font-medium text-blue-900">AI Generated Tasks</h4>
                            <p className="text-sm text-blue-700 mt-1">
                              These tasks were automatically generated based on your project documentation.
                              Review and select the tasks you want to add to your project. You can edit them later if needed.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* AI Documents Tab */}
              {activeTab === 'ai-docs' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900">AI Document Generator</h2>
                      <p className="text-sm text-gray-600 mt-1">Generate professional documents using project data and AI</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <SparklesIcon className="h-5 w-5 text-purple-500" />
                      <span className="text-sm font-medium text-purple-600">Powered by AI</span>
                    </div>
                  </div>

                  {/* Document Generation Form */}
                  <div className="bg-white rounded-lg shadow p-6 mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-6">Document Configuration</h3>

                    {/* Document Type Selection */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Select Document Type
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {documentTypes.map((docType) => (
                          <div
                            key={docType.id}
                            onClick={() => setSelectedDocumentType(docType.id)}
                            className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                              selectedDocumentType === docType.id
                                ? 'border-purple-500 bg-purple-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <h4 className="font-medium text-gray-900 mb-1">{docType.name}</h4>
                            <p className="text-xs text-gray-500">{docType.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Data Sources Selection */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Select Data Sources
                      </label>
                      <div className="space-y-2">
                        {dataSources.map((source) => (
                          <div
                            key={source.id}
                            className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                          >
                            <div className="flex items-center space-x-3">
                              <input
                                type="checkbox"
                                checked={selectedDataSources.includes(source.id)}
                                onChange={() => toggleDataSource(source.id)}
                                className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                              />
                              <div>
                                <span className="text-sm font-medium text-gray-900">{source.name}</span>
                                <span className="text-xs text-gray-500 ml-2">({source.count} items)</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Custom Prompt */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Additional Instructions (Optional)
                      </label>
                      <textarea
                        value={documentPrompt}
                        onChange={(e) => setDocumentPrompt(e.target.value)}
                        placeholder="Add any specific requirements or focus areas for the document..."
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      />
                    </div>

                    {/* Generate Button */}
                    <div className="flex justify-center">
                      <button
                        onClick={generateDocument}
                        disabled={isGeneratingDocument || !selectedDocumentType || selectedDataSources.length === 0}
                        className={`px-6 py-3 rounded-lg font-medium flex items-center space-x-2 ${
                          isGeneratingDocument || !selectedDocumentType || selectedDataSources.length === 0
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-purple-600 text-white hover:bg-purple-700'
                        }`}
                      >
                        {isGeneratingDocument ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            <span>AI is creating your document...</span>
                          </>
                        ) : (
                          <>
                            <SparklesIcon className="h-4 w-4" />
                            <span>Generate Document</span>
                          </>
                        )}
                      </button>
                    </div>

                    {/* AI Document Processing Indicator */}
                    {isGeneratingDocument && (
                      <div className="mt-4 p-4 bg-purple-50 rounded-lg border border-purple-200">
                        <div className="flex items-start space-x-3">
                          <div className="animate-pulse">
                            <SparklesIcon className="h-6 w-6 text-purple-500" />
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-purple-900">AI is creating your professional document</h4>
                            <p className="text-sm text-purple-700 mt-1">
                              Analyzing your project data and generating a comprehensive {documentTypes.find(dt => dt.id === selectedDocumentType)?.name.toLowerCase() || 'document'}.
                              This process takes a few moments to ensure high quality.
                            </p>
                            <div className="mt-3 flex items-center space-x-2">
                              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                              <span className="text-xs text-purple-600 ml-2">Processing...</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Generated Document Display */}
                  {generatedDocument && (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Document Preview */}
                      <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-lg font-medium text-gray-900">{documentTitle}</h3>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => setShowPreview(!showPreview)}
                              className={`px-3 py-1 text-sm rounded-lg flex items-center space-x-2 ${
                                showPreview
                                  ? 'bg-blue-100 text-blue-700'
                                  : 'bg-gray-100 text-gray-700'
                              }`}
                            >
                              <EyeIcon className="h-4 w-4" />
                              <span>{showPreview ? 'Preview' : 'Markdown'}</span>
                            </button>
                          </div>
                        </div>

                        {/* Document Content */}
                        <div className="border border-gray-200 rounded-lg bg-white max-h-96 overflow-y-auto">
                          {showPreview ? (
                            <div className="p-6">
                              <div
                                className="prose prose-sm max-w-none document-preview"
                                dangerouslySetInnerHTML={{
                                  __html: convertMarkdownToHtml(generatedDocument)
                                }}
                                style={{
                                  fontFamily: 'system-ui, -apple-system, sans-serif',
                                  lineHeight: '1.6',
                                  color: '#374151'
                                }}
                              />
                            </div>
                          ) : (
                            <div className="p-6 bg-gray-50">
                              <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono leading-relaxed">
                                {generatedDocument}
                              </pre>
                            </div>
                          )}
                        </div>

                        {/* Document Actions */}
                        <div className="mt-4 flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => {
                                navigator.clipboard.writeText(generatedDocument);
                                toast.success('Document copied to clipboard!');
                              }}
                              className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 flex items-center space-x-2"
                            >
                              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                              <span>Copy</span>
                            </button>
                            <button
                              onClick={() => {
                                const blob = new Blob([generatedDocument], { type: 'text/markdown' });
                                const url = URL.createObjectURL(blob);
                                const a = document.createElement('a');
                                a.href = url;
                                a.download = `${documentTitle.replace(/\s+/g, '_').toLowerCase()}.md`;
                                document.body.appendChild(a);
                                a.click();
                                document.body.removeChild(a);
                                URL.revokeObjectURL(url);
                                toast.success('Document downloaded!');
                              }}
                              className="px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
                            >
                              <ArrowDownTrayIcon className="h-4 w-4" />
                              <span>Download</span>
                            </button>
                          </div>
                          <button
                            onClick={() => {
                              setGeneratedDocument('');
                              setDocumentTitle('');
                              setSelectedDocumentType('');
                              setSelectedDataSources([]);
                              setDocumentPrompt('');
                              setChatMessages([]);
                            }}
                            className="px-3 py-2 text-sm bg-red-100 text-red-700 rounded-lg hover:bg-red-200 flex items-center space-x-2"
                          >
                            <TrashIcon className="h-4 w-4" />
                            <span>Clear All</span>
                          </button>
                        </div>
                      </div>

                      {/* AI Chat Interface */}
                      <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center space-x-2 mb-4">
                          <SparklesIcon className="h-5 w-5 text-purple-500" />
                          <h3 className="text-lg font-medium text-gray-900">Chat with AI</h3>
                          <span className="text-sm text-gray-500">Improve your document</span>
                        </div>

                        {/* Chat Messages */}
                        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50 h-64 overflow-y-auto mb-4">
                          {chatMessages.length === 0 ? (
                            <div className="text-center text-gray-500 mt-8">
                              <ChatBubbleLeftRightIcon className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                              <p className="text-sm">Start chatting to improve your document!</p>
                              <p className="text-xs mt-1">Ask me to add details, change tone, or reorganize content.</p>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              {chatMessages.map((message) => (
                                <div
                                  key={message.id}
                                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                                >
                                  <div
                                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                      message.role === 'user'
                                        ? 'bg-blue-600 text-white'
                                        : 'bg-white border border-gray-200 text-gray-800'
                                    }`}
                                  >
                                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                                    <p className={`text-xs mt-1 ${
                                      message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                                    }`}>
                                      {message.timestamp.toLocaleTimeString()}
                                    </p>
                                  </div>
                                </div>
                              ))}
                              {isChatting && (
                                <div className="flex justify-start">
                                  <div className="bg-white border border-gray-200 text-gray-800 px-4 py-2 rounded-lg">
                                    <div className="flex items-center space-x-1">
                                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        {/* Chat Input */}
                        <div className="flex space-x-2">
                          <input
                            type="text"
                            value={chatInput}
                            onChange={(e) => setChatInput(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}
                            placeholder="Ask me to improve the document..."
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            disabled={isChatting}
                          />
                          <button
                            onClick={sendChatMessage}
                            disabled={!chatInput.trim() || isChatting}
                            className={`px-4 py-2 rounded-lg font-medium flex items-center space-x-2 ${
                              !chatInput.trim() || isChatting
                                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                : 'bg-purple-600 text-white hover:bg-purple-700'
                            }`}
                          >
                            {isChatting ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : (
                              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                              </svg>
                            )}
                          </button>
                        </div>

                        {/* Quick Suggestions */}
                        <div className="mt-4">
                          <p className="text-xs text-gray-500 mb-2">Quick suggestions:</p>
                          <div className="flex flex-wrap gap-2">
                            {[
                              "Add more details",
                              "Make it more professional",
                              "Add executive summary",
                              "Include timeline",
                              "Add financial details",
                              "Make it shorter"
                            ].map((suggestion) => (
                              <button
                                key={suggestion}
                                onClick={() => setChatInput(suggestion)}
                                className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded hover:bg-gray-200 transition-colors"
                              >
                                {suggestion}
                              </button>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Documents Tab */}
              {activeTab === 'documents' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Project Documents</h2>
                    <button
                      onClick={() => setShowDocumentUpload(true)}
                      className="btn btn-primary"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Upload Document
                    </button>
                  </div>

                  {documents.length === 0 ? (
                    <div className="text-center py-12">
                      <DocumentDuplicateIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No documents yet</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Upload your first document to get started.
                      </p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {documents.map((doc) => (
                        <div key={doc.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <div className="flex-shrink-0">
                                <div className="text-2xl">{getFileIcon(doc.file_type)}</div>
                              </div>
                              <div className="flex-1 min-w-0">
                                <h3 className="text-sm font-medium text-gray-900 truncate" title={doc.name}>
                                  {doc.name}
                                </h3>
                                <p className="text-xs text-gray-500">
                                  {(doc.file_size / 1024 / 1024).toFixed(2)} MB • {doc.file_type.toUpperCase()}
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* Document Actions */}
                          <div className="flex items-center justify-between">
                            <div className="text-xs text-gray-500">
                              <p>By {doc.uploaded_by}</p>
                              <p>{new Date(doc.uploaded_at).toLocaleDateString()}</p>
                            </div>
                            <div className="flex items-center space-x-1">
                              <button
                                className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                                title="View document"
                                onClick={() => handleViewDocument(doc)}
                              >
                                <EyeIcon className="h-4 w-4" />
                              </button>
                              <button
                                className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-md transition-colors"
                                title="Download document"
                                onClick={() => handleDownloadDocument(doc)}
                              >
                                <ArrowDownTrayIcon className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => {
                                  if (confirm('Are you sure you want to delete this document?')) {
                                    setDocuments(documents.filter(d => d.id !== doc.id));
                                    toast.success('Document deleted');
                                  }
                                }}
                                className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
                                title="Delete document"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </div>

                          {/* Quick Preview for Images */}
                          {['jpg', 'jpeg', 'png', 'gif'].includes(doc.file_type.toLowerCase()) && (
                            <div className="mt-3 pt-3 border-t border-gray-100">
                              <div className="text-xs text-gray-500 mb-2">Image Preview:</div>
                              <div
                                className="w-full h-20 bg-gray-100 rounded border-2 border-dashed border-gray-300 flex items-center justify-center cursor-pointer hover:bg-gray-50"
                                onClick={() => handleViewDocument(doc)}
                              >
                                <span className="text-gray-400 text-xs">Click to view full image</span>
                              </div>
                            </div>
                          )}

                          {/* File Type Specific Actions */}
                          {doc.file_type.toLowerCase() === 'pdf' && (
                            <div className="mt-3 pt-3 border-t border-gray-100">
                              <button
                                onClick={() => handleViewDocument(doc)}
                                className="w-full text-xs bg-blue-50 text-blue-700 py-2 px-3 rounded-md hover:bg-blue-100 transition-colors"
                              >
                                📄 Open PDF in New Tab
                              </button>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Invoices Tab */}
              {activeTab === 'invoices' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Project Invoices & Billing</h2>
                    <div className="flex items-center gap-3">
                      {!stripeStatus?.connected && (
                        <Link
                          href="/integrations/stripe"
                          className="bg-blue-50 text-blue-700 px-4 py-2 rounded-lg hover:bg-blue-100 flex items-center gap-2 border border-blue-200"
                        >
                          <CurrencyDollarIcon className="h-4 w-4" />
                          Enable Online Payments
                        </Link>
                      )}
                      <Link
                        href="/invoices/settings"
                        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
                      >
                        <Cog6ToothIcon className="h-4 w-4" />
                        Customize
                      </Link>
                      <button
                        onClick={() => setShowNewInvoiceForm(true)}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
                      >
                        <PlusIcon className="h-4 w-4" />
                        New Invoice
                      </button>
                    </div>
                  </div>

                  {/* Stripe Connection Status */}
                  {stripeStatus && (
                    <div className={`mb-6 p-4 rounded-lg border ${
                      stripeStatus.connected
                        ? 'bg-green-50 border-green-200'
                        : 'bg-blue-50 border-blue-200'
                    }`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <CurrencyDollarIcon className={`h-5 w-5 mr-2 ${
                            stripeStatus.connected ? 'text-green-600' : 'text-blue-600'
                          }`} />
                          <div>
                            <h3 className={`text-sm font-medium ${
                              stripeStatus.connected ? 'text-green-900' : 'text-blue-900'
                            }`}>
                              {stripeStatus.connected ? 'Online Payments Enabled' : 'Enable Online Payments'}
                            </h3>
                            <p className={`text-sm ${
                              stripeStatus.connected ? 'text-green-700' : 'text-blue-700'
                            }`}>
                              {stripeStatus.connected
                                ? 'Clients can pay invoices online. Money goes directly to your account.'
                                : 'Connect Stripe to let clients pay invoices online with credit cards.'
                              }
                            </p>
                          </div>
                        </div>
                        {!stripeStatus.connected && (
                          <Link
                            href="/integrations/stripe"
                            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm font-medium"
                          >
                            Connect Stripe
                          </Link>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Invoice Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div className="bg-white p-6 rounded-lg shadow">
                      <h3 className="text-sm font-medium text-gray-500">Total Invoiced</h3>
                      <p className="text-2xl font-bold text-gray-900">
                        ${invoices.reduce((sum, i) => sum + i.amount, 0).toFixed(2)}
                      </p>
                    </div>
                    <div className="bg-white p-6 rounded-lg shadow">
                      <h3 className="text-sm font-medium text-gray-500">Paid</h3>
                      <p className="text-2xl font-bold text-green-600">
                        ${invoices.filter(i => i.status === 'paid').reduce((sum, i) => sum + i.amount, 0).toFixed(2)}
                      </p>
                    </div>
                    <div className="bg-white p-6 rounded-lg shadow">
                      <h3 className="text-sm font-medium text-gray-500">Outstanding</h3>
                      <p className="text-2xl font-bold text-orange-600">
                        ${invoices.filter(i => i.status !== 'paid').reduce((sum, i) => sum + i.amount, 0).toFixed(2)}
                      </p>
                    </div>
                  </div>

                  {/* Search and Filters */}
                  <div className="bg-white p-4 rounded-lg shadow mb-6">
                    <div className="flex flex-col sm:flex-row gap-4">
                      <div className="flex-1">
                        <div className="relative">
                          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                          <input
                            type="text"
                            placeholder="Search invoices..."
                            value={invoiceSearchTerm}
                            onChange={(e) => setInvoiceSearchTerm(e.target.value)}
                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <FunnelIcon className="h-4 w-4 text-gray-400" />
                        <select
                          value={invoiceStatusFilter}
                          onChange={(e) => setInvoiceStatusFilter(e.target.value)}
                          className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="all">All Status</option>
                          <option value="draft">Draft</option>
                          <option value="sent">Sent</option>
                          <option value="paid">Paid</option>
                          <option value="overdue">Overdue</option>
                          <option value="cancelled">Cancelled</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Invoices List */}
                  <div className="bg-white rounded-lg shadow overflow-hidden mb-8">
                    {filteredInvoices.length === 0 ? (
                      <div className="text-center py-16">
                        <div className="text-gray-400 text-6xl mb-4">📄</div>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No invoices found</h3>
                        <p className="text-gray-600 mb-6">
                          {invoices.length === 0
                            ? "Create your first invoice to get started with billing."
                            : "Try adjusting your search or filter criteria."
                          }
                        </p>
                        {invoices.length === 0 && (
                          <button
                            onClick={() => setShowNewInvoiceForm(true)}
                            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 inline-flex items-center gap-2 font-medium"
                          >
                            <PlusIcon className="h-4 w-4" />
                            Create Invoice
                          </button>
                        )}
                      </div>
                    ) : (
                      <div className="overflow-x-auto pb-4">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Invoice
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Client
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Due Date
                              </th>
                              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {filteredInvoices.map((invoice) => (
                              <tr key={invoice.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">
                                      {invoice.invoice_number}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                      {project?.name}
                                    </div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {project?.client_name}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  <div className="flex items-center">
                                    <span>${invoice.amount.toFixed(2)} {invoice.currency}</span>
                                    {invoice.enableOnlinePayment && (
                                      <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800" title="Online payment enabled">
                                        💳
                                      </span>
                                    )}
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getInvoiceStatusColor(invoice.status)}`}>
                                    {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {invoice.due_date ? new Date(invoice.due_date).toLocaleDateString() : '-'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <div className="flex items-center justify-end gap-2">
                                    <Link
                                      href={`/invoices/${invoice.id}`}
                                      className="text-blue-600 hover:text-blue-900"
                                      title="View"
                                    >
                                      <EyeIcon className="h-4 w-4" />
                                    </Link>
                                    <Link
                                      href={`/invoices/${invoice.id}/edit`}
                                      className="text-gray-600 hover:text-gray-900"
                                      title="Edit"
                                    >
                                      <PencilIcon className="h-4 w-4" />
                                    </Link>

                                    {/* Payment Link */}
                                    {invoice.enableOnlinePayment && invoice.paymentUrl && (
                                      <a
                                        href={invoice.paymentUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-green-600 hover:text-green-900 text-xs px-2 py-1 bg-green-50 rounded"
                                        title="Client payment link"
                                      >
                                        Payment Link
                                      </a>
                                    )}

                                    {/* Status Actions */}
                                    {invoice.status === 'draft' && (
                                      <>
                                        <button
                                          onClick={() => {
                                            setInvoices(invoices.map(i =>
                                              i.id === invoice.id
                                                ? {...i, status: 'sent', sent_at: new Date().toISOString()}
                                                : i
                                            ));
                                            toast.success('Invoice marked as sent');
                                          }}
                                          className="text-blue-600 hover:text-blue-900 text-xs px-2 py-1 bg-blue-50 rounded"
                                          title="Mark as sent"
                                        >
                                          Send
                                        </button>
                                        <button
                                          onClick={() => {
                                            if (confirm('Are you sure you want to delete this invoice?')) {
                                              setInvoices(invoices.filter(i => i.id !== invoice.id));
                                              toast.success('Invoice deleted');
                                            }
                                          }}
                                          className="text-red-600 hover:text-red-900"
                                          title="Delete"
                                        >
                                          <TrashIcon className="h-4 w-4" />
                                        </button>
                                      </>
                                    )}

                                    {invoice.status === 'sent' && (
                                      <button
                                        onClick={() => {
                                          setInvoices(invoices.map(i =>
                                            i.id === invoice.id
                                              ? {...i, status: 'paid', paid_at: new Date().toISOString()}
                                              : i
                                          ));
                                          toast.success('Invoice marked as paid');
                                        }}
                                        className="text-green-600 hover:text-green-900 text-xs px-2 py-1 bg-green-50 rounded"
                                        title="Mark as paid"
                                      >
                                        Mark Paid
                                      </button>
                                    )}
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>

                  {/* New Invoice Form */}
                  {showNewInvoiceForm && (
                    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
                      <div className="bg-white rounded-lg p-6 w-full max-w-lg">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Create New Invoice</h3>
                        <form onSubmit={async (e) => {
                          e.preventDefault();
                          const formData = new FormData(e.target as HTMLFormElement);

                          try {
                            const token = localStorage.getItem('token');
                            const enableOnlinePayment = formData.get('enableOnlinePayment') === 'on';

                            console.log('Creating invoice with data:', {
                              projectId: projectId,
                              amount: parseFloat(formData.get('amount') as string),
                              currency: (formData.get('currency') as string).toLowerCase(),
                              enableOnlinePayment
                            });

                            // Create invoice via freelancer billing API
                            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3003';
                            console.log('API URL:', apiUrl);

                            const response = await fetch(`${apiUrl}/api/freelancer-billing/create-invoice`, {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                              },
                              body: JSON.stringify({
                                projectId: projectId,
                                clientId: project?.client_name, // In real app, this would be client ID
                                amount: parseFloat(formData.get('amount') as string),
                                currency: (formData.get('currency') as string).toLowerCase(),
                                description: formData.get('notes') as string || `Invoice for ${project?.name}`,
                                dueDate: formData.get('due_date') as string,
                                enableOnlinePayment: enableOnlinePayment,
                                items: [
                                  {
                                    description: `Services for ${project?.name}`,
                                    quantity: 1,
                                    rate: parseFloat(formData.get('amount') as string),
                                    amount: parseFloat(formData.get('amount') as string)
                                  }
                                ]
                              })
                            });

                            console.log('Response status:', response.status);
                            console.log('Response headers:', response.headers);

                            if (response.ok) {
                              const data = await response.json();
                              console.log('Invoice created successfully:', data);

                              const newInvoice: Invoice = {
                                id: data.data.id,
                                invoice_number: data.data.number,
                                amount: data.data.amount,
                                currency: data.data.currency,
                                status: data.data.status,
                                due_date: data.data.dueDate,
                                created_at: data.data.createdAt,
                                paymentUrl: data.data.paymentUrl,
                                enableOnlinePayment: data.data.enableOnlinePayment
                              };

                              setInvoices([...invoices, newInvoice]);
                              setShowNewInvoiceForm(false);

                              if (enableOnlinePayment && data.data.paymentUrl) {
                                toast.success('Invoice created with online payment enabled!');
                              } else {
                                toast.success('Invoice created successfully');
                              }
                            } else {
                              const errorData = await response.text();
                              console.error('API Error:', response.status, errorData);
                              throw new Error(`Failed to create invoice: ${response.status} - ${errorData}`);
                            }
                          } catch (error: any) {
                            console.error('Error creating invoice:', error);
                            toast.error(`Failed to create invoice: ${error.message || 'Unknown error'}`);
                          }
                        }}>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Invoice Number</label>
                              <input
                                type="text"
                                name="invoice_number"
                                defaultValue={`INV-${String(invoices.length + 1).padStart(3, '0')}`}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Amount</label>
                              <input
                                type="number"
                                name="amount"
                                step="0.01"
                                required
                                placeholder="0.00"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Currency</label>
                              <select
                                name="currency"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              >
                                <option value="USD">USD - US Dollar</option>
                                <option value="EUR">EUR - Euro</option>
                                <option value="GBP">GBP - British Pound</option>
                                <option value="CAD">CAD - Canadian Dollar</option>
                                <option value="AUD">AUD - Australian Dollar</option>
                              </select>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Due Date</label>
                              <input
                                type="date"
                                name="due_date"
                                required
                                min={new Date().toISOString().split('T')[0]}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                          </div>

                          <div className="mt-4">
                            <label className="block text-sm font-medium text-gray-700">Notes (Optional)</label>
                            <textarea
                              name="notes"
                              rows={3}
                              placeholder="Add any additional notes or payment terms..."
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            />
                          </div>

                          {/* Online Payment Toggle */}
                          {stripeStatus?.connected && (
                            <div className="mt-4">
                              <div className="flex items-center">
                                <input
                                  type="checkbox"
                                  name="enableOnlinePayment"
                                  id="enableOnlinePayment"
                                  defaultChecked={true}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="enableOnlinePayment" className="ml-2 block text-sm text-gray-900">
                                  Enable online payment for this invoice
                                </label>
                              </div>
                              <p className="mt-1 text-sm text-gray-500">
                                Clients can pay this invoice online with credit cards. Money goes directly to your account.
                              </p>
                            </div>
                          )}

                          <div className="flex justify-between items-center mt-6">
                            <div className="text-sm text-gray-500">
                              Invoice will be created for: <strong>{project?.client_name}</strong>
                            </div>
                            <div className="flex space-x-3">
                              <button
                                type="button"
                                onClick={() => setShowNewInvoiceForm(false)}
                                className="btn btn-secondary"
                              >
                                Cancel
                              </button>
                              <button type="submit" className="btn btn-primary">
                                Create Invoice
                              </button>
                            </div>
                          </div>
                        </form>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Receipts Tab */}
              {activeTab === 'receipts' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Payment Receipts</h2>
                    <button
                      onClick={() => setShowNewReceiptForm(true)}
                      className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2"
                    >
                      <ReceiptPercentIcon className="h-4 w-4" />
                      Generate Receipt
                    </button>
                  </div>

                  {/* Receipt Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div className="bg-white p-6 rounded-lg shadow">
                      <h3 className="text-sm font-medium text-gray-500">Total Receipts</h3>
                      <p className="text-2xl font-bold text-gray-900">{receipts.length}</p>
                    </div>
                    <div className="bg-white p-6 rounded-lg shadow">
                      <h3 className="text-sm font-medium text-gray-500">Total Received</h3>
                      <p className="text-2xl font-bold text-green-600">
                        ${receipts.reduce((sum, r) => sum + r.amount, 0).toFixed(2)}
                      </p>
                    </div>
                    <div className="bg-white p-6 rounded-lg shadow">
                      <h3 className="text-sm font-medium text-gray-500">This Month</h3>
                      <p className="text-2xl font-bold text-blue-600">
                        ${receipts.filter(r => new Date(r.payment_date).getMonth() === new Date().getMonth())
                          .reduce((sum, r) => sum + r.amount, 0).toFixed(2)}
                      </p>
                    </div>
                  </div>

                  {/* Receipts List */}
                  <div className="bg-white rounded-lg shadow overflow-hidden mb-8">
                    {receipts.length === 0 ? (
                      <div className="text-center py-16">
                        <ReceiptPercentIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No receipts yet</h3>
                        <p className="mt-1 text-sm text-gray-500">
                          Generate receipts when you receive payments from clients.
                        </p>
                        <button
                          onClick={() => setShowNewReceiptForm(true)}
                          className="mt-4 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 inline-flex items-center gap-2"
                        >
                          <ReceiptPercentIcon className="h-4 w-4" />
                          Generate First Receipt
                        </button>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Receipt
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Invoice
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Payment Method
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date
                              </th>
                              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {receipts.map((receipt) => (
                              <tr key={receipt.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">
                                      {receipt.receipt_number}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                      {receipt.transaction_id && `TXN: ${receipt.transaction_id}`}
                                    </div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {receipt.invoice_number}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  ${receipt.amount.toFixed(2)} {receipt.currency}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {receipt.payment_method}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {new Date(receipt.payment_date).toLocaleDateString()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <div className="flex items-center justify-end gap-2">
                                    <button
                                      onClick={() => downloadReceipt(receipt)}
                                      className="text-blue-600 hover:text-blue-900"
                                      title="Download Receipt"
                                    >
                                      <ArrowDownTrayIcon className="h-4 w-4" />
                                    </button>
                                    <button
                                      onClick={() => emailReceipt(receipt)}
                                      className="text-green-600 hover:text-green-900"
                                      title="Email Receipt"
                                    >
                                      <ShareIcon className="h-4 w-4" />
                                    </button>
                                    <button
                                      onClick={() => {
                                        if (confirm('Are you sure you want to delete this receipt?')) {
                                          setReceipts(receipts.filter(r => r.id !== receipt.id));
                                          toast.success('Receipt deleted');
                                        }
                                      }}
                                      className="text-red-600 hover:text-red-900"
                                      title="Delete Receipt"
                                    >
                                      <TrashIcon className="h-4 w-4" />
                                    </button>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Client Portal Tab */}
              {activeTab === 'client' && (
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-6">Client Portal</h2>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                    <div className="flex items-center">
                      <UserGroupIcon className="h-8 w-8 text-blue-600 mr-3" />
                      <div>
                        <h3 className="text-lg font-medium text-blue-900">Share Project with Client</h3>
                        <p className="text-sm text-blue-700">
                          Give your client access to view project progress, meetings, and documents.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Client Access Settings</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                          <div>
                            <h4 className="font-medium text-gray-900">View Meetings</h4>
                            <p className="text-sm text-gray-500">Allow client to view meeting summaries and recordings</p>
                          </div>
                          <input type="checkbox" defaultChecked className="h-4 w-4 text-primary-600" />
                        </div>
                        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                          <div>
                            <h4 className="font-medium text-gray-900">View Documents</h4>
                            <p className="text-sm text-gray-500">Allow client to download project documents</p>
                          </div>
                          <input type="checkbox" defaultChecked className="h-4 w-4 text-primary-600" />
                        </div>
                        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                          <div>
                            <h4 className="font-medium text-gray-900">View Tasks</h4>
                            <p className="text-sm text-gray-500">Allow client to see task progress</p>
                          </div>
                          <input type="checkbox" defaultChecked className="h-4 w-4 text-primary-600" />
                        </div>
                        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                          <div>
                            <h4 className="font-medium text-gray-900">View Invoices</h4>
                            <p className="text-sm text-gray-500">Allow client to view and pay invoices</p>
                          </div>
                          <input type="checkbox" className="h-4 w-4 text-primary-600" />
                        </div>
                        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                          <div>
                            <h4 className="font-medium text-gray-900">View Receipts</h4>
                            <p className="text-sm text-gray-500">Allow client to view payment receipts and download copies</p>
                          </div>
                          <input type="checkbox" defaultChecked className="h-4 w-4 text-primary-600" />
                        </div>
                      </div>
                    </div>

                    {/* Communication Settings */}
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium text-gray-900">Communication Settings</h3>
                        <button
                          onClick={() => setShowCommunicationSettings(!showCommunicationSettings)}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          {showCommunicationSettings ? 'Hide' : 'Configure'}
                        </button>
                      </div>

                      {showCommunicationSettings && (
                        <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Primary Communication Email
                            </label>
                            <input
                              type="email"
                              value={communicationEmail || project?.client_email || ''}
                              onChange={(e) => setCommunicationEmail(e.target.value)}
                              placeholder="<EMAIL>"
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                            <p className="text-xs text-gray-500 mt-1">
                              This email will be used for all one-click communications (meeting summaries, task updates, receipts, etc.)
                            </p>
                          </div>

                          <div className="flex items-center justify-between p-3 bg-white rounded border">
                            <div>
                              <h4 className="font-medium text-gray-900">Enable One-Click Email</h4>
                              <p className="text-sm text-gray-500">Allow quick email sending across all features</p>
                            </div>
                            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600" />
                          </div>

                          <div className="flex items-center justify-between p-3 bg-white rounded border">
                            <div>
                              <h4 className="font-medium text-gray-900">Auto-populate Client Email</h4>
                              <p className="text-sm text-gray-500">Automatically fill client email in all communication forms</p>
                            </div>
                            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600" />
                          </div>

                          <div className="flex items-center justify-between p-3 bg-white rounded border">
                            <div>
                              <h4 className="font-medium text-gray-900">Include Project Context</h4>
                              <p className="text-sm text-gray-500">Add project name and details to all emails</p>
                            </div>
                            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600" />
                          </div>

                          <div className="pt-3 border-t">
                            <button
                              onClick={() => {
                                // Save communication settings
                                toast.success('Communication settings saved');
                                setShowCommunicationSettings(false);
                              }}
                              className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                            >
                              Save Communication Settings
                            </button>
                          </div>
                        </div>
                      )}
                    </div>

                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Generate Client Link</h3>
                      <form onSubmit={(e) => {
                        e.preventDefault();
                        const formData = new FormData(e.target as HTMLFormElement);
                        const email = formData.get('email') as string;
                        if (email) {
                          toast.success(`Invitation sent to ${email}`);
                          (e.target as HTMLFormElement).reset();
                        }
                      }}>
                        <div className="flex space-x-3">
                          <input
                            type="email"
                            name="email"
                            placeholder="<EMAIL>"
                            required
                            className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          />
                          <button type="submit" className="btn btn-primary">
                            Send Invitation
                          </button>
                        </div>
                      </form>
                      <p className="text-sm text-gray-500 mt-2">
                        An email invitation will be sent to the client with secure access to this project.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Fallback for any remaining tabs */}
              {!['overview', 'meetings', 'tasks', 'ai-tasks', 'ai-docs', 'documents', 'invoices', 'receipts', 'client'].includes(activeTab) && (
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    {tabs.find(t => t.id === activeTab)?.name}
                  </h2>
                  <div className="text-center py-12">
                    <div className="text-gray-400">
                      {tabs.find(t => t.id === activeTab)?.name} feature coming soon...
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* New Meeting Form */}
      {showNewMeetingForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-start justify-center z-50 p-4 pt-8">
          <div className="bg-white rounded-lg w-full max-w-lg max-h-[90vh] flex flex-col shadow-xl">
            {/* Fixed Header */}
            <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                {meetingType === 'schedule' ? 'Schedule New Meeting' : 'Add Previous Meeting'}
              </h3>
            </div>
            <form onSubmit={async (e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);

              try {
                let audioUrl = null;
                let transcriptUrl = null;

                // Handle file uploads for previous meetings
                if (meetingType === 'previous') {
                  const audioFile = formData.get('audio') as File;
                  const transcriptFile = formData.get('transcript') as File;

                  if (audioFile && audioFile.size > 0) {
                    const audioFormData = new FormData();
                    audioFormData.append('file', audioFile);
                    audioFormData.append('projectId', projectId);
                    audioFormData.append('uploadType', 'audio');

                    const audioResponse = await fetch('/api/upload', {
                      method: 'POST',
                      body: audioFormData
                    });
                    const audioResult = await audioResponse.json();
                    if (audioResult.success) {
                      audioUrl = audioResult.data.file_url;
                    }
                  }

                  if (transcriptFile && transcriptFile.size > 0) {
                    const transcriptFormData = new FormData();
                    transcriptFormData.append('file', transcriptFile);
                    transcriptFormData.append('projectId', projectId);
                    transcriptFormData.append('uploadType', 'transcript');

                    const transcriptResponse = await fetch('/api/upload', {
                      method: 'POST',
                      body: transcriptFormData
                    });
                    const transcriptResult = await transcriptResponse.json();
                    if (transcriptResult.success) {
                      transcriptUrl = transcriptResult.data.file_url;
                    }
                  }
                }

                // Create meeting via API
                const meetingData = {
                  title: formData.get('title') as string,
                  platform: formData.get('platform') as string,
                  duration_minutes: meetingType === 'previous' ? parseInt(formData.get('duration') as string) || 0 : 0,
                  recorded_at: meetingType === 'schedule'
                    ? formData.get('scheduled_at') as string
                    : formData.get('meeting_date') as string,
                  transcription_status: meetingType === 'previous'
                    ? (audioUrl || transcriptUrl ? 'processing' : 'pending')
                    : 'scheduled',
                  audio_url: audioUrl,
                  transcript_url: transcriptUrl,
                  project_id: projectId
                };

                const response = await fetch('/api/meetings', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(meetingData)
                });

                const result = await response.json();

                if (result.success) {
                  // Refresh meetings list
                  fetchMeetings();
                  setShowNewMeetingForm(false);

                  if (meetingType === 'previous' && (audioUrl || transcriptUrl)) {
                    toast.success('Previous meeting added with files! AI will process for summary and action items.');

                    // Trigger AI processing in the background
                    fetch(`/api/meetings/${result.data.id}/process`, {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                      }
                    }).then(async (processResponse) => {
                      const processResult = await processResponse.json();
                      if (processResult.success) {
                        toast.success('AI processing completed! Meeting summary and action items are ready.');
                        // Refresh meetings to show updated data
                        fetchMeetings();
                      } else {
                        toast.error('AI processing failed, but meeting was saved successfully.');
                      }
                    }).catch((error) => {
                      console.error('AI processing error:', error);
                      toast.error('AI processing failed, but meeting was saved successfully.');
                    });
                  } else {
                    toast.success(meetingType === 'schedule' ? 'Meeting scheduled successfully' : 'Previous meeting added successfully');
                  }
                } else {
                  throw new Error(result.error || 'Failed to create meeting');
                }
              } catch (error) {
                console.error('Meeting creation error:', error);
                toast.error('Failed to create meeting');
              }
            }}>
              {/* Scrollable Content */}
              <div className="flex-1 overflow-y-auto px-6 py-4">
                <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Meeting Title</label>
                  <input
                    type="text"
                    name="title"
                    required
                    placeholder={meetingType === 'schedule' ? 'e.g., Weekly Project Review' : 'e.g., Client Kickoff Meeting'}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Platform/Location</label>
                  <select
                    name="platform"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="zoom">Zoom</option>
                    <option value="teams">Microsoft Teams</option>
                    <option value="meet">Google Meet</option>
                    <option value="webex">Cisco Webex</option>
                    <option value="in-person">In Person</option>
                    <option value="phone">Phone Call</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                {meetingType === 'schedule' ? (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Scheduled Date & Time</label>
                    <input
                      type="datetime-local"
                      name="scheduled_at"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    />
                  </div>
                ) : (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Meeting Date</label>
                        <input
                          type="date"
                          name="meeting_date"
                          required
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Duration (minutes)</label>
                        <input
                          type="number"
                          name="duration"
                          placeholder="60"
                          min="1"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Upload Audio Recording (Optional)</label>
                      <input
                        type="file"
                        name="audio"
                        accept=".mp3,.wav,.m4a,.mp4,.webm,.ogg"
                        className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Supported: MP3, WAV, M4A, MP4, WebM, OGG - AI will transcribe and create summary
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Upload Transcript (Optional)</label>
                      <input
                        type="file"
                        name="transcript"
                        accept=".txt,.doc,.docx,.pdf"
                        className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Supported: TXT, DOC, DOCX, PDF - AI will analyze and create professional summary
                      </p>
                    </div>


                  </>
                )}
                </div>
              </div>

              {/* Fixed Footer */}
              <div className="flex-shrink-0 px-6 py-4 border-t-2 border-gray-300 bg-white shadow-lg">
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowNewMeetingForm(false)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 bg-white rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 font-medium"
                  >
                    {meetingType === 'schedule' ? 'Schedule Meeting' : 'Add Meeting'}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Document Upload Form */}
      {showDocumentUpload && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-md max-h-[90vh] flex flex-col">
            {/* Fixed Header */}
            <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Upload Document</h3>
            </div>
            <form onSubmit={async (e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              const file = formData.get('file') as File;

              if (file) {
                try {
                  // Upload file to server
                  const uploadFormData = new FormData();
                  uploadFormData.append('file', file);
                  uploadFormData.append('projectId', projectId);
                  uploadFormData.append('uploadType', 'document');

                  const uploadResponse = await fetch('/api/upload', {
                    method: 'POST',
                    body: uploadFormData
                  });

                  const uploadResult = await uploadResponse.json();

                  if (uploadResult.success) {
                    const newDocument: Document = {
                      id: uploadResult.data.id,
                      name: uploadResult.data.name,
                      file_url: uploadResult.data.file_url,
                      file_type: uploadResult.data.file_type,
                      file_size: uploadResult.data.file_size,
                      uploaded_at: uploadResult.data.created_at,
                      uploaded_by: 'Current User'
                    };
                    setDocuments([...documents, newDocument]);
                    setShowDocumentUpload(false);
                    toast.success('Document uploaded successfully');
                  } else {
                    toast.error('Failed to upload document: ' + uploadResult.error);
                  }
                } catch (error) {
                  console.error('Upload error:', error);
                  toast.error('Failed to upload document');
                }
              }
            }}>
              {/* Scrollable Content */}
              <div className="flex-1 overflow-y-auto px-6 py-4">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Select File</label>
                    <input
                      type="file"
                      name="file"
                      required
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        const submitBtn = document.querySelector('#document-upload-submit') as HTMLButtonElement;
                        if (file && submitBtn) {
                          submitBtn.textContent = `Upload ${file.name}`;
                          submitBtn.disabled = false;
                        }
                      }}
                      className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Supported formats: PDF, Word, Excel, PowerPoint, Images, Text files
                    </p>
                  </div>
                </div>
              </div>

              {/* Fixed Footer */}
              <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowDocumentUpload(false)}
                    className="btn btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    id="document-upload-submit"
                    disabled
                    className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Select a file to upload
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* New Receipt Form */}
      {showNewReceiptForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Generate Payment Receipt</h3>
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              const newReceipt: Receipt = {
                id: Date.now().toString(),
                receipt_number: `RCP-${String(receipts.length + 1).padStart(3, '0')}`,
                invoice_id: formData.get('invoice_id') as string,
                invoice_number: formData.get('invoice_number') as string,
                amount: parseFloat(formData.get('amount') as string),
                currency: formData.get('currency') as string,
                payment_method: formData.get('payment_method') as string,
                payment_date: formData.get('payment_date') as string,
                transaction_id: formData.get('transaction_id') as string || undefined,
                notes: formData.get('notes') as string || undefined,
                created_at: new Date().toISOString()
              };
              setReceipts([...receipts, newReceipt]);
              setShowNewReceiptForm(false);
              toast.success('Receipt generated successfully');
            }}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Receipt Number</label>
                  <input
                    type="text"
                    name="receipt_number"
                    defaultValue={`RCP-${String(receipts.length + 1).padStart(3, '0')}`}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Invoice Number</label>
                  <select
                    name="invoice_number"
                    required
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    onChange={(e) => {
                      const selectedInvoice = invoices.find(inv => inv.invoice_number === e.target.value);
                      if (selectedInvoice) {
                        const amountInput = document.querySelector('input[name="amount"]') as HTMLInputElement;
                        const currencySelect = document.querySelector('select[name="currency"]') as HTMLSelectElement;
                        if (amountInput) amountInput.value = selectedInvoice.amount.toString();
                        if (currencySelect) currencySelect.value = selectedInvoice.currency;
                      }
                    }}
                  >
                    <option value="">Select Invoice</option>
                    {invoices.length === 0 ? (
                      <option value="" disabled>No invoices available</option>
                    ) : (
                      invoices.filter(inv => inv.status === 'paid' || inv.status === 'sent').length === 0 ? (
                        invoices.map(invoice => (
                          <option key={invoice.id} value={invoice.invoice_number}>
                            {invoice.invoice_number} - ${invoice.amount.toFixed(2)} ({invoice.status})
                          </option>
                        ))
                      ) : (
                        invoices.filter(inv => inv.status === 'paid' || inv.status === 'sent').map(invoice => (
                          <option key={invoice.id} value={invoice.invoice_number}>
                            {invoice.invoice_number} - ${invoice.amount.toFixed(2)}
                          </option>
                        ))
                      )
                    )}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Amount Received</label>
                  <input
                    type="number"
                    name="amount"
                    step="0.01"
                    required
                    placeholder="0.00"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Currency</label>
                  <select
                    name="currency"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                    <option value="CAD">CAD - Canadian Dollar</option>
                    <option value="AUD">AUD - Australian Dollar</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Payment Method</label>
                  <select
                    name="payment_method"
                    required
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="">Select Method</option>
                    <option value="Bank Transfer">Bank Transfer</option>
                    <option value="Credit Card">Credit Card</option>
                    <option value="PayPal">PayPal</option>
                    <option value="Stripe">Stripe</option>
                    <option value="Check">Check</option>
                    <option value="Cash">Cash</option>
                    <option value="Wire Transfer">Wire Transfer</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Payment Date</label>
                  <input
                    type="date"
                    name="payment_date"
                    required
                    defaultValue={new Date().toISOString().split('T')[0]}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700">Transaction ID (Optional)</label>
                <input
                  type="text"
                  name="transaction_id"
                  placeholder="e.g., TXN-ABC123456"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700">Notes (Optional)</label>
                <textarea
                  name="notes"
                  rows={3}
                  placeholder="Add any additional notes about the payment..."
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div className="flex justify-between items-center mt-6">
                <div className="text-sm text-gray-500">
                  Receipt will be generated for: <strong>{project?.client_name}</strong>
                </div>
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowNewReceiptForm(false)}
                    className="btn btn-secondary"
                  >
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-primary">
                    Generate Receipt
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Email Options Modal */}
      {showEmailOptionsModal && selectedReceiptForEmail && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Send Receipt via Email</h3>
            <p className="text-sm text-gray-600 mb-6">
              Choose your preferred email service to send receipt {selectedReceiptForEmail.receipt_number} to {project?.client_name}
            </p>

            <div className="space-y-3">
              {/* Gmail Option */}
              <button
                onClick={() => {
                  sendEmailViaGmail(selectedReceiptForEmail);
                  setShowEmailOptionsModal(false);
                  setSelectedReceiptForEmail(null);
                }}
                className="w-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">G</span>
                  </div>
                  <div className="text-left">
                    <h4 className="font-medium text-gray-900">Gmail</h4>
                    <p className="text-sm text-gray-500">Send via Google Gmail</p>
                  </div>
                </div>
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>

              {/* Outlook Option */}
              <button
                onClick={() => {
                  sendEmailViaOutlook(selectedReceiptForEmail);
                  setShowEmailOptionsModal(false);
                  setSelectedReceiptForEmail(null);
                }}
                className="w-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">O</span>
                  </div>
                  <div className="text-left">
                    <h4 className="font-medium text-gray-900">Outlook</h4>
                    <p className="text-sm text-gray-500">Send via Microsoft Outlook</p>
                  </div>
                </div>
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>

              {/* Default Email Client Option */}
              <button
                onClick={() => {
                  const subject = `Payment Receipt ${selectedReceiptForEmail.receipt_number} - ${project?.name}`;
                  const body = generateEmailBody(selectedReceiptForEmail);
                  const mailtoLink = `mailto:${project?.client_email || ''}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
                  window.open(mailtoLink);
                  toast.success('Default email client opened');
                  setShowEmailOptionsModal(false);
                  setSelectedReceiptForEmail(null);
                }}
                className="w-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center">
                    <svg className="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <h4 className="font-medium text-gray-900">Default Email</h4>
                    <p className="text-sm text-gray-500">Use system default email client</p>
                  </div>
                </div>
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowEmailOptionsModal(false);
                  setSelectedReceiptForEmail(null);
                }}
                className="btn btn-secondary"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
      </div>
    </DashboardLayout>
  );
}
