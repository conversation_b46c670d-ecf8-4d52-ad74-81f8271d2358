'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { LiveTranscription } from '@/components/meetings/LiveTranscription';
import {
  MicrophoneIcon,
  DocumentTextIcon,
  UserGroupIcon,
  ClockIcon,
  ShareIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface TranscriptionSegment {
  id: string;
  text: string;
  timestamp: number;
  confidence: number;
  speaker?: string;
  isFinal: boolean;
}

interface ActionItem {
  id: string;
  text: string;
  assignee?: string;
  dueDate?: string;
  completed: boolean;
}

export default function LiveMeetingPage() {
  const [meetingTitle, setMeetingTitle] = useState('');
  const [attendees, setAttendees] = useState<string[]>(['']);
  const [transcriptionSegments, setTranscriptionSegments] = useState<TranscriptionSegment[]>([]);
  const [actionItems, setActionItems] = useState<ActionItem[]>([]);
  const [meetingNotes, setMeetingNotes] = useState('');
  const [meetingStarted, setMeetingStarted] = useState(false);
  const [meetingId, setMeetingId] = useState<string | null>(null);

  // Initialize meeting
  const startMeeting = async () => {
    if (!meetingTitle.trim()) {
      toast.error('Please enter a meeting title');
      return;
    }

    try {
      const response = await fetch('/api/meetings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          title: meetingTitle,
          attendees: attendees.filter(email => email.trim()),
          platform: 'live',
          status: 'in_progress',
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setMeetingId(data.data.id);
        setMeetingStarted(true);
        toast.success('Meeting started successfully');
      } else {
        throw new Error('Failed to create meeting');
      }
    } catch (error) {
      console.error('Error starting meeting:', error);
      toast.error('Failed to start meeting');
    }
  };

  // Handle transcription updates
  const handleTranscriptionUpdate = (segments: TranscriptionSegment[]) => {
    setTranscriptionSegments(segments);
    
    // Auto-generate action items from transcription
    generateActionItems(segments);
  };

  // Generate action items from transcription
  const generateActionItems = async (segments: TranscriptionSegment[]) => {
    const recentText = segments
      .slice(-5) // Last 5 segments
      .map(s => s.text)
      .join(' ');

    // Simple action item detection (in production, use AI)
    const actionKeywords = ['action item', 'todo', 'follow up', 'will do', 'need to', 'should'];
    const hasActionKeyword = actionKeywords.some(keyword => 
      recentText.toLowerCase().includes(keyword)
    );

    if (hasActionKeyword && recentText.length > 20) {
      const newActionItem: ActionItem = {
        id: Date.now().toString(),
        text: recentText,
        completed: false,
      };

      setActionItems(prev => {
        // Avoid duplicates
        const exists = prev.some(item => 
          item.text.toLowerCase().includes(recentText.toLowerCase().substring(0, 30))
        );
        return exists ? prev : [...prev, newActionItem];
      });
    }
  };

  // Add attendee
  const addAttendee = () => {
    setAttendees([...attendees, '']);
  };

  // Update attendee
  const updateAttendee = (index: number, value: string) => {
    const updated = [...attendees];
    updated[index] = value;
    setAttendees(updated);
  };

  // Remove attendee
  const removeAttendee = (index: number) => {
    setAttendees(attendees.filter((_, i) => i !== index));
  };

  // Toggle action item completion
  const toggleActionItem = (id: string) => {
    setActionItems(prev =>
      prev.map(item =>
        item.id === id ? { ...item, completed: !item.completed } : item
      )
    );
  };

  // End meeting
  const endMeeting = async () => {
    if (!meetingId) return;

    try {
      const response = await fetch(`/api/meetings/${meetingId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          status: 'completed',
          transcript: transcriptionSegments.map(s => s.text).join(' '),
          action_items: actionItems,
          notes: meetingNotes,
        }),
      });

      if (response.ok) {
        toast.success('Meeting ended and saved');
        setMeetingStarted(false);
      } else {
        throw new Error('Failed to end meeting');
      }
    } catch (error) {
      console.error('Error ending meeting:', error);
      toast.error('Failed to end meeting');
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Live Meeting</h1>
              <p className="text-gray-600">Record and transcribe meetings in real-time</p>
            </div>
            {meetingStarted && (
              <button
                onClick={endMeeting}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                End Meeting
              </button>
            )}
          </div>
        </div>

        {!meetingStarted ? (
          /* Meeting Setup */
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Start New Meeting</h2>
              
              <div className="space-y-6">
                {/* Meeting Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Meeting Title
                  </label>
                  <input
                    type="text"
                    value={meetingTitle}
                    onChange={(e) => setMeetingTitle(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter meeting title..."
                  />
                </div>

                {/* Attendees */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Attendees
                  </label>
                  <div className="space-y-2">
                    {attendees.map((attendee, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <input
                          type="email"
                          value={attendee}
                          onChange={(e) => updateAttendee(index, e.target.value)}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                          placeholder="<EMAIL>"
                        />
                        {attendees.length > 1 && (
                          <button
                            onClick={() => removeAttendee(index)}
                            className="px-3 py-2 text-red-600 hover:text-red-800"
                          >
                            Remove
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      onClick={addAttendee}
                      className="text-primary-600 hover:text-primary-800 text-sm"
                    >
                      + Add Attendee
                    </button>
                  </div>
                </div>

                {/* Start Button */}
                <button
                  onClick={startMeeting}
                  className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
                >
                  <MicrophoneIcon className="h-5 w-5" />
                  <span>Start Meeting</span>
                </button>
              </div>
            </div>
          </div>
        ) : (
          /* Live Meeting Interface */
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Live Transcription */}
            <div className="lg:col-span-2">
              <LiveTranscription
                meetingId={meetingId || undefined}
                onTranscriptionUpdate={handleTranscriptionUpdate}
                autoSave={true}
              />
            </div>

            {/* Meeting Sidebar */}
            <div className="space-y-6">
              {/* Meeting Info */}
              <div className="bg-white rounded-lg shadow p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Meeting Info</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <DocumentTextIcon className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">{meetingTitle}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ClockIcon className="h-4 w-4 text-gray-500" />
                    <span>Started at {new Date().toLocaleTimeString()}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <UserGroupIcon className="h-4 w-4 text-gray-500" />
                    <span>{attendees.filter(a => a.trim()).length} attendees</span>
                  </div>
                </div>
              </div>

              {/* Action Items */}
              <div className="bg-white rounded-lg shadow p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Action Items</h3>
                <div className="space-y-2">
                  {actionItems.length === 0 ? (
                    <p className="text-gray-500 text-sm">No action items detected yet</p>
                  ) : (
                    actionItems.map((item) => (
                      <div key={item.id} className="flex items-start space-x-2">
                        <button
                          onClick={() => toggleActionItem(item.id)}
                          className={`mt-1 h-4 w-4 rounded border-2 flex items-center justify-center ${
                            item.completed
                              ? 'bg-green-500 border-green-500'
                              : 'border-gray-300 hover:border-green-500'
                          }`}
                        >
                          {item.completed && (
                            <CheckCircleIcon className="h-3 w-3 text-white" />
                          )}
                        </button>
                        <span
                          className={`text-sm ${
                            item.completed ? 'line-through text-gray-500' : 'text-gray-900'
                          }`}
                        >
                          {item.text.substring(0, 100)}...
                        </span>
                      </div>
                    ))
                  )}
                </div>
              </div>

              {/* Meeting Notes */}
              <div className="bg-white rounded-lg shadow p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Notes</h3>
                <textarea
                  value={meetingNotes}
                  onChange={(e) => setMeetingNotes(e.target.value)}
                  className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500 text-sm"
                  placeholder="Add your meeting notes..."
                />
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h3>
                <div className="space-y-2">
                  <button className="w-full flex items-center space-x-2 px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 rounded">
                    <ShareIcon className="h-4 w-4" />
                    <span>Share Meeting Link</span>
                  </button>
                  <button className="w-full flex items-center space-x-2 px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 rounded">
                    <DocumentTextIcon className="h-4 w-4" />
                    <span>Export Transcript</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
