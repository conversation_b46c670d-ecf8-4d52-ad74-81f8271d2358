import Stripe from 'stripe';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Stripe with secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_...', {
  apiVersion: '2025-05-28.basil',
});

// Stripe Connect configuration for freelancer accounts
export const stripeConnectConfig = {
  publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || 'pk_test_...',
  secretKey: process.env.STRIPE_SECRET_KEY || 'sk_test_...',
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || 'whsec_...',
  clientId: process.env.STRIPE_CLIENT_ID || 'ca_...',
  redirectUri: process.env.FRONTEND_URL + '/integrations/stripe/callback',
};

// KaiNote subscription plans (optional for freelancers)
export const kaiNoteSubscriptionPlans = {
  free: {
    name: 'Free',
    priceId: null,
    price: 0,
    currency: 'usd',
    interval: 'month',
    features: [
      '5 meetings per month',
      'Basic transcription',
      'Action item extraction',
      'Email reminders',
      'Client summaries',
      'Basic invoicing'
    ],
    limits: {
      meetings: 5,
      clients: 3,
      storage: '1GB'
    }
  },
  professional: {
    name: 'Professional',
    priceId: process.env.STRIPE_KAINOTE_PRO_PRICE_ID || 'price_professional',
    price: 1900, // $19.00 in cents
    currency: 'usd',
    interval: 'month',
    features: [
      'Unlimited meetings',
      'Meeting Bot automation',
      'Smart scheduling',
      'Financial dashboard',
      'Advanced invoicing',
      'Client management',
      'Time tracking',
      'AI document generation',
      'Stripe Connect integration'
    ],
    limits: {
      meetings: -1, // unlimited
      clients: -1,
      storage: '100GB'
    }
  },
  business: {
    name: 'Business',
    priceId: process.env.STRIPE_KAINOTE_BIZ_PRICE_ID || 'price_business',
    price: 3900, // $39.00 in cents
    currency: 'usd',
    interval: 'month',
    features: [
      'Everything in Professional',
      'White-label client portals',
      'Advanced automation',
      'Custom integrations',
      'Priority support',
      'Advanced analytics',
      'API access',
      'Multi-currency invoicing'
    ],
    limits: {
      meetings: -1,
      clients: -1,
      storage: '1TB'
    }
  }
};

// Stripe Connect helper functions
export const stripeConnect = {
  // Create Express account for freelancer
  async createExpressAccount(freelancerData: {
    email: string;
    country: string;
    businessType?: string;
  }) {
    try {
      const account = await stripe.accounts.create({
        type: 'express',
        country: freelancerData.country,
        email: freelancerData.email,
        business_type: freelancerData.businessType as any || 'individual',
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
        },
        settings: {
          payouts: {
            schedule: {
              interval: 'daily',
            },
          },
        },
      });
      return account;
    } catch (error) {
      console.error('Error creating Stripe Express account:', error);
      throw error;
    }
  },

  // Create account link for onboarding
  async createAccountLink(accountId: string, refreshUrl: string, returnUrl: string) {
    try {
      const accountLink = await stripe.accountLinks.create({
        account: accountId,
        refresh_url: refreshUrl,
        return_url: returnUrl,
        type: 'account_onboarding',
      });
      return accountLink;
    } catch (error) {
      console.error('Error creating account link:', error);
      throw error;
    }
  },

  // Get account details
  async getAccount(accountId: string) {
    try {
      const account = await stripe.accounts.retrieve(accountId);
      return account;
    } catch (error) {
      console.error('Error retrieving account:', error);
      throw error;
    }
  },

  // Create payment intent for client invoice
  async createPaymentIntent(
    amount: number,
    currency: string,
    connectedAccountId: string,
    applicationFeeAmount: number = 0,
    metadata: any = {}
  ) {
    try {
      const paymentIntent = await stripe.paymentIntents.create({
        amount,
        currency,
        application_fee_amount: applicationFeeAmount,
        transfer_data: {
          destination: connectedAccountId,
        },
        metadata,
      });
      return paymentIntent;
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw error;
    }
  },

  // Create checkout session for client payment
  async createCheckoutSession(
    amount: number,
    currency: string,
    connectedAccountId: string,
    successUrl: string,
    cancelUrl: string,
    applicationFeeAmount: number = 0,
    metadata: any = {}
  ) {
    try {
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price_data: {
              currency,
              product_data: {
                name: metadata.invoiceNumber || 'Invoice Payment',
                description: metadata.description || 'Payment for services',
              },
              unit_amount: amount,
            },
            quantity: 1,
          },
        ],
        mode: 'payment',
        success_url: successUrl,
        cancel_url: cancelUrl,
        payment_intent_data: {
          application_fee_amount: applicationFeeAmount,
          transfer_data: {
            destination: connectedAccountId,
          },
          metadata,
        },
      });
      return session;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw error;
    }
  },

  // Get dashboard link for connected account
  async createDashboardLink(accountId: string) {
    try {
      const link = await stripe.accounts.createLoginLink(accountId);
      return link;
    } catch (error) {
      console.error('Error creating dashboard link:', error);
      throw error;
    }
  }
};

export default stripe;
