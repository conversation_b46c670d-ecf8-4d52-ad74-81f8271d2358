'use client';

import { XCircleIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

export default function BillingCancelPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
            <XCircleIcon className="h-8 w-8 text-red-600" />
          </div>
          
          <h1 className="text-3xl font-extrabold text-gray-900 mb-2">
            Subscription Cancelled
          </h1>
          
          <p className="text-gray-600">
            Your subscription process was cancelled. No charges were made to your account.
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">What happened?</h2>
          
          <p className="text-gray-600 mb-4">
            You cancelled the subscription process before completing payment. This is completely normal and no charges were made.
          </p>
          
          <p className="text-gray-600">
            You can still access KaiNote with our free plan, which includes basic features to get you started.
          </p>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">Free Plan Includes:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• 5 meetings per month</li>
            <li>• Basic transcription</li>
            <li>• Action item extraction</li>
            <li>• Email reminders</li>
            <li>• Client summaries</li>
          </ul>
        </div>

        <div className="space-y-3">
          <Link
            href="/pricing"
            className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            View Plans Again
          </Link>
          
          <Link
            href="/dashboard"
            className="w-full flex items-center justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Continue with Free Plan
          </Link>
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-500">
            Questions about our plans?{' '}
            <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-500">
              Contact our sales team
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
