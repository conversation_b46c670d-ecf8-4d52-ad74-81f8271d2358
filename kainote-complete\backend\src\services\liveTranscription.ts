import WebSocket from 'ws';
import { Server } from 'http';
import OpenAI from 'openai';
import { logger } from '../utils/logger';

interface TranscriptionClient {
  ws: WebSocket;
  userId: string;
  meetingId?: string;
  audioBuffer: Buffer[];
  lastProcessed: number;
}

export class LiveTranscriptionService {
  private wss: WebSocket.Server;
  private clients: Map<string, TranscriptionClient> = new Map();
  private openai: OpenAI;
  private processingInterval: NodeJS.Timeout;

  constructor(server: Server) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    // Create WebSocket server
    this.wss = new WebSocket.Server({
      server,
      path: '/ws/transcription',
    });

    this.setupWebSocketHandlers();
    this.startProcessingLoop();

    logger.info('Live transcription service initialized');
  }

  private setupWebSocketHandlers() {
    this.wss.on('connection', (ws: WebSocket, request) => {
      const clientId = this.generateClientId();
      
      // Extract user info from query params or headers
      const url = new URL(request.url!, `http://${request.headers.host}`);
      const userId = url.searchParams.get('userId') || 'anonymous';
      const meetingId = url.searchParams.get('meetingId') || undefined;

      const client: TranscriptionClient = {
        ws,
        userId,
        meetingId,
        audioBuffer: [],
        lastProcessed: Date.now(),
      };

      this.clients.set(clientId, client);

      logger.info(`Live transcription client connected: ${clientId}`, {
        userId,
        meetingId,
        totalClients: this.clients.size,
      });

      // Send connection confirmation
      this.sendMessage(ws, {
        type: 'connected',
        clientId,
        message: 'Live transcription ready',
      });

      // Handle incoming audio data
      ws.on('message', async (data: Buffer) => {
        try {
          if (data.length > 0) {
            client.audioBuffer.push(data);
            client.lastProcessed = Date.now();
          }
        } catch (error) {
          logger.error('Error handling audio data:', error);
          this.sendMessage(ws, {
            type: 'error',
            message: 'Failed to process audio data',
          });
        }
      });

      // Handle client disconnect
      ws.on('close', () => {
        this.clients.delete(clientId);
        logger.info(`Live transcription client disconnected: ${clientId}`, {
          remainingClients: this.clients.size,
        });
      });

      // Handle errors
      ws.on('error', (error) => {
        logger.error(`WebSocket error for client ${clientId}:`, error);
        this.clients.delete(clientId);
      });
    });
  }

  private startProcessingLoop() {
    // Process audio buffers every 2 seconds
    this.processingInterval = setInterval(() => {
      this.processAudioBuffers();
    }, 2000);
  }

  private async processAudioBuffers() {
    const now = Date.now();
    
    for (const [clientId, client] of this.clients.entries()) {
      try {
        // Process if we have audio data and it's been at least 1 second since last processing
        if (client.audioBuffer.length > 0 && (now - client.lastProcessed) >= 1000) {
          await this.transcribeAudioBuffer(clientId, client);
        }
      } catch (error) {
        logger.error(`Error processing audio for client ${clientId}:`, error);
        this.sendMessage(client.ws, {
          type: 'error',
          message: 'Transcription processing error',
        });
      }
    }
  }

  private async transcribeAudioBuffer(clientId: string, client: TranscriptionClient) {
    if (client.audioBuffer.length === 0) return;

    try {
      // Combine audio chunks into a single buffer
      const combinedBuffer = Buffer.concat(client.audioBuffer);
      
      // Clear the buffer
      client.audioBuffer = [];

      // Convert to audio file format for Whisper API
      const audioFile = await this.convertToAudioFile(combinedBuffer);

      // Call Whisper API for transcription
      const transcription = await this.openai.audio.transcriptions.create({
        file: audioFile,
        model: 'whisper-1',
        language: 'en',
        response_format: 'verbose_json',
        timestamp_granularities: ['word'],
      });

      // Process transcription results
      if (transcription.text && transcription.text.trim().length > 0) {
        const segments = this.processTranscriptionSegments(transcription);
        
        // Send transcription results to client
        for (const segment of segments) {
          this.sendMessage(client.ws, {
            type: 'transcription',
            id: this.generateSegmentId(),
            text: segment.text,
            timestamp: Date.now(),
            confidence: segment.confidence || 0.8,
            is_final: true,
            speaker: segment.speaker,
          });
        }

        logger.info(`Transcription sent to client ${clientId}:`, {
          text: transcription.text.substring(0, 100),
          segments: segments.length,
        });
      }

    } catch (error) {
      logger.error(`Transcription error for client ${clientId}:`, error);
      
      // Send error to client
      this.sendMessage(client.ws, {
        type: 'error',
        message: 'Transcription failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  private async convertToAudioFile(buffer: Buffer): Promise<File> {
    // Create a File object from the buffer for Whisper API
    const blob = new Blob([buffer], { type: 'audio/webm' });
    return new File([blob], 'audio.webm', { type: 'audio/webm' });
  }

  private processTranscriptionSegments(transcription: any): Array<{
    text: string;
    confidence?: number;
    speaker?: string;
  }> {
    const segments = [];

    if (transcription.segments) {
      // Process word-level segments if available
      for (const segment of transcription.segments) {
        segments.push({
          text: segment.text.trim(),
          confidence: segment.avg_logprob ? Math.exp(segment.avg_logprob) : 0.8,
          speaker: this.detectSpeaker(segment),
        });
      }
    } else {
      // Fallback to full text
      segments.push({
        text: transcription.text.trim(),
        confidence: 0.8,
      });
    }

    return segments.filter(seg => seg.text.length > 0);
  }

  private detectSpeaker(segment: any): string | undefined {
    // Simple speaker detection based on audio characteristics
    // In a real implementation, you might use speaker diarization
    return undefined; // For now, no speaker detection
  }

  private sendMessage(ws: WebSocket, message: any) {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        logger.error('Error sending WebSocket message:', error);
      }
    }
  }

  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSegmentId(): string {
    return `segment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Cleanup method
  public cleanup() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }

    // Close all client connections
    for (const [clientId, client] of this.clients.entries()) {
      client.ws.close();
    }

    this.clients.clear();
    this.wss.close();

    logger.info('Live transcription service cleaned up');
  }

  // Get service statistics
  public getStats() {
    return {
      connectedClients: this.clients.size,
      totalProcessed: 0, // You could track this
      uptime: process.uptime(),
    };
  }
}

// Export singleton instance
let liveTranscriptionService: LiveTranscriptionService | null = null;

export const initializeLiveTranscription = (server: Server) => {
  if (!liveTranscriptionService) {
    liveTranscriptionService = new LiveTranscriptionService(server);
  }
  return liveTranscriptionService;
};

export const getLiveTranscriptionService = () => {
  return liveTranscriptionService;
};
