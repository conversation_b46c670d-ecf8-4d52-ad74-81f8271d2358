import express, { Request, Response, NextFunction } from 'express';

const router = express.Router();

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    userId: string;
    email: string;
    name: string;
    role: 'freelancer' | 'client';
  };
}

// Demo auth middleware
const demoAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  const isClientToken = authHeader?.includes('client-token');
  
  req.user = isClientToken ? {
    id: 'demo-client-id',
    userId: 'demo-client-id',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'client'
  } : {
    id: 'demo-user-id',
    userId: 'demo-user-id',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'freelancer'
  };
  next();
};

// In-memory storage for demo (use database in production)
let messages: any[] = [
  {
    id: 'msg_1',
    projectId: 'demo-project-1',
    senderId: 'demo-user-id',
    senderName: '<PERSON>',
    senderRole: 'freelancer',
    recipientId: 'demo-client-id',
    recipientName: '<PERSON>',
    recipientRole: 'client',
    content: 'Hi <PERSON>! I wanted to update you on the project progress. The e-commerce platform is coming along great!',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    isRead: true,
    messageType: 'text'
  },
  {
    id: 'msg_2',
    projectId: 'demo-project-1',
    senderId: 'demo-client-id',
    senderName: 'Sarah Chen',
    senderRole: 'client',
    recipientId: 'demo-user-id',
    recipientName: 'Alex Johnson',
    recipientRole: 'freelancer',
    content: 'That\'s fantastic to hear! Could you share a preview of the current design? I\'m excited to see how it\'s shaping up.',
    timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString(), // 1.5 hours ago
    isRead: true,
    messageType: 'text'
  },
  {
    id: 'msg_3',
    projectId: 'demo-project-1',
    senderId: 'demo-user-id',
    senderName: 'Alex Johnson',
    senderRole: 'freelancer',
    recipientId: 'demo-client-id',
    recipientName: 'Sarah Chen',
    recipientRole: 'client',
    content: 'Absolutely! I\'ll send you a staging link shortly. The new checkout flow is much more intuitive now.',
    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
    isRead: false,
    messageType: 'text'
  }
];

let userStatus: any = {
  'demo-user-id': {
    isOnline: true,
    lastSeen: new Date().toISOString(),
    status: 'online' // online, away, busy, offline
  },
  'demo-client-id': {
    isOnline: false,
    lastSeen: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago
    status: 'away'
  }
};

// Get messages for a project
router.get('/project/:projectId/messages', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { projectId } = req.params;
    const user = req.user!;

    // Filter messages for this project
    const projectMessages = messages
      .filter(msg => msg.projectId === projectId)
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    // Mark messages as read when fetched
    messages = messages.map(msg => {
      if (msg.projectId === projectId && msg.recipientId === user.id) {
        return { ...msg, isRead: true };
      }
      return msg;
    });

    res.json({
      success: true,
      data: projectMessages
    });

  } catch (error: any) {
    console.error('Error getting messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get messages',
      details: error.message
    });
  }
});

// Send a message
router.post('/project/:projectId/messages', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { projectId } = req.params;
    const { content, messageType = 'text' } = req.body;
    const user = req.user!;

    if (!content || !content.trim()) {
      return res.status(400).json({
        success: false,
        error: 'Message content is required'
      });
    }

    // Determine recipient (other party in the project)
    const recipientRole = user.role === 'freelancer' ? 'client' : 'freelancer';
    const recipientId = user.role === 'freelancer' ? 'demo-client-id' : 'demo-user-id';
    const recipientName = user.role === 'freelancer' ? 'Sarah Chen' : 'Alex Johnson';

    const newMessage = {
      id: `msg_${Date.now()}`,
      projectId,
      senderId: user.id,
      senderName: user.name,
      senderRole: user.role,
      recipientId,
      recipientName,
      recipientRole,
      content: content.trim(),
      timestamp: new Date().toISOString(),
      isRead: false,
      messageType
    };

    messages.push(newMessage);

    // In real app, send real-time notification via WebSocket
    console.log('New message sent:', newMessage);

    res.json({
      success: true,
      data: newMessage
    });

  } catch (error: any) {
    console.error('Error sending message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send message',
      details: error.message
    });
  }
});

// Get user online status
router.get('/project/:projectId/participants/status', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { projectId } = req.params;

    // Get status for all project participants
    const participantStatus = {
      freelancer: {
        id: 'demo-user-id',
        name: 'Alex Johnson',
        role: 'freelancer',
        ...userStatus['demo-user-id']
      },
      client: {
        id: 'demo-client-id',
        name: 'Sarah Chen',
        role: 'client',
        ...userStatus['demo-client-id']
      }
    };

    res.json({
      success: true,
      data: participantStatus
    });

  } catch (error: any) {
    console.error('Error getting participant status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get participant status',
      details: error.message
    });
  }
});

// Update user status
router.post('/user/status', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { status } = req.body; // online, away, busy, offline
    const user = req.user!;

    if (!['online', 'away', 'busy', 'offline'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status. Must be: online, away, busy, or offline'
      });
    }

    userStatus[user.id] = {
      isOnline: status === 'online',
      lastSeen: new Date().toISOString(),
      status
    };

    res.json({
      success: true,
      data: userStatus[user.id]
    });

  } catch (error: any) {
    console.error('Error updating user status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update status',
      details: error.message
    });
  }
});

// Mark messages as read
router.post('/project/:projectId/messages/mark-read', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { projectId } = req.params;
    const user = req.user!;

    // Mark all messages in this project as read for current user
    messages = messages.map(msg => {
      if (msg.projectId === projectId && msg.recipientId === user.id) {
        return { ...msg, isRead: true };
      }
      return msg;
    });

    res.json({
      success: true,
      message: 'Messages marked as read'
    });

  } catch (error: any) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to mark messages as read',
      details: error.message
    });
  }
});

// Get unread message count for user
router.get('/user/unread-count', demoAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;

    const unreadCount = messages.filter(msg => 
      msg.recipientId === user.id && !msg.isRead
    ).length;

    res.json({
      success: true,
      data: { unreadCount }
    });

  } catch (error: any) {
    console.error('Error getting unread count:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get unread count',
      details: error.message
    });
  }
});

export default router;
