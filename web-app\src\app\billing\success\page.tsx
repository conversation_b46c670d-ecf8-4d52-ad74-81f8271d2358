'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { CheckCircleIcon, SparklesIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

export default function BillingSuccessPage() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const [loading, setLoading] = useState(true);
  const [subscriptionData, setSubscriptionData] = useState<any>(null);

  useEffect(() => {
    // In a real app, you would verify the session with your backend
    // For demo purposes, we'll simulate success
    setTimeout(() => {
      setSubscriptionData({
        planName: 'Professional',
        amount: '$19.00',
        nextBilling: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()
      });
      setLoading(false);
    }, 2000);
  }, [sessionId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Processing your subscription...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
            <CheckCircleIcon className="h-8 w-8 text-green-600" />
          </div>
          
          <h1 className="text-3xl font-extrabold text-gray-900 mb-2">
            Welcome to KaiNote!
          </h1>
          
          <div className="flex items-center justify-center mb-4">
            <SparklesIcon className="h-5 w-5 text-primary-600 mr-2" />
            <span className="text-lg font-semibold text-primary-600">
              Subscription Activated
            </span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Subscription Details</h2>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Plan:</span>
              <span className="font-medium text-gray-900">{subscriptionData?.planName}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Amount:</span>
              <span className="font-medium text-gray-900">{subscriptionData?.amount}/month</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Next billing:</span>
              <span className="font-medium text-gray-900">{subscriptionData?.nextBilling}</span>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">What's Next?</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Access all premium features immediately</li>
            <li>• Set up your first meeting bot</li>
            <li>• Configure automation rules</li>
            <li>• Explore the financial dashboard</li>
          </ul>
        </div>

        <div className="space-y-3">
          <Link
            href="/dashboard"
            className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Go to Dashboard
          </Link>
          
          <Link
            href="/billing"
            className="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Manage Billing
          </Link>
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-500">
            Need help getting started?{' '}
            <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-500">
              Contact our support team
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
