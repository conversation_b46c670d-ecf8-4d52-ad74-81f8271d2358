import { supabaseAdmin } from '../config/supabase';
import { addDays, addHours, isBefore, isAfter } from 'date-fns';

export interface AutomationRule {
  id: string;
  user_id: string;
  project_id?: string;
  rule_name: string;
  rule_type: 'task_completion' | 'deadline_approaching' | 'milestone_reached' | 'project_status_change' | 'client_communication';
  trigger_conditions: any;
  actions: any[];
  client_communication_config: any;
  is_active: boolean;
  priority: number;
}

export interface AutomationExecution {
  id: string;
  rule_id: string;
  user_id: string;
  trigger_data: any;
  execution_status: 'pending' | 'running' | 'completed' | 'failed';
  result_data?: any;
  error_message?: string;
}

export class AutomationRulesService {

  /**
   * Create a new automation rule
   */
  async createAutomationRule(ruleData: Partial<AutomationRule>): Promise<AutomationRule> {
    const { data, error } = await supabaseAdmin
      .from('smart_automation_rules')
      .insert(ruleData)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  /**
   * Get automation rules for a user or project
   */
  async getAutomationRules(userId: string, projectId?: string): Promise<AutomationRule[]> {
    let query = supabaseAdmin
      .from('smart_automation_rules')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true);

    if (projectId) {
      query = query.eq('project_id', projectId);
    }

    const { data, error } = await query.order('priority', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  /**
   * Execute automation rules based on trigger events
   */
  async executeAutomationRules(
    triggerType: string,
    triggerData: any,
    userId: string,
    projectId?: string
  ): Promise<AutomationExecution[]> {
    try {
      // Get applicable automation rules
      const rules = await this.getAutomationRules(userId, projectId);
      const applicableRules = rules.filter(rule => 
        rule.rule_type === triggerType && this.evaluateTriggerConditions(rule, triggerData)
      );

      const executions: AutomationExecution[] = [];

      // Execute each applicable rule
      for (const rule of applicableRules) {
        const execution = await this.executeRule(rule, triggerData);
        executions.push(execution);
      }

      return executions;

    } catch (error) {
      console.error('Error executing automation rules:', error);
      throw error;
    }
  }

  /**
   * Execute a single automation rule
   */
  private async executeRule(rule: AutomationRule, triggerData: any): Promise<AutomationExecution> {
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Log execution start
      const execution = await this.logExecutionStart(executionId, rule, triggerData);

      // Execute all actions in the rule
      const results = [];
      for (const action of rule.actions) {
        const result = await this.executeAction(action, triggerData, rule);
        results.push(result);
      }

      // Update execution as completed
      await this.logExecutionComplete(executionId, { results });
      
      // Update rule execution count
      await this.updateRuleExecutionCount(rule.id);

      return {
        ...execution,
        execution_status: 'completed',
        result_data: { results }
      };

    } catch (error) {
      // Log execution failure
      await this.logExecutionFailure(executionId, error.message);
      
      return {
        id: executionId,
        rule_id: rule.id,
        user_id: rule.user_id,
        trigger_data: triggerData,
        execution_status: 'failed',
        error_message: error.message
      };
    }
  }

  /**
   * Execute a specific action
   */
  private async executeAction(action: any, triggerData: any, rule: AutomationRule): Promise<any> {
    switch (action.type) {
      case 'create_task':
        return await this.createTaskAction(action, triggerData, rule);
      
      case 'update_task_status':
        return await this.updateTaskStatusAction(action, triggerData, rule);
      
      case 'send_client_email':
        return await this.sendClientEmailAction(action, triggerData, rule);
      
      case 'create_milestone':
        return await this.createMilestoneAction(action, triggerData, rule);
      
      case 'schedule_followup':
        return await this.scheduleFollowupAction(action, triggerData, rule);
      
      case 'update_project_status':
        return await this.updateProjectStatusAction(action, triggerData, rule);
      
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  /**
   * Create task action
   */
  private async createTaskAction(action: any, triggerData: any, rule: AutomationRule): Promise<any> {
    const taskData = {
      project_id: rule.project_id || triggerData.project_id,
      user_id: rule.user_id,
      title: this.interpolateTemplate(action.title, triggerData),
      description: this.interpolateTemplate(action.description, triggerData),
      status: action.status || 'todo',
      priority: action.priority || 'medium',
      due_date: action.due_date ? this.calculateDueDate(action.due_date, triggerData) : null
    };

    const { data, error } = await supabaseAdmin
      .from('project_tasks')
      .insert(taskData)
      .select()
      .single();

    if (error) throw error;
    return { type: 'task_created', task: data };
  }

  /**
   * Update task status action
   */
  private async updateTaskStatusAction(action: any, triggerData: any, rule: AutomationRule): Promise<any> {
    const taskId = triggerData.task_id || action.task_id;
    
    const updateData: any = {
      status: action.new_status,
      updated_at: new Date().toISOString()
    };

    if (action.new_status === 'completed') {
      updateData.completed_at = new Date().toISOString();
    }

    const { data, error } = await supabaseAdmin
      .from('project_tasks')
      .update(updateData)
      .eq('id', taskId)
      .select()
      .single();

    if (error) throw error;
    return { type: 'task_updated', task: data };
  }

  /**
   * Send client email action
   */
  private async sendClientEmailAction(action: any, triggerData: any, rule: AutomationRule): Promise<any> {
    // Get project and client information
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('client_name, client_email')
      .eq('id', rule.project_id || triggerData.project_id)
      .single();

    if (projectError || !project.client_email) {
      throw new Error('Client email not found');
    }

    // Prepare email content
    const emailData = {
      user_id: rule.user_id,
      project_id: rule.project_id || triggerData.project_id,
      task_id: triggerData.task_id || null,
      automation_rule_id: rule.id,
      recipient_email: project.client_email,
      recipient_name: project.client_name,
      subject: this.interpolateTemplate(action.subject, { ...triggerData, client_name: project.client_name }),
      content: this.interpolateTemplate(action.content, { ...triggerData, client_name: project.client_name }),
      communication_type: 'email',
      scheduled_for: action.delay ? addHours(new Date(), action.delay) : new Date(),
      status: 'scheduled'
    };

    const { data, error } = await supabaseAdmin
      .from('scheduled_communications')
      .insert(emailData)
      .select()
      .single();

    if (error) throw error;
    return { type: 'email_scheduled', communication: data };
  }

  /**
   * Create milestone action
   */
  private async createMilestoneAction(action: any, triggerData: any, rule: AutomationRule): Promise<any> {
    const milestoneData = {
      project_id: rule.project_id || triggerData.project_id,
      user_id: rule.user_id,
      milestone_name: this.interpolateTemplate(action.name, triggerData),
      description: this.interpolateTemplate(action.description, triggerData),
      target_date: this.calculateDueDate(action.target_date, triggerData),
      status: 'pending',
      is_critical: action.is_critical || false,
      associated_tasks: triggerData.task_id ? [triggerData.task_id] : []
    };

    const { data, error } = await supabaseAdmin
      .from('project_milestones')
      .insert(milestoneData)
      .select()
      .single();

    if (error) throw error;
    return { type: 'milestone_created', milestone: data };
  }

  /**
   * Schedule followup action
   */
  private async scheduleFollowupAction(action: any, triggerData: any, rule: AutomationRule): Promise<any> {
    const followupData = {
      user_id: rule.user_id,
      target_type: action.target_type || 'task',
      target_id: triggerData.task_id || triggerData.project_id,
      followup_type: action.followup_type,
      scheduled_date: this.calculateDueDate(action.delay, triggerData),
      custom_message: this.interpolateTemplate(action.message, triggerData),
      status: 'scheduled'
    };

    const { data, error } = await supabaseAdmin
      .from('automated_followups')
      .insert(followupData)
      .select()
      .single();

    if (error) throw error;
    return { type: 'followup_scheduled', followup: data };
  }

  /**
   * Update project status action
   */
  private async updateProjectStatusAction(action: any, triggerData: any, rule: AutomationRule): Promise<any> {
    const { data, error } = await supabaseAdmin
      .from('projects')
      .update({ 
        status: action.new_status,
        updated_at: new Date().toISOString()
      })
      .eq('id', rule.project_id || triggerData.project_id)
      .select()
      .single();

    if (error) throw error;
    return { type: 'project_updated', project: data };
  }

  /**
   * Evaluate trigger conditions
   */
  private evaluateTriggerConditions(rule: AutomationRule, triggerData: any): boolean {
    const conditions = rule.trigger_conditions;
    
    // Simple condition evaluation - can be enhanced with complex logic
    if (conditions.task_status && triggerData.task_status !== conditions.task_status) {
      return false;
    }
    
    if (conditions.priority && triggerData.priority !== conditions.priority) {
      return false;
    }
    
    if (conditions.days_until_deadline) {
      const daysUntil = this.calculateDaysUntilDeadline(triggerData.due_date);
      if (daysUntil !== conditions.days_until_deadline) {
        return false;
      }
    }

    return true;
  }

  /**
   * Interpolate template variables
   */
  private interpolateTemplate(template: string, data: any): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return data[key] || match;
    });
  }

  /**
   * Calculate due date based on relative time
   */
  private calculateDueDate(dateSpec: any, triggerData: any): Date {
    if (typeof dateSpec === 'string') {
      return new Date(dateSpec);
    }
    
    if (dateSpec.relative) {
      const baseDate = triggerData.due_date ? new Date(triggerData.due_date) : new Date();
      return addDays(baseDate, dateSpec.days || 0);
    }
    
    return new Date();
  }

  /**
   * Calculate days until deadline
   */
  private calculateDaysUntilDeadline(dueDate: string): number {
    if (!dueDate) return Infinity;
    
    const deadline = new Date(dueDate);
    const now = new Date();
    const diffTime = deadline.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Log execution start
   */
  private async logExecutionStart(executionId: string, rule: AutomationRule, triggerData: any): Promise<AutomationExecution> {
    const executionData = {
      id: executionId,
      rule_id: rule.id,
      user_id: rule.user_id,
      trigger_data: triggerData,
      execution_status: 'running' as const,
      executed_at: new Date().toISOString()
    };

    const { data, error } = await supabaseAdmin
      .from('automation_executions')
      .insert(executionData)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  /**
   * Log execution completion
   */
  private async logExecutionComplete(executionId: string, resultData: any): Promise<void> {
    const { error } = await supabaseAdmin
      .from('automation_executions')
      .update({
        execution_status: 'completed',
        result_data: resultData
      })
      .eq('id', executionId);

    if (error) throw error;
  }

  /**
   * Log execution failure
   */
  private async logExecutionFailure(executionId: string, errorMessage: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('automation_executions')
      .update({
        execution_status: 'failed',
        error_message: errorMessage
      })
      .eq('id', executionId);

    if (error) console.error('Failed to log execution failure:', error);
  }

  /**
   * Update rule execution count
   */
  private async updateRuleExecutionCount(ruleId: string): Promise<void> {
    const { error } = await supabaseAdmin
      .rpc('increment_rule_execution_count', { rule_id: ruleId });

    if (error) console.error('Failed to update rule execution count:', error);
  }
}
