'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useQuery } from 'react-query';
import { useAuth } from '@/lib/auth';
import { apiHelpers } from '@/lib/api';
import { 
  ArrowLeftIcon,
  CalendarIcon,
  ClockIcon,
  LinkIcon,
  LockClosedIcon,
  UserGroupIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

export default function ScheduleBotPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [formData, setFormData] = useState({
    meeting_url: '',
    meeting_password: '',
    scheduled_at: '',
    platform: 'google-meet',
    title: '',
    project_id: '',
    bot_name: 'KaiNote Bot'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch user's projects for selection
  const { data: projects } = useQuery(
    'projects',
    () => apiHelpers.getProjects(),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      // Validate meeting URL
      if (!formData.meeting_url.startsWith('http')) {
        throw new Error('Please enter a valid meeting URL');
      }

      // Validate scheduled time
      const scheduledTime = new Date(formData.scheduled_at);
      const now = new Date();
      if (scheduledTime <= now) {
        throw new Error('Scheduled time must be in the future');
      }

      // Schedule the bot
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/meeting-bot/schedule`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Failed to schedule bot');
      }

      // Redirect to bot sessions list
      router.push('/meetings/bot/sessions');
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const detectPlatform = (url: string) => {
    if (url.includes('zoom.us')) return 'zoom';
    if (url.includes('meet.google.com')) return 'google-meet';
    if (url.includes('teams.microsoft.com')) return 'microsoft-teams';
    return 'google-meet';
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setFormData(prev => ({
      ...prev,
      meeting_url: url,
      platform: detectPlatform(url)
    }));
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center space-x-4">
            <Link
              href="/meetings"
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Schedule Meeting Bot</h1>
              <p className="text-gray-600">Let our bot join your meeting and transcribe it automatically</p>
            </div>
          </div>

          {/* Info Banner */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <InformationCircleIcon className="h-5 w-5 text-blue-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  How Meeting Bot Works
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <ul className="list-disc list-inside space-y-1">
                    <li>Our bot will join your meeting 2 minutes before the scheduled time</li>
                    <li>It will appear as "{formData.bot_name}" with camera and microphone muted</li>
                    <li>Real-time transcription will be processed and saved to your account</li>
                    <li>Action items and summaries will be generated automatically</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Meeting Details</h2>
              
              {error && (
                <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-red-800">{error}</p>
                    </div>
                  </div>
                </div>
              )}
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Meeting URL */}
                <div className="md:col-span-2">
                  <label htmlFor="meeting_url" className="block text-sm font-medium text-gray-700 mb-2">
                    <LinkIcon className="h-4 w-4 inline mr-1" />
                    Meeting URL *
                  </label>
                  <input
                    type="url"
                    id="meeting_url"
                    name="meeting_url"
                    value={formData.meeting_url}
                    onChange={handleUrlChange}
                    required
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="https://meet.google.com/abc-defg-hij or https://zoom.us/j/123456789"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Supported: Google Meet, Zoom, Microsoft Teams
                  </p>
                </div>

                {/* Meeting Title */}
                <div className="md:col-span-2">
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                    Meeting Title
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="e.g., Weekly Client Check-in"
                  />
                </div>

                {/* Date & Time */}
                <div>
                  <label htmlFor="scheduled_at" className="block text-sm font-medium text-gray-700 mb-2">
                    <CalendarIcon className="h-4 w-4 inline mr-1" />
                    Meeting Date & Time *
                  </label>
                  <input
                    type="datetime-local"
                    id="scheduled_at"
                    name="scheduled_at"
                    value={formData.scheduled_at}
                    onChange={handleChange}
                    required
                    min={new Date().toISOString().slice(0, 16)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>

                {/* Platform */}
                <div>
                  <label htmlFor="platform" className="block text-sm font-medium text-gray-700 mb-2">
                    Platform
                  </label>
                  <select
                    id="platform"
                    name="platform"
                    value={formData.platform}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="google-meet">Google Meet</option>
                    <option value="zoom">Zoom</option>
                    <option value="microsoft-teams">Microsoft Teams</option>
                  </select>
                </div>

                {/* Meeting Password */}
                <div>
                  <label htmlFor="meeting_password" className="block text-sm font-medium text-gray-700 mb-2">
                    <LockClosedIcon className="h-4 w-4 inline mr-1" />
                    Meeting Password
                  </label>
                  <input
                    type="password"
                    id="meeting_password"
                    name="meeting_password"
                    value={formData.meeting_password}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Optional"
                  />
                </div>

                {/* Project */}
                <div>
                  <label htmlFor="project_id" className="block text-sm font-medium text-gray-700 mb-2">
                    Project
                  </label>
                  <select
                    id="project_id"
                    name="project_id"
                    value={formData.project_id}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="">Select a project (optional)</option>
                    {projects?.map((project: any) => (
                      <option key={project.id} value={project.id}>
                        {project.name} - {project.client_name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Bot Name */}
                <div className="md:col-span-2">
                  <label htmlFor="bot_name" className="block text-sm font-medium text-gray-700 mb-2">
                    <UserGroupIcon className="h-4 w-4 inline mr-1" />
                    Bot Display Name
                  </label>
                  <input
                    type="text"
                    id="bot_name"
                    name="bot_name"
                    value={formData.bot_name}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="KaiNote Bot"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    This name will appear to other meeting participants
                  </p>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3">
              <Link
                href="/meetings"
                className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-primary-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {isSubmitting ? 'Scheduling Bot...' : 'Schedule Bot'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
