'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  CheckIcon,
  XMarkIcon,
  MicrophoneIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  BoltIcon,
  StarIcon,
} from '@heroicons/react/24/outline';

const plans = [
  {
    name: 'Starter',
    price: 29,
    period: 'month',
    description: 'Perfect for new freelancers getting started',
    features: [
      '1,500 transcription minutes/month',
      'Basic meeting recording',
      'AI-powered summaries',
      'Project management',
      'Time tracking',
      'Expense tracking',
      'Basic invoicing',
      'Client portals',
      'Email support',
    ],
    limitations: [
      'No live transcription',
      'Basic automation only',
      'Standard templates',
    ],
    cta: 'Start Free Trial',
    popular: false,
    color: 'blue',
  },
  {
    name: 'Professional',
    price: 49,
    period: 'month',
    description: 'For established freelancers who need advanced features',
    features: [
      '3,000 transcription minutes/month',
      '🔴 Live real-time transcription',
      'Advanced meeting recording',
      'AI-powered summaries & action items',
      'Advanced project management',
      'Smart time tracking & calendar',
      'Advanced expense management',
      'Professional invoicing with Stripe',
      'Advanced client portals',
      'Portfolio website generation',
      'Workflow automation',
      'Smart scheduling',
      'AI document generation',
      'Priority support',
    ],
    limitations: [],
    cta: 'Start Free Trial',
    popular: true,
    color: 'purple',
  },
  {
    name: 'Enterprise',
    price: 99,
    period: 'month',
    description: 'For agencies and teams with advanced needs',
    features: [
      'Unlimited transcription minutes',
      '🔴 Live real-time transcription',
      'Advanced meeting recording',
      'AI-powered summaries & action items',
      'Team project management',
      'Advanced time tracking & calendar',
      'Advanced expense management',
      'Professional invoicing with Stripe',
      'White-label client portals',
      'Custom portfolio websites',
      'Advanced workflow automation',
      'Smart scheduling with AI optimization',
      'AI document generation',
      'Team collaboration tools',
      'Custom integrations',
      'Dedicated support',
    ],
    limitations: [],
    cta: 'Contact Sales',
    popular: false,
    color: 'green',
  },
];

const faqs = [
  {
    question: 'What is live transcription?',
    answer: 'Live transcription provides real-time speech-to-text conversion during your meetings. You can see the transcription appear as you speak with just 1-2 seconds delay, making it perfect for taking notes and tracking action items in real-time.',
  },
  {
    question: 'How accurate is the transcription?',
    answer: 'Our transcription uses OpenAI\'s Whisper API, which provides industry-leading accuracy of 95%+ for clear audio. Live transcription includes confidence scoring so you can see the reliability of each segment.',
  },
  {
    question: 'Can I upgrade or downgrade my plan?',
    answer: 'Yes, you can change your plan at any time. Upgrades take effect immediately, while downgrades take effect at the next billing cycle. Unused minutes don\'t roll over between months.',
  },
  {
    question: 'What happens if I exceed my transcription minutes?',
    answer: 'If you exceed your monthly limit, transcription will be temporarily disabled until the next billing cycle. You can upgrade your plan at any time to get more minutes immediately.',
  },
  {
    question: 'Is there a free trial?',
    answer: 'Yes! All paid plans come with a 14-day free trial. No credit card required to start. You get full access to all features during the trial period.',
  },
  {
    question: 'Do you offer refunds?',
    answer: 'We offer a 30-day money-back guarantee. If you\'re not satisfied with KaiNote, contact us within 30 days for a full refund.',
  },
];

export default function PricingPage() {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');

  const getPrice = (basePrice: number) => {
    return billingPeriod === 'yearly' ? Math.round(basePrice * 0.8) : basePrice;
  };

  const getPlanColor = (color: string) => {
    const colors = {
      blue: 'border-blue-500 bg-blue-50',
      purple: 'border-purple-500 bg-purple-50',
      green: 'border-green-500 bg-green-50',
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const getButtonColor = (color: string) => {
    const colors = {
      blue: 'bg-blue-600 hover:bg-blue-700',
      purple: 'bg-purple-600 hover:bg-purple-700',
      green: 'bg-green-600 hover:bg-green-700',
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-primary-600">KaiNote</h1>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/auth/login"
                className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                Sign in
              </Link>
              <Link
                href="/auth/register"
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Header */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Simple, Transparent Pricing
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Choose the perfect plan for your freelance business. All plans include core features 
              with advanced capabilities available on higher tiers.
            </p>

            {/* Billing Toggle */}
            <div className="flex items-center justify-center space-x-4 mb-8">
              <span className={`text-sm ${billingPeriod === 'monthly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
                Monthly
              </span>
              <button
                onClick={() => setBillingPeriod(billingPeriod === 'monthly' ? 'yearly' : 'monthly')}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  billingPeriod === 'yearly' ? 'bg-primary-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    billingPeriod === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className={`text-sm ${billingPeriod === 'yearly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
                Yearly
              </span>
              {billingPeriod === 'yearly' && (
                <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  Save 20%
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan) => (
            <div
              key={plan.name}
              className={`relative bg-white rounded-2xl shadow-lg ${
                plan.popular ? `ring-2 ${getPlanColor(plan.color)}` : 'border border-gray-200'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                    <StarIcon className="h-4 w-4" />
                    <span>Most Popular</span>
                  </div>
                </div>
              )}

              <div className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <p className="text-gray-600 mb-6">{plan.description}</p>

                <div className="mb-6">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold text-gray-900">${getPrice(plan.price)}</span>
                    <span className="text-gray-500 ml-2">/{billingPeriod === 'yearly' ? 'month' : 'month'}</span>
                  </div>
                  {billingPeriod === 'yearly' && (
                    <p className="text-sm text-gray-500 mt-1">
                      Billed annually (${getPrice(plan.price) * 12}/year)
                    </p>
                  )}
                </div>

                <Link
                  href="/auth/register"
                  className={`w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white ${getButtonColor(plan.color)} transition-colors mb-6`}
                >
                  {plan.cta}
                </Link>

                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-gray-900 uppercase tracking-wide">
                    What's included:
                  </h4>
                  <ul className="space-y-3">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <CheckIcon className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {plan.limitations.length > 0 && (
                    <>
                      <h4 className="text-sm font-medium text-gray-900 uppercase tracking-wide mt-6">
                        Not included:
                      </h4>
                      <ul className="space-y-3">
                        {plan.limitations.map((limitation, index) => (
                          <li key={index} className="flex items-start">
                            <XMarkIcon className="h-5 w-5 text-gray-400 mt-0.5 mr-3 flex-shrink-0" />
                            <span className="text-sm text-gray-500">{limitation}</span>
                          </li>
                        ))}
                      </ul>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Feature Comparison */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why Choose KaiNote?
            </h2>
            <p className="text-xl text-gray-600">
              The only platform that combines meeting transcription with complete business management
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-red-100 rounded-lg p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                <MicrophoneIcon className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Live Transcription</h3>
              <p className="text-gray-600 text-sm">
                Real-time transcription with 1-2 second latency using advanced AI
              </p>
            </div>

            <div className="text-center">
              <div className="bg-blue-100 rounded-lg p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                <ClockIcon className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Smart Time Tracking</h3>
              <p className="text-gray-600 text-sm">
                Integrated time tracking with task estimates and calendar scheduling
              </p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 rounded-lg p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Financial Management</h3>
              <p className="text-gray-600 text-sm">
                Complete expense tracking, invoicing, and financial analytics
              </p>
            </div>

            <div className="text-center">
              <div className="bg-purple-100 rounded-lg p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                <BoltIcon className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Workflow Automation</h3>
              <p className="text-gray-600 text-sm">
                AI-powered automation for client communication and scheduling
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="bg-gray-50">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
          </div>

          <div className="space-y-8">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {faq.question}
                </h3>
                <p className="text-gray-600">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-primary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Transform Your Freelance Business?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Start your free trial today. No credit card required.
            </p>
            <Link
              href="/auth/register"
              className="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50"
            >
              Start Free Trial
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
