# KaiNote Deployment Guide

This guide covers deploying KaiNote to various platforms including Vercel, Railway, AWS, and self-hosted environments.

## 🚀 Quick Deploy Options

### Option 1: Vercel + Railway (Recommended)
- **Frontend**: Deploy to Vercel
- **Backend**: Deploy to Railway
- **Database**: Use Railway PostgreSQL or Supabase

### Option 2: Full AWS Deployment
- **Frontend**: AWS Amplify or S3 + CloudFront
- **Backend**: AWS ECS or Lambda
- **Database**: AWS RDS PostgreSQL

### Option 3: Self-Hosted
- **Server**: Ubuntu/CentOS with Docker
- **Database**: PostgreSQL
- **Reverse Proxy**: Nginx

## 📋 Prerequisites

- Node.js 18+
- PostgreSQL database or Supabase account
- OpenAI API key
- Domain name (optional)

## 🔧 Environment Variables

### Frontend (.env.local)
```bash
NEXT_PUBLIC_API_URL=https://your-api-domain.com
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Backend (.env)
```bash
NODE_ENV=production
PORT=3003
DATABASE_URL=********************************/kainote
JWT_SECRET=your_super_secret_jwt_key
OPENAI_API_KEY=your_openai_api_key
FRONTEND_URL=https://your-frontend-domain.com
```

## 🌐 Vercel + Railway Deployment

### Step 1: Deploy Backend to Railway

1. **Create Railway Account**
   ```bash
   npm install -g @railway/cli
   railway login
   ```

2. **Initialize Railway Project**
   ```bash
   cd backend
   railway init
   railway add postgresql
   ```

3. **Set Environment Variables**
   ```bash
   railway variables set NODE_ENV=production
   railway variables set JWT_SECRET=your_secret_key
   railway variables set OPENAI_API_KEY=your_openai_key
   ```

4. **Deploy**
   ```bash
   railway up
   ```

### Step 2: Deploy Frontend to Vercel

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Deploy Frontend**
   ```bash
   cd frontend
   vercel --prod
   ```

3. **Set Environment Variables in Vercel Dashboard**
   - `NEXT_PUBLIC_API_URL`: Your Railway backend URL
   - `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase URL
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anon key

## 🐳 Docker Deployment

### Docker Compose Setup

Create `docker-compose.yml`:

```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3001:3001"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:3003
    depends_on:
      - backend

  backend:
    build: ./backend
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************/kainote
      - JWT_SECRET=your_secret_key
      - OPENAI_API_KEY=your_openai_key
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=kainote
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

### Deploy with Docker

```bash
# Build and start services
docker-compose up -d

# Run database migrations
docker-compose exec backend npm run db:migrate

# Seed demo data
docker-compose exec backend npm run db:seed
```

## ☁️ AWS Deployment

### Frontend (AWS Amplify)

1. **Connect Repository**
   - Go to AWS Amplify Console
   - Connect your GitHub repository
   - Select the `frontend` folder as build root

2. **Build Settings**
   ```yaml
   version: 1
   frontend:
     phases:
       preBuild:
         commands:
           - npm ci
       build:
         commands:
           - npm run build
     artifacts:
       baseDirectory: .next
       files:
         - '**/*'
     cache:
       paths:
         - node_modules/**/*
   ```

### Backend (AWS ECS)

1. **Create ECR Repository**
   ```bash
   aws ecr create-repository --repository-name kainote-backend
   ```

2. **Build and Push Docker Image**
   ```bash
   cd backend
   docker build -t kainote-backend .
   docker tag kainote-backend:latest 123456789.dkr.ecr.region.amazonaws.com/kainote-backend:latest
   docker push 123456789.dkr.ecr.region.amazonaws.com/kainote-backend:latest
   ```

3. **Create ECS Service**
   - Use AWS Console or Terraform
   - Configure load balancer
   - Set environment variables

### Database (AWS RDS)

1. **Create PostgreSQL Instance**
   ```bash
   aws rds create-db-instance \
     --db-instance-identifier kainote-db \
     --db-instance-class db.t3.micro \
     --engine postgres \
     --master-username postgres \
     --master-user-password your_password \
     --allocated-storage 20
   ```

## 🔒 Security Considerations

### SSL/TLS
- Use HTTPS for all environments
- Configure SSL certificates (Let's Encrypt for self-hosted)

### Environment Variables
- Never commit secrets to version control
- Use secure secret management (AWS Secrets Manager, etc.)

### Database Security
- Use connection pooling
- Enable SSL for database connections
- Regular backups

### API Security
- Rate limiting enabled
- CORS properly configured
- Input validation and sanitization

## 📊 Monitoring and Logging

### Application Monitoring
- Use Winston for structured logging
- Monitor API response times
- Set up error tracking (Sentry)

### Infrastructure Monitoring
- Monitor server resources
- Database performance
- API endpoint health checks

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Railway
        run: |
          npm install -g @railway/cli
          railway login --token ${{ secrets.RAILWAY_TOKEN }}
          railway up --service backend

  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Vercel
        run: |
          npm install -g vercel
          vercel --token ${{ secrets.VERCEL_TOKEN }} --prod
```

## 🆘 Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check DATABASE_URL format
   - Verify network connectivity
   - Check firewall settings

2. **CORS Errors**
   - Verify FRONTEND_URL in backend .env
   - Check CORS configuration

3. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Check TypeScript compilation errors

### Health Checks

- Backend: `GET /health`
- Database: Check connection in logs
- Frontend: Verify page loads correctly

## 📈 Scaling Considerations

### Horizontal Scaling
- Use load balancers
- Implement session management
- Database read replicas

### Performance Optimization
- Enable caching (Redis)
- CDN for static assets
- Database query optimization

### Cost Optimization
- Use auto-scaling groups
- Monitor resource usage
- Optimize database queries
